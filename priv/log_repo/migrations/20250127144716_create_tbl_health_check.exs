defmodule Logs.LogRepo.Migrations.CreateTblHealthCheck do
  use Ecto.Migration

  def change do
    create table(:tbl_health_check) do
      add :endpoint, :string, size: 600
      add :name, :string, size: 200
      add :last_seen, :naive_datetime
      add :status, :integer
      add :endpoint_status, :integer

      timestamps()
    end

    create unique_index(:tbl_health_check, [:endpoint, :name])
  end

end

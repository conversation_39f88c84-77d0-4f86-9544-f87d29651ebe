defmodule Logs.LogRepo.Migrations.CreateLogsApi do
  use Ecto.Migration

  def change do
    create table(:logs_api) do
      add :reference, :string, size: 30
      add :ip_address, :string, size: 30
      add :endpoint, :string, size: 300
      add :external_reference, :string, size: 200
      add :service, :string, size: 150, null: false
      add :request, :string, size: 10000, null: false
      add :response, :string, size: 600_000, null: true
      add :caller_id, :integer
      add :caller_type, :string, size: 30

      timestamps()
    end
  end
end

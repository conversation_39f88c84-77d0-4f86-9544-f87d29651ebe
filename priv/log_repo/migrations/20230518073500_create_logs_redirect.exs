defmodule Logs.LogRepo.Migrations.CreateLogsRedirect do
  use Ecto.Migration

  def change do
    create table(:logs_redirect) do
      add :device_uuid, :string, null: true
      add :reference, :string, size: 30
      add :ip_address, :string, size: 50, null: false
      add :service, :string, size: 50, null: false
      add :request, :string, size: 4000, null: false
      add :response, :string, size: 4000, null: true
      add :full_browser_name, :string, size: 200, null: false
      add :system_platform_name, :string, size: 300, null: false
      add :known_browser, :boolean, default: false, null: false

      timestamps()
    end
  end
end

defmodule Logs.LogRepo.Migrations.CreateLogsSession do
  use Ecto.Migration

  def change do
    create table(:logs_session) do
      add :session_id, :string, size: 400, null: false
      add :portal, :string, size: 50, null: false
      add :description, :string, size: 300, null: false
      add :device_uuid, :string, null: true
      add :ip_address, :string, size: 50, null: true
      add :full_browser_name, :string, size: 200, null: true
      add :browser_details, :string, size: 200, null: true
      add :system_platform_name, :string, size: 300, null: true
      add :device_type, :string, size: 150, null: true
      add :known_browser, :boolean, default: false, null: false
      add :status, :boolean, default: false, null: false
      add :log_out_at, :naive_datetime
      add :log_in_at, :naive_datetime
      add :user_id, :integer

      timestamps()
    end

    create index(:logs_session, [:user_id])
  end
end

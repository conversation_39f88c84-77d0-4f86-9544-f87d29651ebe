defmodule App.Repo.Migrations.AddLicenseDataTable do
  use Ecto.Migration

  def change do
    create table(:license_fields) do
      add :field_name, :string
      add :field_type, :string
      add :reason, :string
      add :field_label, :string
      add :field_options, {:array, :string}, default: []
      add :field_dependents, {:array, :string}, default: []
      add :field_validations, :map
      add :attention_field, :string
      add :attention_status, :string
      add :field_description, :string, size: 200
      add :dependent_selection, :string
      add :required, :boolean, default: true, null: false

      timestamps()
    end

    create table(:license_data) do
      add :license_field_id, references(:license_fields, on_delete: :nothing)
      add :license_id, references(:licenses, on_delete: :nothing)
      add :status, :integer, default: 1

      timestamps()
    end

    create table(:user_license_mapping) do
      add :status, :integer, default: 0
      add :approval_status, :boolean, default: false
      add :is_replaced, :boolean, default: false
      add :to_reprint, :boolean, default: false
      add :approved, :boolean, default: false
      add :data, :map, default: %{}
      add :record_name, :string
      add :meeting_number, :string
      add :reprint_reason, :string
      add :replace_reason, :string
      add :reprint_comments, :string
      add :replace_comments, :string
      add :reason, :string
      add :file_resolution, :text
      add :comments, :string
      add :summary_user_draft, :text
      add :count_down_start_date, :date
      add :count_down_days, :integer, default: 90

      add :associated_license_id, references(:licenses, on_delete: :nothing)

      add :license_id, references(:licenses, on_delete: :nothing)
      add :user_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create(index(:license_data, [:license_field_id, :license_id]))
    create(index(:user_license_mapping, [:license_id, :user_id, :associated_license_id]))
  end
end

defmodule App.Repo.Migrations.CreateUsersAuthTables do
  use Ecto.Migration

  def change do
    create table(:tbl_users) do
      add(:email, :string, null: false, size: 50)
      add(:first_name, :string, size: 40)
      add(:last_name, :string, size: 40)
      add(:mobile, :string, size: 15)
      add(:password_hash, :string, null: false)
      add(:status, :string, default: "A")
      add(:user_role, :integer, default: 2)
      add(:user_type, :string, default: "STAFF")
      add(:registration_type, :string, default: "INDIVIDUAL")
      add(:auto_password, :string, default: "Y")

      add(:remote_ip, :string, size: 30)
      add(:sex, :string, size: 30)
      add(:last_logon, :naive_datetime)
      add(:login_attempts, :integer, default: 0)

      add(:blocked, :boolean, default: false)
      add(:deleted_at, :naive_datetime)

      timestamps()
    end

    create(unique_index(:tbl_users, [:email]))
    create(index(:tbl_users, [:password_hash]))

    create table(:tbl_users_tokens) do
      add(:user_id, references(:tbl_users, on_delete: :delete_all), null: false)
      add(:token, :binary, null: false)
      add(:context, :string, null: false)
      add(:sent_to, :string)
      add(:login_timestamp, :naive_datetime)
      add(:ip_address, :string, size: 50, null: true)
      add(:device_type, :string, size: 50, null: true)
      add(:browser, :string, size: 50, null: true)
      add(:os, :string, size: 50, null: true)
      add(:deleted_at, :naive_datetime)

      timestamps(updated_at: false)
    end

    create(index(:tbl_users_tokens, [:user_id]))
    create(unique_index(:tbl_users_tokens, [:context, :token]))
  end
end

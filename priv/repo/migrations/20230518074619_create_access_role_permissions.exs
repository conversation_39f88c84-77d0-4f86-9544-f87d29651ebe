defmodule App.Repo.Migrations.CreateAccessRolePermissions do
  use Ecto.Migration

  def change do
    create table(:access_role_permissions) do
      add :access_role_id, references(:access_roles, on_delete: :delete_all), null: false
      add :permission_id, references(:permissions, on_delete: :delete_all), null: false

      timestamps()
    end

    create unique_index(:access_role_permissions, [:access_role_id, :permission_id])
    create index(:access_role_permissions, [:access_role_id])
    create index(:access_role_permissions, [:permission_id])
  end
end

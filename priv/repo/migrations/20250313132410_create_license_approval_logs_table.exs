defmodule App.Repo.Migrations.CreateLicenseApprovalLogsTable do
  use Ecto.Migration

  def change do
    create table(:license_approval_logs) do
      add :license_mapping_id, references(:user_license_mapping, on_delete: :nothing), null: false
      add :user_id, references(:tbl_users, on_delete: :nothing), null: false
      add :is_approved, :boolean, default: false
      add :status, :integer, default: 0

      timestamps(type: :utc_datetime)
    end

    create index(:license_approval_logs, [:license_mapping_id, :user_id])
  end
end

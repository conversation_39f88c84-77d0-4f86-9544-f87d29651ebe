defmodule App.Repo.Migrations.CreateLicenseReviews do
  use Ecto.Migration

  def change do
    create table(:license_reviews) do
      add :reason, :string
      add :field_id, :integer
      add :user_id, :integer
      add :license_id, :integer
      add :associated_id, :integer
      add :attention_status, :string, default: "submitted"
      add :attention_field, :string

      timestamps(type: :utc_datetime)
    end
  end
end

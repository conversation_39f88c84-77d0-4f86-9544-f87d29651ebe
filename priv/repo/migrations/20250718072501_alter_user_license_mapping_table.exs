defmodule App.Repo.Migrations.AlterUserLicenseMappingTable do
  use Ecto.Migration

  def change do
    alter table(:user_license_mapping) do
      remove :associated_license_id
    end

    alter table(:user_license_mapping) do
      add :associated_license_id, references(:user_license_mapping, on_delete: :nothing)
      add :initiator_id, references(:tbl_users, on_delete: :nothing)

      add :show_evaluators, :boolean, default: false
      add :is_resubmitted, :boolean, default: false
    end

    create index(:user_license_mapping, [:associated_license_id])
  end
end

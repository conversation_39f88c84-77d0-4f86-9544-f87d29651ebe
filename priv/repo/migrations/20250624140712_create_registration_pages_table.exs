defmodule App.Repo.Migrations.CreateRegistrationPagesTable do
  use Ecto.Migration

  def change do
    create table(:registration_pages) do
      add :name, :string, size: 100
      add :url, :string, size: 100
      add :icon, :string, size: 100
      add :status, :integer, default: 1
      add :license_id, references(:licenses, on_delete: :nothing)

      timestamps(type: :utc_datetime)
    end

    alter table(:user_license_mapping) do
      add :registration_page_id, references(:registration_pages, on_delete: :nothing)
    end
    create unique_index(:registration_pages, [:license_id])
  end
end

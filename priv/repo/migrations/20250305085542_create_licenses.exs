defmodule App.Repo.Migrations.CreateLicenses do
  use Ecto.Migration

  def change do
    create table(:licenses) do
      add :name, :string
      add :type, :string, default: "INDIVIDUAL"
      add :form_number, :integer
      add :section, :integer
      add :security_act_no, :integer
      add :note, :string, size: 1000
      add :icon, :string
      add :color, :string
      add :role_id, :integer
      add :status, :string
      add :reason, :string
      add :count_down_days, :integer, default: 90
      add :primary_key, :string
      add :require_license, :boolean, default: false
      add :file_path, :string
      add :amount, :decimal, default: 0

      timestamps(type: :utc_datetime)
    end

    alter table(:licenses) do
      add :associated_license_id, references(:licenses, on_delete: :nothing)
    end
  end
end

defmodule App.Repo.Migrations.CreateApprovalLevelTable do
  use Ecto.Migration

  def change do
    create table(:approval_levels) do
      add :count_down, :integer
      add :condition_tracking, :integer
      add :genarate_summary, :integer
      add :status, :integer, default: 0
      add :approval_status, :integer, default: 1

      add :department_id, references(:department_roles, on_delete: :nothing), null: false
      add :role_id, references(:access_roles, on_delete: :nothing), null: false

      timestamps(type: :utc_datetime)
    end

    create index(:approval_levels, [:department_id, :role_id])
  end
end

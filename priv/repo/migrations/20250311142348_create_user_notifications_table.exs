defmodule App.Repo.Migrations.CreateUserNotificationsTable do
  use Ecto.Migration

  def change do
    create table(:user_notifications) do
      add :message, :text
      add :reference, :string
      add :page_url, :string, size: 225
      add :type, :string, size: 225
      add :status, :boolean, default: false
      add :reason, :string

      add :read_id, {:array, :integer}, default: []

      add :user_id, references(:tbl_users, on_delete: :nothing)
      add :role_id, references(:department_roles, on_delete: :nothing), null: false
      timestamps(type: :utc_datetime)
    end

    create index(:user_notifications, [:user_id, :role_id])
  end
end

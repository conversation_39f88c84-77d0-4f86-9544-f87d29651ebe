defmodule App.Repo.Migrations.CreateLicenseCertificateTable do
  use Ecto.Migration

  def change do
    create table(:licence_certificates) do
      add :name, :string
      add :description, :string
      add :status, :integer, default: 1
      add :template, :text

      timestamps()
    end

    alter table(:licenses) do
      add :certificate_id, references(:licence_certificates, on_delete: :nothing)
      add :other_fees, :decimal, default: 0
    end

    create unique_index(:licence_certificates, [:name])
    create(index(:licenses, [:certificate_id]))
  end
end

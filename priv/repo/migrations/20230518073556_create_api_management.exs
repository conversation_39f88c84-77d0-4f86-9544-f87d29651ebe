defmodule App.Repo.Migrations.CreateApiManagement do
  use Ecto.Migration

  def change do
    create table(:api_management) do
      add :name, :string, size: 40
      add :type, :string, size: 40
      add :base_url, :string, size: 225
      add :key, :string, size: 300
      add :access_point, :string, size: 40, default: "PROD"
      add :status, :integer, default: 1, null: false
      add :data, :map, default: "{}"
      add :deleted_at, :naive_datetime
      add :updated_by, references(:tbl_users, on_delete: :nothing)
      add :created_by, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create unique_index(:api_management, [:name, :type])
    create index(:api_management, [:updated_by])
    create index(:api_management, [:created_by])
  end
end

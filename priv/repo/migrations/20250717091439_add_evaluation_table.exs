defmodule App.Repo.Migrations.AddEvaluationTable do
  use Ecto.Migration

  def change do
    create table(:evaluation_criteria) do
      add :name, :string
      add :type, :string
      add :section, :integer, default: 1
      add :description, :text
      add :status, :integer, default: 1

      timestamps()
    end

    create table(:sandbox_cohorts) do
      add :name, :string

      add :application_id, references(:user_license_mapping, on_delete: :nothing)

      add :status, :integer, default: 1

      timestamps()
    end

    create table(:application_evaluators) do
      add :application_id, references(:user_license_mapping, on_delete: :nothing)

      add :cohort_id, references(:sandbox_cohorts, on_delete: :nothing)

      add :evaluator_id, references(:tbl_users, on_delete: :nothing)
      add :comments, :string
      add :status, :boolean, default: false
      add :enable_edits, :boolean, default: true

      timestamps()
    end

    create table(:evaluations) do
      add :comments, :string
      add :criteria_id, references(:evaluation_criteria, on_delete: :nothing)
      add :application_id, references(:user_license_mapping, on_delete: :nothing)
      add :evaluator_id, references(:tbl_users, on_delete: :nothing)

      add :rating, :decimal
      add :status, :integer, default: 0

      timestamps()
    end

    create table(:sandbox_scores) do
      add :section_score, :decimal
      add :max_score, :decimal
      add :percentage_score, :decimal
      add :grand_total, :decimal
      add :max_grand_total, :decimal
      add :grand_percentage_score, :decimal

      add :decision, :string

      add :evaluation_id, references(:evaluations, on_delete: :nothing)
      add :application_id, references(:user_license_mapping, on_delete: :nothing)
      add :comments, :string

      add :status, :integer, default: 1

      timestamps()
    end

    create index(:sandbox_cohorts, [:application_id])
    create index(:application_evaluators, [:cohort_id])
    create index(:evaluations, [:criteria_id])
    create index(:evaluations, [:application_id])
    create index(:evaluations, [:evaluator_id])
    create index(:sandbox_scores, [:evaluation_id])
    create index(:sandbox_scores, [:application_id])
  end
end

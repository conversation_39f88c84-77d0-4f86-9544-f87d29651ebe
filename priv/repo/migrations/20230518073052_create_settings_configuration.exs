defmodule App.Repo.Migrations.CreateSettingsConfiguration do
  use Ecto.Migration

  def change do
    create table(:settings_configuration) do
      add :name, :string, size: 100
      add :value, :string, size: 100
      add :value_type, :string, size: 100
      add :regex, :string, size: 300
      add :field_type, :string, size: 100
      add :select_details, :string, size: 300
      add :description, :string, size: 300
      add :deleted_at, :naive_datetime
      add :updated_by, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create index(:settings_configuration, [:updated_by])
    create unique_index(:settings_configuration, [:name])
  end
end

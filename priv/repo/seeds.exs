# Script for populating the database. You can run it as:
#
#     mix run priv/repo/seeds.exs
#
# Inside the script, you can read and write to any of your
# repositories directly:
#
#     App.Repo.insert!(%App.SomeSchema{})
#
# We recommend using the bang functions (`insert!`, `update!`
# and so on) as they will fail if something goes wrong.

alias App.SetUp.{
  ApiManagement,
  Permissions,
  MessageDrafts,
  Roles,
  Users,
  Setting,
  AdminContacts,
  Licenses,
  LicenceDrafts,
  SummaryDrafts,
  LicenseCategories,
  ApprovalLevels,
  Committees,
  Certificates,
  Conditions,
  RegPages,
  Evaluation
}

Permissions.init()

MessageDrafts.init()

ApiManagement.init()

Setting.init()
Setting.init1()

Committees.init()

Roles.init()
|> Users.init()
|> Certificates.init()
|> LicenseCategories.init()
|> Licenses.init()
|> LicenceDrafts.init()
|> SummaryDrafts.init()
|> RegPages.init()

ApprovalLevels.init()

AdminContacts.init()
Conditions.init()
Evaluation.init()

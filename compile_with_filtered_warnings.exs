#!/usr/bin/env elixir

# Script to compile with warnings as errors, excluding PdfGenerator warnings

defmodule CompileWithFilteredWarnings do
  def run do
    # Run mix compile --force and capture output
    {output, _exit_code} = System.cmd("mix", ["compile", "--force"], stderr_to_stdout: true)

    # Split output into lines
    lines = String.split(output, "\n")

    # Filter out PdfGenerator and Gettext warnings
    filtered_lines =
      Enum.reject(lines, fn line ->
        String.contains?(line, "PdfGenerator.generate_binary!") or
        String.contains?(line, "Gettext") or
        String.contains?(line, "gettext")
      end)

    # Check if there are any warnings left (excluding PdfGenerator)
    warning_lines =
      Enum.filter(filtered_lines, fn line ->
        String.starts_with?(line, "warning:")
      end)

    # Print all output
    IO.puts(Enum.join(filtered_lines, "\n"))

    # Exit with error code if there are non-PdfGenerator warnings
    if length(warning_lines) > 0 do
      IO.puts("\n=== COMPILATION FAILED ===")
      IO.puts("Found #{length(warning_lines)} warnings (excluding PdfGenerator warnings)")
      IO.puts("All warnings except PdfGenerator.generate_binary! and Gettext must be fixed")
      System.halt(1)
    else
      IO.puts("\n=== COMPILATION SUCCESSFUL ===")
      IO.puts("No warnings found (PdfGenerator and Gettext warnings excluded)")
      System.halt(0)
    end
  end
end

CompileWithFilteredWarnings.run()

// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"
import "./channels/channels.js"
import "phoenix_html"
// Establish Phoenix Socket and LiveView configuration.
import {Socket} from "phoenix"
import {LiveSocket} from "phoenix_live_view"
import topbar from "../vendor/topbar"
import Alpine from "../vendor/alpine"
import collapse from '../vendor/alpine_collapse'
import focus from '../vendor/alpine_focus'
import hooks from "./hooks"
Alpine.plugin(collapse)
Alpine.plugin(focus)
window.Alpine = Alpine
Alpine.start()

let csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")
let liveSocket = new LiveSocket("/live", Socket, {
  hooks,
  longPollFallbackMs: 2500,
  dom: {
    onBeforeElUpdated(from, to) {
      if (from._x_dataStack) {
        window.Alpine.clone(from, to)
      }
    },
  },
  params: {_csrf_token: csrfToken},

})

// Show progress bar on live navigation and form submits
topbar.config({barColors: {0: "#29d"}, shadowColor: "rgba(0, 0, 0, .3)"})
// window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
// window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

const main = document.querySelector("main");
let loadingSpinner = document.getElementById("main-loader");

window.addEventListener("phx:page-loading-start", info => {
  if (info.detail.kind === "redirect" && (main !== "null" || main !== "undefined")) {
    main.classList.add("phx-page-loading"); // Dim the main content
  }
  if (info.detail.kind === "redirect" && loadingSpinner !== "null" || loadingSpinner !== "undefined") {
    loadingSpinner.classList.remove("hidden"); // Show spinner
    loadingSpinner.classList.add("phx-page-loading"); // Show spinner
  }
  topbar.show(300);
});

window.addEventListener("phx:page-loading-stop", info => {
  if (info.detail.kind === "redirect" && (main !== "null" || main !== "undefined")) {
    main.classList.remove("phx-page-loading");
  }
  if (info.detail.kind === "redirect" && loadingSpinner !== "null" || loadingSpinner !== "undefined") {
    loadingSpinner.classList.remove("phx-page-loading"); // Hide spinner
    loadingSpinner.classList.add("hidden") // Completely hide the spinner after page load
  }
  topbar.hide();
});

window.addEventListener('phx:scroll-to', (e) => {
  const section = document.getElementById(e.detail.section);
  if (section) {
    section.scrollIntoView({ behavior: 'smooth' });
  }
});

// connect if there are any LiveViews on the page
liveSocket.connect()

// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()


window.liveSocket = liveSocket


import Swal from 'sweetalert2';
import {soundControl} from "../hooks/sound";

const DEFAULT_TIMER = 7000;
const DEFAULT_BUTTON_COLOR = '#313133';

/**
 * @param {'success'|'error'|'warning'|'info'} icon 
 * @param {string} message 
 * @param {string} title 
 * @param {'normal'|'toast'} expression 
 * @param {number} timer 
 */
export const sweet_alert = (
    icon = 'success',
    message = '',
    title = '',
    expression = 'normal',
    timer = DEFAULT_TIMER
) => {
    const handlers = {
        normal: () => {
            Swal.fire({
                title,
                text: message,
                icon,
                timer,
                confirmButtonColor: DEFAULT_BUTTON_COLOR,
                customClass: {
                    popup: 'border-radius-2'
                }
            });
        },
        toast: () => {
            console.log('Toast Alert:', {icon, message, timer});
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                iconColor: 'white',
                showConfirmButton: false,
                timer,
                customClass: {
                    popup: 'colored-toast'
                },
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer);
                    toast.addEventListener('mouseleave', Swal.resumeTimer);
                }
            });
            
            Toast.fire({
                icon,
                title: message,
                showClass: {
                    popup: 'animate__animated animate__bounceIn'
                },
                hideClass: {
                    popup: 'animate__animated animate__fadeOut'
                }
            });
            soundControl();
        }
    };

    const handler = handlers[expression];
    if (handler) {
        handler();
    } else {
        console.warn(`Invalid expression: ${expression}`);
    }
};

/**
 * Closes all active SweetAlert2 popups
 */
export const destroyAlert = () => {
    Swal.close();
};

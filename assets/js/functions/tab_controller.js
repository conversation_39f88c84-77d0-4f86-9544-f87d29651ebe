import Swal from 'sweetalert2';

const TAB_CONSTANTS = {
    ACTIVE_TAB: 'pbs_sms_gateway_active_tab',
    TAB_AVAILABLE: 'pbs_sms_gateway_tab_available',
    MESSAGE: 'App is open in another window.',
    FAVICON: {
        ERROR: '/images/favicon-error.png',
        DEFAULT: '/images/logo.svg'
    }
};

class TabController {
    constructor() {
        this.storageHandler = this.handleStorageEvent.bind(this);
        this.initialize();
    }

    initialize() {
        localStorage.setItem(TAB_CONSTANTS.ACTIVE_TAB, Date.now().toString());
        window.addEventListener('storage', this.storageHandler);
    }

    setFavicon(iconPath) {
        try {
            let link = document.querySelector("link[rel~='icon']");
            if (!link) {
                link = document.createElement('link');
                link.rel = 'icon';
                document.getElementsByTagName('head')[0].appendChild(link);
            }
            link.href = iconPath;
        } catch (error) {
            console.error('Error setting favicon:', error);
        }
    }

    async handleStorageEvent(e) {
        if (e.key === TAB_CONSTANTS.ACTIVE_TAB) {
            try {
                localStorage.setItem(TAB_CONSTANTS.TAB_AVAILABLE, Date.now().toString());
                this.setFavicon(TAB_CONSTANTS.FAVICON.ERROR);

                await Swal.fire({
                    text: TAB_CONSTANTS.MESSAGE,
                    confirmButtonColor: '#313133',
                    allowOutsideClick: false,
                    showLoaderOnConfirm: true,
                    backdrop: 'rgba(243,79,36,1) center left no-repeat',
                    customClass: { popup: 'border-radius-2' },
                    preConfirm: () => {
                        return new Promise((resolve) => {
                            setTimeout(() => {
                                localStorage.setItem(TAB_CONSTANTS.ACTIVE_TAB, Date.now().toString());
                                this.setFavicon(TAB_CONSTANTS.FAVICON.DEFAULT);
                                resolve();
                            }, 1000);
                        });
                    }
                });
            } catch (error) {
                console.error('Error handling storage event:', error);
                this.setFavicon(TAB_CONSTANTS.FAVICON.DEFAULT);
            }
        }
    }

    destroy() {
        window.removeEventListener('storage', this.storageHandler);
        localStorage.removeItem(TAB_CONSTANTS.ACTIVE_TAB);
        localStorage.removeItem(TAB_CONSTANTS.TAB_AVAILABLE);
        this.setFavicon(TAB_CONSTANTS.FAVICON.DEFAULT);
    }
}

export default TabController;

import socket from "./user_socket";
import {download_your_files} from "../functions/download";
import {sweet_alert} from "../functions/sweet_alert";

const USER_ID = sessionStorage.userSessionId;
const CHANNELS = {
    START: "user_session:lobby",
    USER: `user_session:${USER_ID}`,
    EXPORTS: `exports:${USER_ID}`,
    SWEET_ALERT: `sweet_alert:${USER_ID}`
};

const handleExports = (channel) => {
    channel.on(`exports:${USER_ID}`, payload => {
        channel.push('export_file', payload)
    });

    channel.on(`export:${USER_ID}:complete`, async ({payload, fileName, extension}) => {
        await download_your_files(payload, fileName, extension);
    });
};

const handleLogout = (channel) => {
    channel.on(`user_log_out:${USER_ID}`, ({status}) => {
        status && window.location.reload();
    });
};

const handleGlobalExport = (channel) => {
    channel.on(`global_export:${USER_ID}:complete`, async ({payload, fileName, extension}) => {
        await download_your_files(payload, fileName, extension);
    });
};

const handleSweetAlert = (channel) => {
    channel.on(`sweet_alert:${USER_ID}`, ({
        icon = 'success',
        message = '',
        title = '',
        expression = 'normal',
        timer = 3000,
        confirm_text = 'Process',
        action_type = '',
        data = {}
    }) => {
        sweet_alert(icon, message, title, expression, timer, confirm_text, action_type, data);
    });
};

const initializeChannels = () => {
    if (!USER_ID || USER_ID === "null" || USER_ID === "undefined") return;

    try {
        socket.connect();
        
        const channels = Object.entries(CHANNELS).map(([key, channel]) => ({
            name: key,
            instance: socket.channel(channel, {})
        }));

        channels.forEach(({name, instance}) => {
            instance.join()
                .receive("ok", () => console.log(`${name} channel joined`))
                .receive("error", resp => console.error(`${name} channel error:`, resp));
        });

        const [, userChannel, exportsChannel, sweetAlertChannel] = channels.map(c => c.instance);

        handleExports(exportsChannel);
        handleLogout(userChannel);
        handleSweetAlert(sweetAlertChannel);
        handleGlobalExport(userChannel);

    } catch (error) {
        console.error('Channel initialization error:', error);
    }
};

initializeChannels();
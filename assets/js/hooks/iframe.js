/** @typedef {import('phoenix_live_view').ViewHook} ViewHook */

export const ResizeIframe = {
    /** @type {number} */
    resizeTimeout: null,
    
    /** @type {Function} */
    resizeHandler: null,

    mounted() {
        this.resizeHandler = () => this.debounceResize();
        
        // Initial resize
        this.resizeIframe();
        
        // Add load and window resize listeners
        this.el.addEventListener('load', this.resizeHandler);
        window.addEventListener('resize', this.resizeHandler);
    },

    destroyed() {
        // Clean up all listeners
        this.el.removeEventListener('load', this.resizeHandler);
        window.removeEventListener('resize', this.resizeHandler);
        if (this.resizeTimeout) {
            clearTimeout(this.resizeTimeout);
        }
    },

    debounceResize() {
        if (this.resizeTimeout) {
            clearTimeout(this.resizeTimeout);
        }
        this.resizeTimeout = setTimeout(() => {
            requestAnimationFrame(() => this.resizeIframe());
        }, 100);
    },

    resizeIframe() {
        try {
            const iframe = this.el;
            const iframeWindow = iframe.contentWindow;
            
            if (!iframeWindow) return;

            const iframeDoc = iframeWindow.document;
            const height = Math.max(
                iframeDoc.documentElement.scrollHeight,
                iframeDoc.body.scrollHeight
            );

            const maxHeight = window.innerHeight * 0.8;
            const newHeight = Math.min(height, maxHeight);

            if (iframe.style.height !== `${newHeight}px`) {
                iframe.style.height = `${newHeight}px`;
            }
        } catch (error) {
            console.warn('Failed to resize iframe:', error);
        }
    }
};

import Sortable from "../../vendor/sortable"

const SortableJs = {
    mounted() {
        new Sortable(this.el, {
            animation: 150,
            delay: 100,
            dragClass: "drag-item",
            ghostClass: "drag-ghost",
            forceFallback: true,
            onEnd: e => {
                let params = {old: e.oldIndex, new: e.newIndex, ...e.item.dataset}
            }
        })
    }
}

export default SortableJs;

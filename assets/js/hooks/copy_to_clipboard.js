export const CopyInnerHTML = {
    mounted() {
        const { to } = this.el.dataset;
        
        if (!to) {
            console.error('No target element specified in data-to attribute');
            return;
        }

        this.el.addEventListener("click", this.handleClick.bind(this));
    },

    async handleClick(event) {
        event.preventDefault();
        
        const targetElement = document.querySelector(this.el.dataset.to);
        
        if (!targetElement) {
            console.error('Target element not found');
            return;
        }

        try {
            await navigator.clipboard.writeText(targetElement.innerHTML);
            this.updateButtonState("✅", "📋");
        } catch (error) {
            console.error('Failed to copy text:', error);
            this.updateButtonState("❌", "📋");
        }
    },

    updateButtonState(tempIcon, defaultIcon) {
        this.el.innerText = tempIcon;
        setTimeout(() => {
            this.el.innerText = defaultIcon;
        }, 3000);
    },

    destroyed() {
        this.el.removeEventListener("click", this.handleClick);
    }
};
import { v4 as uuidv4 } from 'uuid';

const DEVICE_UUID_KEY = 'deviceUUID';
let cachedUUID = null;

function getOrCreateDeviceUUID() {
    if (cachedUUID) return cachedUUID;
    
    try {
        let deviceUUID = localStorage.getItem(DEVICE_UUID_KEY);
        
        if (!deviceUUID || typeof deviceUUID !== 'string') {
            deviceUUID = uuidv4();
            localStorage.setItem(DEVICE_UUID_KEY, deviceUUID);
        }
        
        cachedUUID = deviceUUID;
        return deviceUUID;
    } catch (error) {
        console.warn('Failed to access localStorage:', error);
        return uuidv4(); // Fallback to new UUID if localStorage fails
    }
}

const DeviceUuid = {
    mounted() {
        if (this.el) {
            this.el.value = getOrCreateDeviceUUID();
        }
    }
};

export default DeviceUuid;

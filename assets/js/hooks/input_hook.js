// Constants
const LIMITS = {
  NRC: 11,
  NUMBER: 9,
  NUMBER2: 12,
  INTEGER: 9,
  TPIN: 10,
  PASSPORT: 9,
  SENDER_SEGMENT: 15,
  SMS_LENGTH: 160
};

// Utility functions
const createInputHandler = (pattern, maxLength) => {
  return function(event) {
    let value = event.target.value.replace(pattern, '');
    if (maxLength) value = value.slice(0, maxLength);
    event.target.value = value;
  };
};

// Hook definitions
export const nrcInputHook = {
  mounted() {
    const formatNRC = (value) => {
      const digits = value.replace(/\D/g, '').slice(0, 9); // max 9 digits
      let formatted = '';

      if (digits.length <= 6) {
        formatted = digits;
      } else if (digits.length <= 8) {
        formatted = `${digits.slice(0, 6)}/${digits.slice(6)}`;
      } else {
        formatted = `${digits.slice(0, 6)}/${digits.slice(6, 8)}/${digits.slice(8)}`;
      }

      return formatted;
    };

    const handleInput = (event) => {
      const input = event.target;
      const oldStart = input.selectionStart;
      const oldLength = input.value.length;

      const formatted = formatNRC(input.value);
      input.value = formatted;

      // Adjust cursor to prevent jumping
      const newLength = formatted.length;
      const diff = newLength - oldLength;
      input.setSelectionRange(oldStart + diff, oldStart + diff);
    };

    this.el.addEventListener('input', handleInput);
    this._cleanup = () => this.el.removeEventListener('input', handleInput);
  },
  destroyed() {
    this._cleanup?.();
  }
};


export const validateAlphaHook = {
  mounted() {
    const handleInput = (event) => {
      // Allow letters (including accents) and spaces only
      let value = event.target.value.replace(/[^a-zA-ZÀ-ÿ\s]/g, '');

      // Capitalize the first letter of each word
      value = value
        .split(' ')
        .map(word =>
          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join(' ');

      event.target.value = value;
    };

    this.el.addEventListener('input', handleInput);
    this._cleanup = () => this.el.removeEventListener('input', handleInput);
  },
  destroyed() {
    this._cleanup?.();
  }
};


export const validateNumberHook = {
  mounted() {
    const handleInput = createInputHandler(/[^\d]/g, LIMITS.NUMBER);
    this.el.addEventListener('input', handleInput);
    this._cleanup = () => this.el.removeEventListener('input', handleInput);
  },
  destroyed() {
    this._cleanup?.();
  }
};

export const validateAmountsHook = {
  mounted() {
    const handleInput = createInputHandler(/[^\d.]/g, LIMITS.NUMBER, true);
    this.el.addEventListener('input', handleInput);
    this._cleanup = () => this.el.removeEventListener('input', handleInput);
  },
  destroyed() {
    this._cleanup?.();
  }
};


export const validateNumberHook2 = {
  mounted() {
    const handleInput = createInputHandler(/[^\d]/g, LIMITS.NUMBER2);
    this.el.addEventListener('input', handleInput);
    this._cleanup = () => this.el.removeEventListener('input', handleInput);
  },
  destroyed() {
    this._cleanup?.();
  }
};

export const validateIntegerHook = {
  mounted() {
    const handleKeydown = (event) => {
      if (["Backspace", "Delete", "Tab", "Enter", "ArrowLeft", "ArrowRight"].includes(event.key)) return;
      if (!/^\d$/.test(event.key)) event.preventDefault();
    };

    const handleInput = createInputHandler(/\D/g, LIMITS.INTEGER);

    this.el.addEventListener("keydown", handleKeydown);
    this.el.addEventListener("input", handleInput);
    
    this._cleanup = () => {
      this.el.removeEventListener("keydown", handleKeydown);
      this.el.removeEventListener("input", handleInput);
    };
  },
  destroyed() {
    this._cleanup?.();
  }
};

export const validateTpinHook = {
  mounted() {
    const handleInput = createInputHandler(/[^\d]/g, LIMITS.TPIN);
    this.el.addEventListener('input', handleInput);
    this._cleanup = () => this.el.removeEventListener('input', handleInput);
  },
  destroyed() {
    this._cleanup?.();
  }
};

export const validatesSenderHook = {
  mounted() {
    const handleInput = (event) => {
      let value = event.target.value.replace(/[^a-zA-Z0-9,]/g, '').replace(/,+/g, ',');
      
      const segments = value.split(',').map(segment => {
        return segment.split('').map((char, index) => {
          return (index + 1) % LIMITS.SENDER_SEGMENT === 0 && 
                 index !== segment.length - 1 ? char + ',' : char;
        }).join('');
      });

      event.target.value = segments.join(',');
    };

    this.el.addEventListener('input', handleInput);
    this._cleanup = () => this.el.removeEventListener('input', handleInput);
  },
  destroyed() {
    this._cleanup?.();
  }
};

export const validatePassportHook = {
  mounted() {
    const handleInput = (event) => {
      // Remove all invalid characters and force uppercase
      let value = event.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');

      // Limit length to 15 characters
      if (value.length > 15) {
        value = value.slice(0, 15);
      }

      event.target.value = value;
    };

    this.el.addEventListener('input', handleInput);
    this._cleanup = () => this.el.removeEventListener('input', handleInput);
  },
  destroyed() {
    this._cleanup?.();
  }
};


export const messageCounter = {
  mounted() {
    const handleInput = (event) => {
      const length = event.target.value.length;
      const total = Math.ceil(length / LIMITS.SMS_LENGTH);
      const rem = LIMITS.SMS_LENGTH - (length % LIMITS.SMS_LENGTH) || 0;

      document.querySelector(".count").textContent = total;
      document.querySelector(".remainder").textContent = rem;
    };

    this.el.addEventListener("input", handleInput);
    this._cleanup = () => this.el.removeEventListener("input", handleInput);
  },
  destroyed() {
    this._cleanup?.();
  }
};
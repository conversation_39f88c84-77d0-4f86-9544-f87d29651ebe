const CURRENCY_CODE = 'GBP';
const NUMBER_REGEX = /[^0-9.,-]+/g;

const FORMAT_OPTIONS = {
    maximumFractionDigits: 2,
    currency: CURRENCY_CODE,
    style: "currency",
    currencyDisplay: "symbol"
};

const CurrencyInput = {
    mounted() {
        this._onFocus = this.onFocus.bind(this);
        this._onBlur = this.onBlur.bind(this);

        // Format initial value and bind events
        this.onBlur({ target: this.el });
        this.el.addEventListener('focus', this._onFocus);
        this.el.addEventListener('blur', this._onBlur);
    },

    destroyed() {
        this.el.removeEventListener('focus', this._onFocus);
        this.el.removeEventListener('blur', this._onBlur);
    },

    localStringToNumber(s) {
        try {
            if (!s) return 0;
            return Number(String(s).replace(NUMBER_REGEX, "")) || 0;
        } catch (error) {
            console.warn('Error parsing currency value:', error);
            return 0;
        }
    },

    onFocus(e) {
        const value = e.target.value;
        if (!value) return;
        
        try {
            e.target.value = this.localStringToNumber(value);
        } catch (error) {
            console.warn('Error focusing currency input:', error);
        }
    },

    onBlur(e) {
        try {
            const value = e.target.value;
            const numberValue = this.localStringToNumber(value);
            
            e.target.value = numberValue || numberValue === 0
                ? numberValue.toLocaleString(undefined, FORMAT_OPTIONS)
                : '';
        } catch (error) {
            console.warn('Error formatting currency value:', error);
            e.target.value = '';
        }
    }
};

export default CurrencyInput;

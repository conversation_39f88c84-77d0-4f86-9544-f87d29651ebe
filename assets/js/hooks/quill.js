// Import Quill and related libraries
import Quill from 'quill';
import QuillBetterTable from 'quill-better-table';
import {saveAs} from 'file-saver';
import * as quillToWord from 'quill-to-word';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';

// Fix: Correctly set pdfMake vfs fonts
pdfMake.vfs = pdfFonts.pdfMake ? pdfFonts.pdfMake.vfs : pdfFonts.vfs;

export const RichTextEditor = {
    // Keep track of all editor instances
    editorInstances: {},

    // Table button styles reference (singleton)
    tableButtonStylesAdded: false,
    styleElement: null,

    convertQuillToDocDefinition(html) {
        // Create a temporary div to work with the HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        // Parse the HTML into pdfmake content
        const content = [];

        // Process each node in the HTML
        Array.from(tempDiv.childNodes).forEach(node => {
            if (node.nodeType === Node.TEXT_NODE) {
                if (node.textContent.trim()) {
                    content.push({text: node.textContent.trim()});
                }
            } else if (node.nodeType === Node.ELEMENT_NODE) {
                // Handle various HTML elements
                switch (node.tagName.toLowerCase()) {
                    case 'h1':
                        content.push({
                            text: node.textContent,
                            fontSize: 24,
                            bold: true,
                            margin: [0, 10, 0, 5]
                        });
                        break;
                    case 'h2':
                        content.push({
                            text: node.textContent,
                            fontSize: 18,
                            bold: true,
                            margin: [0, 8, 0, 4]
                        });
                        break;
                    case 'p':
                        content.push({
                            text: node.textContent,
                            margin: [0, 5, 0, 5]
                        });
                        break;
                    case 'ul':
                        const listItems = Array.from(node.querySelectorAll('li'))
                            .map(li => ({text: li.textContent, listType: 'bullet'}));
                        content.push({
                            ul: listItems
                        });
                        break;
                    case 'ol':
                        const numberedItems = Array.from(node.querySelectorAll('li'))
                            .map(li => ({text: li.textContent}));
                        content.push({
                            ol: numberedItems
                        });
                        break;
                    case 'strong':
                    case 'b':
                        content.push({text: node.textContent, bold: true});
                        break;
                    case 'em':
                    case 'i':
                        content.push({text: node.textContent, italics: true});
                        break;
                    case 'u':
                        content.push({text: node.textContent, decoration: 'underline'});
                        break;
                    case 'br':
                        content.push({text: '\n'});
                        break;
                    // Handle tables properly
                    case 'table':
                        const tableData = this.processTable(node);
                        content.push(tableData);
                        break;
                    default:
                        // Default handling for other elements
                        content.push({text: node.textContent});
                }
            }
        });

        return {
            content: content,
            defaultStyle: {
                fontSize: 12,
            }
        };
    },

    // Add helper method to process tables for PDF
    processTable(tableNode) {
        const rows = Array.from(tableNode.querySelectorAll('tr'));
        const body = [];

        rows.forEach(row => {
            const cells = Array.from(row.querySelectorAll('th, td'));
            const rowData = cells.map(cell => {
                return {
                    text: cell.textContent,
                    bold: cell.tagName.toLowerCase() === 'th',
                    colSpan: cell.getAttribute('colspan') ? parseInt(cell.getAttribute('colspan')) : 1,
                    rowSpan: cell.getAttribute('rowspan') ? parseInt(cell.getAttribute('rowspan')) : 1
                };
            });
            body.push(rowData);
        });

        return {
            table: {
                headerRows: tableNode.querySelector('thead') ? tableNode.querySelectorAll('thead tr').length : 0,
                body: body
            },
            margin: [0, 5, 0, 5]
        };
    },

    mounted() {
        const editorContainer = this.el;
        const editorId = this.el.id;

        // Store context reference for use in callbacks
        const hook = this;

        // Register styles only once for all instances
        if (!this.tableButtonStylesAdded) {
            this.styleElement = this.addTableButtonStyles();
            this.tableButtonStylesAdded = true;
        }

        // Register the better-table module (only once)
        if (!Quill.imports['modules/better-table']) {
            Quill.register({
                'modules/better-table': QuillBetterTable
            }, true);

            // Import the correct BlockEmbed class
            const BlockEmbed = Quill.import('blots/block/embed');

            class TableButtonBlot extends BlockEmbed {
                static create(value) {
                    const node = super.create(value);
                    return node;
                }
            }

            TableButtonBlot.blotName = 'insertTable';
            TableButtonBlot.tagName = 'div';
            TableButtonBlot.className = 'ql-custom-table';

            // Make sure to register it correctly
            Quill.register(TableButtonBlot);
        }

        // Configure Quill options
        const options = {
            theme: 'snow',
            modules: {
                table: false,  // disable default table module
                'better-table': {
                    operationMenu: {
                        items: {
                            insertRowAbove: {text: 'Insert Row Above'},
                            insertRowBelow: {text: 'Insert Row Below'},
                            insertColumnLeft: {text: 'Insert Column Left'},
                            insertColumnRight: {text: 'Insert Column Right'},
                            deleteRow: {text: 'Delete Row'},
                            deleteColumn: {text: 'Delete Column'},
                            deleteTable: {text: 'Delete Table'},
                            mergeCells: {text: 'Merge Cells'},
                            unmergeCells: {text: 'Unmerge Cells'}
                        }
                    },
                    keyboard: {
                        bindings: QuillBetterTable.keyboardBindings
                    }
                },
                toolbar: {
                    container: [
                        ['bold', 'italic', 'underline', 'strike'],
                        ['blockquote', 'code-block'],
                        [{'header': [1, 2, 3, 4, 5, 6, false]}],
                        [{'list': 'ordered'}, {'list': 'bullet'}, {'list': 'check'}],
                        [{'indent': '-1'}, {'indent': '+1'}],
                        [{'color': []}, {'background': []}],
                        [{'align': []}],
                        ['link', 'image'],
                        ['clean'],
                        ['insertTable']
                    ],
                    handlers: {
                        'insertTable': function () {
                            // Get the instance data by editorId
                            const instanceData = hook.editorInstances[editorId];
                            if (!instanceData) return;

                            // Get quill instance from the stored data
                            const quill = instanceData.quill;

                            // Create and display table selector UI
                            const container = hook.createTableSelectorUI(quill, editorId);

                            // Position it relative to the button in this specific editor
                            const toolbar = quill.container.querySelector('.ql-toolbar');
                            const button = toolbar.querySelector('.ql-insertTable');
                            if (button) {
                                const buttonRect = button.getBoundingClientRect();
                                hook.positionTableSelector(container, buttonRect, editorId);
                            }
                        }
                    }
                }
            }
        };

        // Initialize Quill editor
        const quill = new Quill(editorContainer, options);

        // Store editor instance with its associated data
        this.editorInstances[editorId] = {
            quill: quill,
            documentClickHandler: null,
            textChangeHandler: null
        };

        // Create a text-change listener for updating hidden field
        const textChangeHandler = () => {
            this.pushEvent('update_rich_text', {
                id: editorId,
                value: quill.container.firstChild.innerHTML
            });
        };

        // Store the handler reference
        this.editorInstances[editorId].textChangeHandler = textChangeHandler;

        // Update hidden field on editor change
        quill.on('text-change', textChangeHandler);

        // Handle incoming data from Phoenix LiveView
        this.handleEvent("rich_text_data", ({id, value}) => {
            // Only process events for this specific editor
            if (id !== editorId) return;

            const instanceData = this.editorInstances[editorId];
            if (!instanceData || !instanceData.quill) return;

            if (!value) return;

            try {
                // Check if value itself is HTML content
                const htmlContent = typeof value === 'string' ? value :
                    (value.data ? value.data : null);

                if (htmlContent) {
                    // Set HTML content directly when HTML is provided
                    instanceData.quill.root.innerHTML = htmlContent;
                }
            } catch (error) {
                console.error(`Error setting editor ${editorId} HTML content:`, error);
            }
        });

        // Handle editor status (enabled/disabled)
        this.handleEvent("rich_text_status", ({id, status}) => {
            // Only process events for this specific editor
            if (id !== editorId) return;

            const instanceData = this.editorInstances[editorId];
            if (instanceData && instanceData.quill) {
                instanceData.quill.enable(status);
            }
        });

        // Handle PDF export event from the server
        this.handleEvent("export_pdf", ({id, file_name}) => {
            // Only process events for this specific editor
            if (id !== editorId) return;

            const instanceData = this.editorInstances[editorId];
            if (!instanceData || !instanceData.quill) return;

            try {
                // Get HTML content from Quill editor
                const htmlContent = instanceData.quill.root.innerHTML;

                // Create PDF definition using pdfmake
                const docDefinition = this.convertQuillToDocDefinition(htmlContent);

                // Generate and download PDF
                pdfMake.createPdf(docDefinition).download(`${file_name || 'quill-document'}.pdf`);
            } catch (error) {
                console.error(`Error exporting editor ${editorId} to PDF:`, error);
                this.pushEvent("export_error", {id: editorId, format: "pdf", message: error.message});
            }
        });

        // Handle Word export event from the server
        this.handleEvent("export_word", async ({id, file_name}) => {
            // Only process events for this specific editor
            if (id !== editorId) return;

            const instanceData = this.editorInstances[editorId];
            if (!instanceData || !instanceData.quill) return;

            try {
                // Get Quill Delta content from the editor
                const delta = instanceData.quill.getContents();

                // Use quill-to-word to convert Delta to Word document
                let docFile;
                if (quillToWord.generateWord) {
                    docFile = await quillToWord.generateWord(delta, {
                        exportAs: 'blob'
                    });
                } else if (quillToWord.default) {
                    docFile = await quillToWord.default(delta, {
                        exportAs: 'blob'
                    });
                } else if (typeof quillToWord === 'function') {
                    docFile = await quillToWord(delta, {
                        exportAs: 'blob'
                    });
                } else {
                    throw new Error('quillToWord function not found');
                }

                // Ensure we have a proper blob
                if (!(docFile instanceof Blob)) {
                    // If it's not a blob, try to convert it
                    if (docFile instanceof ArrayBuffer) {
                        docFile = new Blob([docFile], { 
                            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
                        });
                    } else if (docFile && docFile.buffer) {
                        docFile = new Blob([docFile.buffer], { 
                            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
                        });
                    } else {
                        throw new Error('Invalid file format returned from quillToWord');
                    }
                }
                
                saveAs(docFile, `${file_name || 'document'}.docx`);
            } catch (error) {
                console.error(`Error exporting editor ${editorId} to Word:`, error);
                this.pushEvent("export_error", {id: editorId, format: "word", message: error.message});
            }
        });
    },

    // Helper method to create table selector UI
    createTableSelectorUI(quill, editorId) {
        const hook = this;

        // Create a div to select table dimensions
        const container = document.createElement('div');
        container.className = 'ql-table-selector';
        container.dataset.editorId = editorId; // Store editor ID in dataset
        container.style.position = 'absolute';
        container.style.backgroundColor = 'white';
        container.style.border = '1px solid #ccc';
        container.style.padding = '8px';
        container.style.zIndex = '100';
        container.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.2)';
        container.style.borderRadius = '4px';

        // Create grid for table dimensions
        const grid = document.createElement('div');
        grid.style.display = 'grid';
        grid.style.gridTemplateColumns = 'repeat(8, 20px)';
        grid.style.gridTemplateRows = 'repeat(8, 20px)';
        grid.style.gap = '2px';

        let selectedRows = 0;
        let selectedCols = 0;
        let cells = [];

        // Create an 8x8 grid of cells
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const cell = document.createElement('div');
                cell.style.width = '20px';
                cell.style.height = '20px';
                cell.style.backgroundColor = '#f0f0f0';
                cell.style.border = '1px solid #ddd';
                cell.style.transition = 'background-color 0.1s ease';

                // When hovering, highlight cells
                cell.addEventListener('mouseover', function () {
                    selectedRows = row + 1;
                    selectedCols = col + 1;

                    // Reset all cells
                    cells.forEach(c => c.style.backgroundColor = '#f0f0f0');

                    // Highlight cells in the range
                    for (let r = 0; r < selectedRows; r++) {
                        for (let c = 0; c < selectedCols; c++) {
                            cells[r * 8 + c].style.backgroundColor = '#b3d7ff';
                        }
                    }

                    dimensions.textContent = `${selectedRows}×${selectedCols}`;
                });

                // On click, insert table with selected dimensions
                cell.addEventListener('click', function () {
                    const tableModule = quill.getModule('better-table');
                    tableModule.insertTable(selectedRows, selectedCols);
                    document.body.removeChild(container);

                    // Remove event listener on selection
                    const instanceData = hook.editorInstances[editorId];
                    if (instanceData && instanceData.documentClickHandler) {
                        document.removeEventListener('click', instanceData.documentClickHandler);
                        instanceData.documentClickHandler = null;
                    }
                });

                grid.appendChild(cell);
                cells.push(cell);
            }
        }

        // Add dimension display
        const dimensions = document.createElement('div');
        dimensions.textContent = '0×0';
        dimensions.style.textAlign = 'center';
        dimensions.style.marginTop = '8px';
        dimensions.style.fontFamily = 'system-ui, -apple-system, sans-serif';
        dimensions.style.fontSize = '12px';
        dimensions.style.color = '#444';

        container.appendChild(grid);
        container.appendChild(dimensions);

        return container;
    },

    // Helper method to position table selector
    positionTableSelector(container, buttonRect, editorId) {
        container.style.top = `${buttonRect.bottom + window.scrollY + 5}px`;
        container.style.left = `${buttonRect.left + window.scrollX}px`;
        document.body.appendChild(container);

        // Get instance data
        const hook = this;
        const instanceData = hook.editorInstances[editorId];

        // Handler for outside clicks to close the selector
        function closeSelector(e) {
            if (!container.contains(e.target) &&
                !e.target.classList.contains('ql-insertTable')) {
                if (document.body.contains(container)) {
                    document.body.removeChild(container);
                }
                document.removeEventListener('click', closeSelector);
                if (instanceData) {
                    instanceData.documentClickHandler = null;
                }
            }
        }

        // Store reference to handler for cleanup
        if (instanceData) {
            // Remove any existing handler first
            if (instanceData.documentClickHandler) {
                document.removeEventListener('click', instanceData.documentClickHandler);
            }
            instanceData.documentClickHandler = closeSelector;
        }

        // Handle outside clicks to close the selector
        document.addEventListener('click', closeSelector);
    },

    // Helper method to add table button styles
    addTableButtonStyles() {
        const style = document.createElement('style');
        style.textContent = `
        .ql-insertTable::before {
          content: "";
          display: inline-block;
          width: 18px;
          height: 18px;
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='3' y1='9' x2='21' y2='9'%3E%3C/line%3E%3Cline x1='3' y1='15' x2='21' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='3' x2='9' y2='21'%3E%3C/line%3E%3Cline x1='15' y1='3' x2='15' y2='21'%3E%3C/line%3E%3C/svg%3E");
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
        }
        
        .ql-snow .ql-toolbar button.ql-insertTable {
          width: 28px;
        }
        
        .ql-table-selector {
          animation: fadeIn 0.2s ease;
        }
        
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-5px); }
          to { opacity: 1; transform: translateY(0); }
        }
      `;
        document.head.appendChild(style);
        return style; // Return for cleanup in destroy
    },

    // Add destroy method to clean up resources
    destroyed() {
        const editorId = this.el.id;
        const instanceData = this.editorInstances[editorId];

        // Clean up this specific editor instance
        if (instanceData) {
            const quill = instanceData.quill;

            if (quill) {
                // Remove text-change event listener
                if (instanceData.textChangeHandler) {
                    quill.off('text-change', instanceData.textChangeHandler);
                }

                // Disable editor
                quill.enable(false);
            }

            // Remove any table selector click handler if active
            if (instanceData.documentClickHandler) {
                document.removeEventListener('click', instanceData.documentClickHandler);
            }

            // Remove table selector if it exists for this editor
            const selector = document.querySelector(`.ql-table-selector[data-editor-id="${editorId}"]`);
            if (selector && document.body.contains(selector)) {
                document.body.removeChild(selector);
            }
        }

        // Remove this instance from the tracked instances
        delete this.editorInstances[editorId];

        // Check if this is the last editor being destroyed
        if (Object.keys(this.editorInstances).length === 0) {
            // Clean up style element if it exists
            if (this.styleElement && document.head.contains(this.styleElement)) {
                document.head.removeChild(this.styleElement);
                this.styleElement = null;
                this.tableButtonStylesAdded = false;
            }
        }
    }
};
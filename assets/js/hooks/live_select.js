function debounce(func, msec) {
    let timer;
    return (...args) => {
        clearTimeout(timer)
        timer = setTimeout(() => {
            func.apply(this, args)
        }, msec)
    }
}

export const LiveSelect = {
  textInput() {
    return this.el.querySelector("input[type=text]");
  },
  debounceMsec() {
    return parseInt(this.el.dataset["debounce"]);
  },
  updateMinLen() {
    return parseInt(this.el.dataset["updateMinLen"]);
  },
  maybeStyleClearButton() {
    const clearButton = this.el.querySelector("button[phx-click=clear]");
    if (clearButton) {
      this.textInput().parentElement.style.position = "relative";
      Object.assign(clearButton.style, {
        position: "absolute",
        top: "0px",
        bottom: "0px",
        right: "5px",
        display: "block",
      });
    }
  },
  pushEventToParent(event, payload) {
    const target = this.el.dataset["phxTarget"];
    target ? this.pushEventTo(target, event, payload) : this.pushEvent(event, payload);
  },
  _cleanup() {
    // Cleanup event listeners
    if (this._boundHandlers) {
        const input = this.textInput();
        input.removeEventListener("keydown", this._boundHandlers.keydown);
        input.removeEventListener("input", this._boundHandlers.input);
        
        const dropdown = this.el.querySelector("ul");
        dropdown?.removeEventListener("mousedown", this._boundHandlers.dropdownClick);
        
        this.el.querySelectorAll("button[data-idx]").forEach(button => {
            button.removeEventListener("click", this._boundHandlers.buttonClick);
        });
    }
},
  attachDomEventHandlers() {
    this._boundHandlers = {};
    const input = this.textInput();

    // Store bound handlers for cleanup
    this._boundHandlers.keydown = (event) => {
        if (event.code === "Enter") event.preventDefault();
        this.pushEventTo(this.el, "keydown", { key: event.code });
    };

    this.changeEvents = debounce((id, field, text) => {
        this.pushEventTo(this.el, "validate", { text });
        this.pushEventToParent("live_select_change", { id: this.el.id, field, text });
    }, this.debounceMsec());

    this._boundHandlers.input = (event) => {
        const text = event.target.value.trim();
        const field = this.el.dataset["field"];
        if (text.length >= this.updateMinLen()) {
            this.changeEvents(this.el.id, field, text);
        } else {
            this.pushEventTo(this.el, "options_clear", {});
        }
    };

    const dropdown = this.el.querySelector("ul");
    if (dropdown) {
        this._boundHandlers.dropdownClick = (event) => {
            const option = event.target.closest("div[data-idx]");
            if (option) {
                this.pushEventTo(this.el, "option_click", { idx: option.dataset.idx });
                event.preventDefault();
            }
        };
        dropdown.addEventListener("mousedown", this._boundHandlers.dropdownClick);
    }

    this._boundHandlers.buttonClick = (button) => () => {
        this.pushEventTo(this.el, "option_remove", { idx: button.dataset.idx });
    };

    // Attach event listeners
    input.addEventListener("keydown", this._boundHandlers.keydown);
    input.addEventListener("input", this._boundHandlers.input);

    this.el.querySelectorAll("button[data-idx]").forEach(button => {
        button.addEventListener("click", this._boundHandlers.buttonClick(button));
    });
},
  setInputValue(value) {
    this.textInput().value = value;
  },
  inputEvent(selection, mode) {
    const selector =
      mode === "single"
        ? "input.single-mode"
        : selection.length === 0
        ? "input[data-live-select-empty]"
        : "input[type=hidden]";
    this.el.querySelector(selector)?.dispatchEvent(new Event("input", { bubbles: true }));
  },
  mounted() {
    this.maybeStyleClearButton();
    this.handleEvent("parent_event", ({ id, event, payload }) => {
      if (this.el.id === id) {
        this.pushEventToParent(event, payload);
      }
    });
    this.handleEvent("select", ({ id, selection, mode, input_event, parent_event }) => {
      if (this.el.id === id) {
        this.selection = selection;
        this.setInputValue(mode === "single" && selection.length > 0 ? selection[0].label : null);
        if (input_event) this.inputEvent(selection, mode);
        if (parent_event) this.pushEventToParent(parent_event, { id });
      }
    });
    this.handleEvent("active", ({ id, idx }) => {
      if (this.el.id === id) {
        this.el.querySelector(`div[data-idx="${idx}"]`)?.scrollIntoView({ block: "nearest" });
      }
    });
    this.attachDomEventHandlers();
  },
  updated() {
    this.maybeStyleClearButton();
    this.attachDomEventHandlers();
  },
  reconnected() {
    if (this.selection?.length > 0) {
      this.pushEventTo(this.el.id, "selection_recovery", this.selection);
    }
  },
  destroyed() {
    this._cleanup();
    // Clear any pending debounced calls
    if (this.changeEvents?.cancel) {
        this.changeEvents.cancel();
    }
}
};

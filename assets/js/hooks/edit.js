// assets/js/hooks/edit.js
export const EditableHook = {
    mounted() {
        // ensure it’s editable
        this.el.setAttribute("contenteditable", "true")

        // on every input (typing, paste, delete…)
        this.el.addEventListener("input", e => {
            this.pushEvent("editable_update", { html: this.el.innerHTML })
        })

        // optionally, also on blur (when you leave the field)
        this.el.addEventListener("blur", e => {
            this.pushEvent("editable_blur", { html: this.el.innerHTML })
        })
    }
}

const LiveViewPushEvent = {
    mounted() {
        const liveView = this
        this.liveViewPushEvent = function(e) {
            liveView.pushEvent(e.detail.event, e.detail.payload)
        }
        this.liveViewPushEventTo = function(e) {
            liveView.pushEventTo(e.detail.selector, e.detail.event,
                e.detail.payload)
        }
        window.addEventListener('liveview-push-event',
            this.liveViewPushEvent)
        window.addEventListener('liveview-push-event-to',
            this.liveViewPushEventTo)
    },
    destroyed() {
        window.removeEventListener('liveview-push-event',
            this.liveViewPushEvent)
        window.removeEventListener('liveview-push-event-to',
            this.liveViewPushEventTo)
    }
}

export default LiveViewPushEvent;
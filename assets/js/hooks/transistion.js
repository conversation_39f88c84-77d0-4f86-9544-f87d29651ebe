export const Transition = {
    mounted() {
        try {
            this.from = this.el.dataset.transitionFrom?.split(' ') || []
            this.to = this.el.dataset.transitionTo?.split(' ') || []
            
            if (this.from.length) {
                this.el.classList.add(...this.from)
                
                this.timeoutId = setTimeout(() => {
                    this.el.classList.remove(...this.from)
                    this.el.classList.add(...this.to)
                }, 10)
            }
        } catch (error) {
            console.error('Transition hook error:', error)
        }
    },
    
    updated() {
        const classesToRemove = ['transition', ...this.from]
        this.el.classList.remove(...classesToRemove.filter(c => this.el.classList.contains(c)))
    },
    
    destroyed() {
        if (this.timeoutId) {
            clearTimeout(this.timeoutId)
        }
        
        // Cleanup classes if element still exists
        if (this.el) {
            this.el.classList.remove(...(this.from || []), ...(this.to || []))
        }
    }
}

defmodule AppWeb.PermissionHelpers do
  @moduledoc """
  Helper functions for checking permissions in templates and LiveViews
  """

  def has_permission?(socket, permission_code) do
    case socket.assigns do
      %{permission_codes: codes} -> permission_code in codes
      _ -> false
    end
  end

  def has_any_permission?(socket, permission_codes) when is_list(permission_codes) do
    case socket.assigns do
      %{permission_codes: codes} ->
        Enum.any?(permission_codes, &(&1 in codes))

      _ ->
        false
    end
  end

  def has_all_permissions?(socket, permission_codes) when is_list(permission_codes) do
    case socket.assigns do
      %{permission_codes: codes} ->
        Enum.all?(permission_codes, &(&1 in codes))

      _ ->
        false
    end
  end
end

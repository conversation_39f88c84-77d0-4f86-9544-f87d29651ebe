defmodule AppWeb.Helps.CountryHelpers do
  def get_countries do
    Countries.all()
    |> Enum.map(fn country ->
      {country.name, country.alpha2}
    end)
    |> Enum.sort_by(fn {name, _code} -> name end)
  end

  def get_nationality do
    Countries.all()
    |> Enum.map(fn country ->
      {country.nationality, country.nationality}
    end)
    |> Enum.uniq()
    |> Enum.reject(fn {nationality, _code} -> nationality == "" end)
    |> Enum.sort_by(fn {nationality, _code} -> nationality end)
  end

  def get_country_by_code(country_code) do
    try do
      case Countries.get(country_code) do
        nil -> nil
        country -> country.name
      end
    rescue
      MatchError -> nil
    end
  end

  def get_country_by_calling_code(calling_code) do
    try do
      case Countries.filter_by(:country_code, calling_code) do
        [] ->
          nil

        countries ->
          # Return first country for single match, or list of countries for multiple matches
          case length(countries) do
            1 -> hd(countries).name
            _ -> Enum.map(countries, & &1.name)
          end
      end
    rescue
      _ -> nil
    end
  end

  def get_countries_by_calling_code(calling_code) do
    try do
      Countries.filter_by(:country_code, calling_code)
      |> Enum.map(fn country ->
        {country.name, country.alpha2}
      end)
      |> Enum.sort_by(fn {name, _code} -> name end)
    rescue
      _ -> []
    end
  end

  #   # Single country lookup
  # AppWeb.CountryHelpers.get_country_by_calling_code("260")
  # # => "Zambia"

  # # Multiple countries (shared calling code)
  # AppWeb.CountryHelpers.get_country_by_calling_code("1")
  # # => ["United States of America", "Canada", ...]

  # # Structured data for forms/dropdowns
  # AppWeb.CountryHelpers.get_countries_by_calling_code("260")
  # # => [{"Zambia", "ZM"}]
end

defmodule AppWeb.UserAuth do
  @moduledoc """
  Authentication and authorization module for the application.

  This module provides functions for user authentication, session management,
  and authorization checks. It handles login/logout operations, session tokens,
  remember me functionality, and LiveView authentication mounting.

  Key features:
  - User login and logout with session management
  - Remember me cookie functionality
  - LiveView authentication mounting
  - Permission and role-based access control
  - Session security with token rotation
  """

  use AppWeb, :verified_routes

  import Plug.Conn
  import Phoenix.Controller

  alias App.Roles
  alias App.Users, as: Accounts
  alias App.Util.CustomTime
  alias Logs.Audit

  # Make the remember me cookie valid for 60 days.
  # If you want bump or reduce this value, also change
  # the token expiry itself in UserToken.
  @max_age 60 * 60 * 24 * 60
  @remember_me_cookie "_pbs_gw_client_user_remember_me"
  @remember_me_options [sign: true, max_age: @max_age, same_site: "pbs_gw_client"]

  def make_session_id(token), do: "users_sessions:#{Base.url_encode64(token, padding: false)}"

  @spec log_in_user(Plug.Conn.t(), atom | %{:id => any, optional(any) => any}, any) ::
          Plug.Conn.t()
  @doc """
  Logs the user in.

  It renews the session ID and clears the whole session
  to avoid fixation attacks. See the renew_session
  function to customize this behaviour.

  It also sets a `:live_socket_id` key in the session,
  so LiveView sessions are identified and automatically
  disconnected on log out. The line can be safely removed
  if you are not using LiveView.
  """
  def log_in_user(conn, user, params \\ %{}) do
    token = Accounts.generate_user_session_token(user)
    user_return_to = get_session(conn, :user_return_to)
    session_id = make_session_id(token)

    Task.start(fn ->
      Accounts.update_user(user, %{last_logon: CustomTime.local_time()})

      Audit.create_session_on_login(
        conn,
        {session_id, "WEB", "Successfully logged in", user.id},
        true
      )
    end)

    conn
    |> renew_session()
    |> put_token_in_session(token)
    |> maybe_write_remember_me_cookie(token, params)
    |> redirect(to: user_return_to || signed_in_path(conn, user))
  end

  def redirect_user_login(conn, user, redirect_path) do
    token = Accounts.generate_user_session_token(user)
    # user_return_to = get_session(conn, :user_return_to)
    session_id = make_session_id(token)

    Task.start(fn ->
      Accounts.update_user(user, %{last_logon: CustomTime.local_time()})

      Audit.create_session_on_login(
        conn,
        {session_id, "WEB", "Successfully logged in", user.id},
        true
      )
    end)

    conn
    |> renew_session()
    |> put_token_in_session(token)
    |> maybe_write_remember_me_cookie(token, %{})
    |> redirect(to: redirect_path)
  end

  defp maybe_write_remember_me_cookie(conn, token, %{"remember_me" => "true"}) do
    put_resp_cookie(conn, @remember_me_cookie, token, @remember_me_options)
  end

  defp maybe_write_remember_me_cookie(conn, _token, _params) do
    conn
  end

  # This function renews the session ID and erases the whole
  # session to avoid fixation attacks. If there is any data
  # in the session you may want to preserve after log in/log out,
  # you must explicitly fetch the session data before clearing
  # and then immediately set it after clearing, for example:
  #
  #     defp renew_session(conn) do
  #       preferred_locale = get_session(conn, :preferred_locale)
  #
  #       conn
  #       |> configure_session(renew: true)
  #       |> clear_session()
  #       |> put_session(:preferred_locale, preferred_locale)
  #     end
  #
  defp renew_session(conn) do
    conn
    |> configure_session(renew: true)
    |> clear_session()
  end

  @doc """
  Logs the user out.

  It clears all session data for safety. See renew_session.
  """
  def log_out_user(conn) do
    user_token = get_session(conn, :user_token)
    user_token && Accounts.delete_user_session_token(user_token)

    if live_socket_id = get_session(conn, :live_socket_id) do
      AppWeb.Endpoint.broadcast(live_socket_id, "disconnect", %{})
    end

    Task.start(fn ->
      Audit.find_and_update_session_id_log_out(
        String.replace(conn.private[:plug_session]["live_socket_id"], "=", ""),
        conn.assigns.current_user.id,
        "Successfully logged out"
      )
    end)

    conn
    |> renew_session()
    |> delete_resp_cookie(@remember_me_cookie)
    |> redirect(to: ~p"/")
  end

  @doc """
  Authenticates the user by looking into the session
  and remember me token.
  """
  def fetch_current_user(conn, _opts) do
    {user_token, conn} = ensure_user_token(conn)

    user = user_token && Accounts.get_user_by_session_token(user_token)

    assign(conn, :current_user, user)
    |> assign(
      :user_token,
      Phoenix.Token.sign(
        AppWeb.Endpoint,
        "user auth",
        try do
          user.id
        rescue
          _ -> nil
        end
      )
    )
  end

  defp ensure_user_token(conn) do
    if token = get_session(conn, :user_token) do
      {token, conn}
    else
      conn = fetch_cookies(conn, signed: [@remember_me_cookie])

      if token = conn.cookies[@remember_me_cookie] do
        {token, put_token_in_session(conn, token)}
      else
        {nil, conn}
      end
    end
  end

  @doc """
  Handles mounting and authenticating the current_user in LiveViews.

  ## `on_mount` arguments

    * `:mount_current_user` - Assigns current_user
      to socket assigns based on user_token, or nil if
      there's no user_token or no matching user.

    * `:ensure_authenticated` - Authenticates the user from the session,
      and assigns the current_user to socket assigns based
      on user_token.
      Redirects to login page if there's no logged user.

    * `:redirect_if_user_is_authenticated` - Authenticates the user from the session.
      Redirects to signed_in_path if there's a logged user.

  ## Examples

  Use the `on_mount` lifecycle macro in LiveViews to mount or authenticate
  the current_user:

      defmodule AppWeb.PageLive do
        use AppWeb, :live_view

        on_mount {AppWeb.UserAuth, :mount_current_user}
        ...
      end

  Or use the `live_session` of your router to invoke the on_mount callback:

      live_session :authenticated, on_mount: [{AppWeb.UserAuth, :ensure_authenticated}] do
        live "/profile", ProfileLive, :index
      end
  """
  def on_mount(:mount_current_user, _params, session, socket) do
    {:cont, mount_current_user(socket, session)}
  end

  def on_mount(:ensure_authenticated, _params, session, socket) do
    socket = mount_current_user(socket, session)

    if socket.assigns.current_user do
      {:cont, socket}
    else
      socket =
        socket
        |> Phoenix.LiveView.put_flash(:error, "You must log in to access this page.")
        |> Phoenix.LiveView.redirect(to: ~p"/users/log_in")

      {:halt, socket}
    end
  end

  def on_mount(:redirect_if_user_is_authenticated, _params, session, socket) do
    socket = mount_current_user(socket, session)

    if user = socket.assigns.current_user do
      {:halt, Phoenix.LiveView.redirect(socket, to: signed_in_path(socket, user))}
    else
      {:cont, socket}
    end
  end

  def on_mount(:get_browser_data, _params, session, socket) do
    socket =
      Phoenix.Component.assign(socket, :live_socket_identifier, session["live_socket_id"])
      |> Phoenix.Component.assign(remote_ip: session["remote_ip"])
      |> Phoenix.Component.assign(browser_info: session["browser_info"])
      |> Phoenix.Component.assign(user_agent: session["user_agent"])

    {:cont, socket}
  end

  defp mount_current_user(socket, session) do
    case session do
      %{"user_token" => user_token} ->
        user = Accounts.get_user_by_session_token(user_token)
        assign_user_to_socket(socket, user)

      _ ->
        assign_nil_user(socket)
    end
  end

  defp assign_user_to_socket(socket, nil) do
    assign_nil_user(socket)
  end

  defp assign_user_to_socket(socket, user) do
    user_permissions = get_user_permissions_data(user)

    socket
    |> Phoenix.Component.assign_new(:current_user, fn -> user end)
    |> Phoenix.Component.assign_new(:permissions, fn -> user_permissions.permission_codes end)
    |> Phoenix.Component.assign_new(:role_name, fn -> user_permissions.role_name end)
    |> Phoenix.Component.assign_new(:role_department, fn -> user_permissions.role_department end)
    |> Phoenix.Component.assign_new(:user_token, fn ->
      Phoenix.Token.sign(AppWeb.Endpoint, "user auth", user.id)
    end)
  end

  defp assign_nil_user(socket) do
    Phoenix.Component.assign_new(socket, :current_user, fn -> nil end)
  end

  defp get_user_permissions_data(user) do
    all_permissions = Roles.get_user_permissions(user.id, user.role_id)
    role = Roles.get_access_role_with_permissions(user.role_id)
    permission_codes = Enum.map(all_permissions, & &1.code)

    %{
      permission_codes: permission_codes,
      role_name: if(role, do: role.name, else: "No Role"),
      role_department: if(role, do: role.department_role_id, else: nil)
    }
  end

  @doc """
  Used for routes that require the user to not be authenticated.
  """
  def redirect_if_user_is_authenticated(conn, _opts) do
    if user = conn.assigns[:current_user] do
      conn
      |> redirect(to: signed_in_path(conn, user))
      |> halt()
    else
      conn
    end
  end

  @doc """
  Used for routes that require the user to be authenticated.

  If you want to enforce the user email is confirmed before
  they use the application at all, here would be a good place.
  """
  def require_authenticated_user(conn, _opts) do
    if conn.assigns[:current_user] do
      conn
    else
      conn
      |> put_flash(:error, "You must log in to access this page.")
      |> maybe_store_return_to()
      |> redirect(to: ~p"/users/log_in")
      |> halt()
    end
  end

  defp put_token_in_session(conn, token) do
    conn
    |> put_session(:user_token, token)
    |> put_session(:live_socket_id, "users_sessions:#{Base.url_encode64(token)}")
  end

  defp maybe_store_return_to(%{method: "GET"} = conn) do
    put_session(conn, :user_return_to, current_path(conn))
  end

  defp maybe_store_return_to(conn), do: conn

  defp signed_in_path(_conn, _user), do: ~p"/dashboard"
end

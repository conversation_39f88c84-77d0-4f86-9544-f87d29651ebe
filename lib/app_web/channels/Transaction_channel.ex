defmodule AppWeb.TransactionChannel do
  use AppWeb, :channel
  @moduledoc false

  @impl true
  def join("transaction:" <> user_id, _payload, socket) do
    {:ok, assign(socket, :channel, "transaction:#{user_id}")}
  end

  @impl true
  def handle_in("electricity", payload, socket) do
    broadcast!(socket, "transaction:" <> payload.user_id, payload)
    {:reply, {:ok, payload}, socket}
  end

  def handle_in("airtime", payload, socket) do
    broadcast!(socket, "transaction:" <> payload.user_id, payload)
    {:reply, {:ok, payload}, socket}
  end

  def handle_in("tv_bills", payload, socket) do
    broadcast!(socket, "transaction:" <> payload.user_id, payload)
    {:reply, {:ok, payload}, socket}
  end
end

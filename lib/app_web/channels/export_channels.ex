defmodule AppWeb.ExportsChannel do
  use AppWeb, :channel
  alias AppWeb.Endpoint
  @moduledoc false

  @impl true
  def join("exports:" <> browser_id, _payload, socket) do
    {:ok, assign(socket, :channel, "exports:#{browser_id}")}
  end

  # Channels can be used in a request/response fashion
  # by sending replies to requests from the client
  @impl true
  def handle_in("export_file", %{"browser_id" => browser_id} = payload, socket) do
    Task.start(fn ->
      file_content = App.Service.Export.index(socket, payload)

      if !is_nil(file_content) do
        Endpoint.broadcast(
          "exports:" <> to_string(browser_id),
          "export:" <> to_string(browser_id) <> ":complete",
          file_content
        )
      end
    end)

    {:noreply, socket}
  end

  # It is also common to receive messages from the client and
  # broadcast to everyone in the current topic (exports:lobby).
  @impl true
  def handle_in("shout", payload, socket) do
    broadcast(socket, "shout", payload)
    {:noreply, socket}
  end
end

defmodule AppWeb.Plugs.SecureHeaders do
  import Plug.Conn

  def init(opts), do: opts

  def call(conn, _opts) do
    conn
    |> put_resp_header("x-content-type-options", "nosniff")
    |> put_resp_header("x-download-options", "noopen")
    |> put_resp_header("x-frame-options", "SAMEORIGIN")
    |> put_resp_header("x-xss-protection", "1; mode=block")
    |> put_resp_header("referrer-policy", "strict-origin-when-cross-origin")
    |> put_resp_header(
      "permissions-policy",
      "camera=(), microphone=(), geolocation=(), interest-cohort=()"
    )
    |> put_resp_header(
      "content-security-policy",
      # Added 'unsafe-eval' for Alpine.js
      "default-src 'self'; " <>
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " <>
        "style-src 'self' 'unsafe-inline'; " <>
        "img-src 'self' data:; " <>
        "font-src 'self'; " <>
        "connect-src 'self'; " <>
        "frame-src 'self'; " <>
        "object-src 'none'; " <>
        "base-uri 'self';"
    )
    |> put_resp_header("strict-transport-security", "max-age=31536000; includeSubDomains")
  end
end

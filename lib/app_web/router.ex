defmodule AppWeb.Router do
  use AppWeb, :router
  import AppWeb.{AddToConn, UserA<PERSON>}
  alias AppWeb.{Layouts, UserAuth}

  pipeline :browser do
    plug(:accepts, ["html"])
    plug(:fetch_session)
    plug(:fetch_live_flash)
    plug(:protect_from_forgery)
    plug(:put_secure_browser_headers)
    plug(:fetch_current_user)
    plug(:put_client_ip)
    plug(:put_browser_id)
  end

  pipeline :redirect_url do
    plug(:accepts, ["html"])
    plug(:fetch_session)
    plug(:fetch_live_flash)
    plug(:put_secure_browser_headers)
    plug(:fetch_current_user)
    plug(:put_client_ip)
    plug(:put_browser_id)
  end

  pipeline :callback do
    plug(:accepts, ["html"])
    plug(:fetch_session)
    plug(:fetch_live_flash)
    plug(:put_secure_browser_headers)
  end

  pipeline :api do
    plug(:accepts, ["json"])
  end

  pipeline :admin_layout do
    plug(:put_root_layout, html: {Layouts, :dashboard})
  end

  pipeline :landing_layout do
    plug(:put_root_layout, html: {Layouts, :root})
  end

  pipeline :receipt_layout do
    plug(:put_root_layout, html: {Layouts, :receipt})
  end

  scope "/file", AppWeb do
    pipe_through([:api])
    get "/licenses/:id/download", LicenseDownloadController, :download

    post("/read", FileController, :read)
    post("/remove", FileController, :remove)
  end

  scope "/", AppWeb do
    pipe_through([:landing_layout, :browser])

    live("/file_browser", FileBrowserLive, :index)
    get("/session/:token", UserSessionController, :session)
    match(:*, "/users/log_out", UserSessionController, :delete)
  end

  scope "/", AppWeb do
    pipe_through([:landing_layout, :browser, :redirect_if_user_is_authenticated])

    live_session :redirect_if_user_is_authenticated,
      on_mount: [
        {UserAuth, :get_browser_data},
        {UserAuth, :mount_current_user},
        {UserAuth, :redirect_if_user_is_authenticated}
      ] do
      get("/", PageController, :home)
      live("/users/log_in", UserLoginLive, :new)
      live("/users/register", SelfRegistration.LandingLive.Index, :index)
      live("/users/signup", SelfRegistration.LandingLive.Index, :index)
      live("/users/signup/individual", UserRegistration.IndividualLive.Index, :index)
      live("/users/signup/business", UserRegistration.BusinessLive.Index, :index)
      live("/users/reset_password", UserForgotPasswordLive, :new)
    end

    post("/users/log_in", UserSessionController, :create)
  end

  scope "/", AppWeb do
    pipe_through([:browser, :receipt_layout])

    live_session :receipt_layout,
      on_mount: [
        {UserAuth, :get_browser_data},
        {UserAuth, :mount_current_user},
        {UserAuth, :ensure_authenticated}
      ] do
      live("/applications/association/:id", Auth.ViewApplicationsLive.Index, :index)
      live("/finance/association/:id", Auth.FinanceLive.Index, :index)
    end
  end

  scope "/", AppWeb do
    pipe_through([:admin_layout, :browser, :require_authenticated_user])

    live_session :require_authenticated_user,
      on_mount: [
        {UserAuth, :get_browser_data},
        {UserAuth, :mount_current_user},
        {UserAuth, :ensure_authenticated}
      ] do
      live("/dashboard", DashboardLive.Dashboard, :dashboard)
      live("/apply", ApplyLive.Index, :index)

      scope "/", Dashboard do
        live("/categories/:name", ClientLive.Index, :index)
      end

      scope "/maintenance", Maintenance do
        live("/api", ApiManagementLive.Index, :index)
        live("/approval_levels", ApprovalLevelsLive.Index, :index)
        live("/approval_levels/:id", ApplicationLevelsLive.Index, :index)
        live("/conditional_levels/:id", ConditionalLevelsLive.Index, :index)
        live("/conditions", ConditionsLive.Index, :index)
        live("/admin_emails", AdminEmailLive.Index, :index)
        live("/licence_drafts", LicenceDraftLive.Index, :index)
        live("/certificate_drafts", CertificateDraftLive.Index, :index)
        live("/summary/drafts", LicenceSummaryDraftLive.Index, :index)
        live("/:page", CategoryLandingLive.Index, :index)
      end

      scope "/logs", Auth.LogsLive do
        live("/audit/:session_id", Audit, :index)
        live("/user/management", Audit, :index)
        live("/system", Index, :index)
        live("/access/:session_id", Access, :index)
        live("/email", Email, :index)
        live("/device", DevicesLive.Index, :index)
        live("/session", Session, :index)
        live("/sms", Sms, :index)
        live("/archive/sms", MessagingAchieveLive.Index, :index)
        live("/api", Api, :index)
        live("/endpoints", Endpoint, :index)
        live("/redirect", Redirect, :index)
      end

      scope "/admin" do
        live("/users", Auth.UserLive.Index, :index)
        live("/logs/access/:session_id", Auth.LogsLive.Access, :index)

        live("/access_roles", Authenticated.Authentication.AccessRoleLive.Index, :index)
      end

      scope "/users", Auth do
        live("/system_admin", UserLive.Index, :index)
      end

      scope "/license/registration", Auth.Registration do
        live("/fields/:license_id", TextFieldsLive.Index, :index)
        live("/uploads/:license_id", UploadsLive.Index, :index)
        live("/payments/:license_id", PaymentsLive.Index, :index)
        live("/summary/:license_id", SummaryLive.Index, :index)
      end

      scope "/license", Auth do
        live("/categories", LicenseCategoriesLive.Index, :index)
        live("/creation", LicenseLive.Index, :index)
        live("/summary/:id", LicenseSummaryLive.Index, :index)
        live("/management/:id", LicenseMagementLive.Index, :index)
        live("/client/maintenance", ClientManagementLive.Index, :index)

        live("/registration/:license_id", LicenceRegistrationLive.Index, :index)
        live("/registration/:license_id/:parent_id", LicenceRegistrationLive.Index, :index)
        live("/change/:license_id", LicenceChangeLive.Index, :index)

        live("/applications", LicenceApplicationsLive.Index, :index)
        live("/applications/:path", LicenceApplicationsLive.Index, :index)
        live("/main_applications/:path/:id", LicenceApplicationsLive.Index, :index)
        live("/approval_report/:id", LicenceApplicationsLive.ApprovalReport, :index)
        live("/applications/conditional", ConditionalApplicationsLive.Index, :index)
        live("/applications/issued", IssuedApplicationsLive.Index, :index)
        live("/applications/view/:id", ViewApplicationsLive.Index, :index)
        live("/applications/details/:id", ApplicationDetailsLive.Index, :index)
        live("/applications/representative/:id", ViewApplicationsLive.Representative, :index)
        live("/applications/finance/:id", FinanceLive.Index, :index)
        live("/conditions/:license_id", ConditionTrackingLive.Index, :index)
        live("/updates", LicensesUpdateLive.Index, :index)

        get("/template/:id/:application_id", LicenceTemplateController, :show)
        get("/certificate/:id", LicenceCertificateController, :show)

        live("/:page", CategoryLandingLive.Index, :index)

        live("/applications/detail/:id", ApplicationDetails1Live.Index, :index)
        live("/applications/summary/:id", ApplicationSummaryLive.Index, :index)
        live("/applications/evaluators/:id", ApplicationEvaluatorsLive.Index, :index)
        live("/applications/evaluators/upload/:id", UploadEvaluatorsLive.Index, :index)
        live("/applications/evaluation/:id", EvaluatorsLive.EvaluationLive.Index, :index)
      end

      scope "/", Auth do
        live("/client/maintenance", ClientManagementLive.Index, :index)

        live("/notifications", NotificationLive.Index, :index)
        live("/maintenance/committee", CommitteeLive.Index, :index)

        live("/registration/:license_id", LicenceRegistrationLive.Index, :index)
        live("/applications", LicenceApplicationsLive.Index, :index)
        live("/applications/view/:id", ViewApplicationsLive.Index, :index)

        live("/client/applications/:status", ClientApplicationsLive.Index, :index)

        live("/client/representatives/management", AssociatesManagementLive.Index, :index)
        live("/client/representatives/new", NewAssociatesLive.Index, :index)
        live("/client/representatives/upload", UploadAssociatesLive.Index, :index)
        live("/client/representatives/pending", PendingAssociatesLive.Index, :index)
      end

      scope "/message" do
        live("/drafts", MessageDraftLive.Index, :index)
      end

      scope "/report" do
        live("/issued_licenses", IssuedLicensesLive.Index, :index)
        live("/sandbox_scores/:id", Auth.Reports.SandboxScoresLive.Index, :index)
      end

      scope "/steps" do
        live("/registration/page", PageLive.Index, :index)
        live("/step/mapping", LicenceStepMappingLive.Index, :index)
      end

      # AppWeb.Authenticated.Authentication.PermissionsLive.Department
      scope "/roles", Authenticated.Authentication do
        live("/system_roles", DepartmentRoleLive.Index, :index)
        live("/access_roles/:role", AccessRoleLive.Index, :index)
        live("/access_roles/:role/view", AccessRoleLive.View, :view)
        live("/access_roles/:role/add", AccessRoleLive.Add, :add)
        live("/permissions/:role/department", PermissionsLive.Department, :index)
        live("/permissions/:role/access", PermissionsLive.Access, :index)
        live("/permissions/:role/user", PermissionsLive.User, :index)
      end

      scope "/settings", Authenticated do
        live("/home", SettingsLive.Index, :index)
        live("/error_codes", ErrorLive.Index, :index)
      end
    end
  end

  # Other scopes may use custom stacks.
  # scope "/api", AppWeb do
  #   pipe_through :api
  # end

  # Enable LiveDashboard and Swoosh mailbox preview in development
  if Application.compile_env(:app, :dev_routes) do
    # If you want to use the LiveDashboard in production, you should put
    # it behind authentication and allow only admins to access it.
    # If your application does not have an admins-only section yet,
    # you can use Plug.BasicAuth to set up some basic authentication
    # as long as you are also using SSL (which you should anyway).
    import Phoenix.LiveDashboard.Router

    scope "/dev" do
      pipe_through(:browser)
      live_dashboard("/dashboard", metrics: AppWeb.Telemetry)
      forward("/mailbox", Plug.Swoosh.MailboxPreview)
    end
  end
end

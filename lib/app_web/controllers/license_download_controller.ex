defmodule AppWeb.LicenseDownloadController do
  use AppWeb, :controller

  def download(conn, record) do
    with {:ok, page} <- fetch_page(record["id"]),
         {:ok, html_content} <- render_html_content(page),
         {:ok, docx_content} <- convert_html_to_docx(html_content) do
      conn
      |> put_resp_content_type(
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      )
      |> put_resp_header(
        "content-disposition",
        ~s(attachment; filename="#{page.record_name}.docx")
      )
      |> send_resp(200, docx_content)
    else
      {:error, reason} ->
        conn
        |> put_status(:internal_server_error)
        |> send_resp(500, "Error generating document: #{reason}")
    end
  end

  defp fetch_page(id) do
    case App.Licenses.license_id(id) do
      nil -> {:error, "Record not found"}
      page -> {:ok, page}
    end
  end

  defp render_html_content(page) do
    html = page.summary_user_draft

    base_data = %{
      "meeting_number" => page.meeting_number,
      "license.security_act_no" => page.license.security_act_no,
      "inserted_at" => Calendar.strftime(page.inserted_at, "%d %B, %Y")
    }

    data =
      if page.record_name do
        base_data
      else
        with {:ok, dob} <- date_of_birth(page.data["date_of_birth"]) do
          Map.merge(base_data, %{
            "record_status" => page.status,
            "date_of_birth" => calculate_age(dob)
          })
        else
          {:error, reason} -> {:error, reason}
        end
      end

    case data do
      {:error, reason} -> {:error, reason}
      data -> {:ok, App.SummaryDrafts.render(html, Map.merge(page.data, data), page)}
    end
  end

  defp convert_html_to_docx(html_content) do
    temp_dir = System.tmp_dir!()
    temp_html_path = Path.join(temp_dir, "#{UUID.uuid4()}.html")
    temp_docx_path = Path.join(temp_dir, "#{UUID.uuid4()}.docx")

    try do
      File.write!(temp_html_path, html_content)

      case System.cmd("pandoc", ["-f", "html", "-t", "docx", "-o", temp_docx_path, temp_html_path]) do
        {_, 0} ->
          docx_content = File.read!(temp_docx_path)
          {:ok, docx_content}

        {error, _exit_code} ->
          {:error, "Document conversion failed: #{error}"}
      end
    rescue
      e in File.Error ->
        {:error, "File operation failed: #{e}"}
    catch
      :exit, reason ->
        {:error, "Document process exited: #{reason}"}
    after
      File.rm(temp_html_path)
      File.rm(temp_docx_path)
    end
  end

  defp date_of_birth(dob) when is_binary(dob) do
    case Date.from_iso8601(dob) do
      {:ok, date} -> {:ok, date}
      {:error, _} -> {:error, "Invalid date of birth"}
    end
  end

  defp date_of_birth(_), do: {:error, "Missing date of birth"}

  defp calculate_age(dob) do
    today = Date.utc_today()
    years = today.year - dob.year
    if {today.month, today.day} < {dob.month, dob.day}, do: years - 1, else: years
  end
end

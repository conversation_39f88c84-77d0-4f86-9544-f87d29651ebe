defmodule AppWeb.FileController do
  use AppWeb, :controller
  alias App.Service.FileHandler

  def read(conn, params) do
    FileHandler.read_file(params["uri"])
    |> case do
      {:ok, path} ->
        conn
        |> put_resp_header("content-type", MIME.from_path(path))
        |> put_resp_header("accept-ranges", "bytes")
        |> send_file(200, path)

      _ ->
        json(conn, %{
          "status" => false,
          "message" => "Something went wrong, please try again later"
        })
    end
  end

  def remove(conn, params) do
    FileHandler.remove_file(params["uri"])

    json(conn, %{
      "status" => true,
      "message" => "Success"
    })
  end
end

defmodule AppWeb.UserSessionController do
  use App<PERSON>eb, :controller
  alias App.Users, as: Accounts
  alias AppWeb.UserAuth

  def create(conn, %{"_action" => "registered"} = params),
    do: create(conn, params, "Account created successfully!")

  def create(conn, %{"_action" => "password_updated"} = params) do
    conn
    |> create(params, "Password updated successfully!")
  end

  def create(conn, params), do: create(conn, params, "Welcome back!")

  defp create(conn, %{"user" => user_params}, _info) do
    %{"email" => email, "password" => password} = user_params

    if user = Accounts.get_user_by_email_and_password(email, password) do
      conn
      |> UserAuth.log_in_user(user, user_params)
    else
      # In order to prevent user enumeration attacks, don't disclose whether the email is registered.
      conn
      |> put_flash(:error, "Invalid email or password")
      |> put_flash(:email, String.slice(email, 0, 160))
      |> redirect(to: ~p"/users/log_in")
    end
  end

  def delete(conn, _params) do
    conn
    |> UserAuth.log_out_user()
  end

  def session(conn, params) do
    case Phoenix.Token.verify(conn, "userSessionAuthentication", params["token"], max_age: 60_000) do
      {:ok, data} ->
        user = Accounts.get_user!(data["user_id"])
        Accounts.register_device(conn, data, user)
        UserAuth.log_in_user(conn, user, params)

      {:error, _reason} ->
        conn
        |> redirect(to: "/")
    end
  end
end

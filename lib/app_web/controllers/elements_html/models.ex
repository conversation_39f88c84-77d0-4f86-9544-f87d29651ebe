defmodule Model do
  use Phoenix.Component
  #  alias <PERSON>.LiveView.JS
  import AppWeb.Gettext
  import Phoenix.HTML
  alias AppWeb.CoreComponents

  def name(assigns) do
    ~H"""
    ...
    """
  end

  def small(assigns) do
    assigns =
      assigns
      |> assign_new(:return_to, fn -> "close_small_modal" end)
      |> assign_new(:title, fn -> nil end)
      # Added size control
      |> assign_new(:size, fn -> "medium" end)

    ~H"""
    <div
      id={@id}
      phx-mounted={@show && CoreComponents.show_modal(@id)}
      phx-remove={CoreComponents.hide_modal(@id)}
      class="relative z-50 hidden"
    >
      <!-- Modal overlay -->
      <div id={"#{@id}-bg"} class="fixed inset-0 bg-black/50 transition-opacity" aria-hidden="true">
      </div>
      <!-- Modal content -->
      <div
        style="backdrop-filter: blur(8px); background-color: rgba(0, 0, 0, 0.4);"
        class="fixed inset-0 flex items-center justify-center overflow-y-auto"
        aria-labelledby={"#{@id}-title"}
        aria-describedby={"#{@id}-description"}
        role="dialog"
        aria-modal="true"
        tabindex="0"
      >
        <div
          class={
            "relative p-6 m-3 bg-white rounded-lg shadow-xl transform transition-all w-full " <>
            case @size do
              "x-small" -> "max-w-md"
              "small" -> "max-w-lg"
              "medium" -> "max-w-2xl"
              "large" -> "max-w-4xl"
              _ -> "max-w-2xl"
            end
          }
          id="modal-content"
          phx-click-away={@return_to}
          phx-window-keydown={@return_to}
          phx-key="escape"
        >
          <!-- Title and close button -->
          <div class="flex justify-between items-center border-b pb-3">
            <div class="text-lg font-semibold text-gray-800">
              <%= @title || "Modal Title" %>
            </div>

            <button
              phx-click={@return_to}
              type="button"
              class="p-2 text-gray-500 hover:text-gray-700 transition-opacity focus:outline-none"
              aria-label={gettext("close")}
            >
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fill-rule="evenodd"
                  d="M10 9.293l4.293-4.293a1 1 0 011.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414L10 8.586z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </div>
          <!-- Modal content -->
          <div class="mt-4 text-gray-600">
            <%= render_slot(@inner_block) %>
          </div>
        </div>
      </div>
    </div>
    """
  end

  def modal(assigns) do
    assigns =
      assign_new(assigns, :return_to, fn -> "close_small_modal" end)
      |> assign_new(:title, fn -> nil end)
      # Default size to 's'
      |> assign_new(:size, fn -> "s" end)

    ~H"""
    <div
      id={@id}
      phx-mounted={@show && CoreComponents.show_modal(@id)}
      phx-remove={CoreComponents.hide_modal(@id)}
      class="relative z-50 hidden"
    >
      <div
        id={"#{@id}-bg"}
        class="fixed inset-0 bg-black-50/90 transition-opacity"
        aria-hidden="true"
      />
      <div
        style="backdrop-filter: blur(5px);background-color: rgba(0, 0, 0, 0.4);"
        class="fixed inset-0 overflow-y-auto"
        aria-labelledby={"#{@id}-title"}
        aria-describedby={"#{@id}-description"}
        role="dialog"
        aria-modal="true"
        tabindex="0"
      >
        <div class="flex min-h-full items-center justify-center">
          <div
            id="modal-content"
            class={"inline-block overflow px-4 pt-5 pb-4 text-left align-bottom bg-white rounded-lg shadow-xl transition-all transform phx-modal-content sm:my-8 sm:align-middle #{modal_size(@size)} sm:p-6"}
            role="dialog"
            aria-modal="true"
            aria-labelledby="modal-headline"
            phx-click-away={@return_to}
            phx-window-keydown={@return_to}
            phx-key="escape"
          >
            <div class="w-full max-w-3xl">
              <div class="absolute top-6 left-5"><%= @title %></div>

              <div class="absolute top-6 right-5">
                <button
                  phx-click={@return_to}
                  type="button"
                  class="-m-3 flex-none p-3 opacity-20 hover:opacity-40"
                  aria-label={gettext("close")}
                >
                  <span class="hero-x-mark-solid h-5 w-5 stroke-current" />
                </button>
              </div>
            </div>
            <%= render_slot(@inner_block) %>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp modal_size("xxl"), do: "sm:max-w-7xl sm:w-11/12"
  defp modal_size("xl"), do: "sm:max-w-5xl sm:w-10/12"
  defp modal_size("l"), do: "sm:max-w-3xl sm:w-8/12"
  defp modal_size("s"), do: "sm:max-w-xl sm:w-4/12"

  def fullscreen1(assigns) do
    assigns =
      assign_new(assigns, :return_to, fn -> "close_small_modal" end)
      |> assign_new(:title, fn -> nil end)

    ~H"""
    <div
      id={@id}
      phx-mounted={@show && CoreComponents.show_modal(@id)}
      phx-remove={CoreComponents.hide_modal(@id)}
      class="relative z-50 hidden"
    >
      <div
        id={"#{@id}-bg"}
        class="fixed inset-0 bg-black-50/90 transition-opacity"
        aria-hidden="true"
      />
      <div
        style="backdrop-filter: blur(5px);background-color: rgba(0, 0, 0, 0.4);"
        class="fixed inset-0 overflow-y-auto"
        aria-labelledby={"#{@id}-title"}
        aria-describedby={"#{@id}-description"}
        role="dialog"
        aria-modal="true"
        tabindex="0"
      >
        <div class="flex min-h-full items-center justify-center">
          <div
            id="modal-content"
            class="inline-block max-h-90 overflow-y-auto px-4 pt-5 pb-4 text-left align-bottom bg-white rounded-lg shadow-xl transition-all transform phx-modal-content sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full sm:p-6"
            role="dialog"
            aria-modal="true"
            aria-labelledby="modal-headline"
            phx-click-away={@return_to}
            phx-window-keydown={@return_to}
            phx-key="escape"
          >
            <div class="w-full max-w-3xl">
              <div class="absolute top-6 left-5"><%= @title %></div>

              <div class="absolute top-6 right-5">
                <button
                  phx-click={@return_to}
                  type="button"
                  class="-m-3 flex-none p-3 opacity-20 hover:opacity-40"
                  aria-label={gettext("close")}
                >
                  <span class="hero-x-mark-solid h-5 w-5 stroke-current" />
                </button>
              </div>
            </div>
            <%= render_slot(@inner_block) %>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @doc ~S"""
  Renders a table with generic styling.

  ## Examples

      <Model.fullscreen title="Edit" />
  """
  attr(:title, :string, default: nil)
  attr(:id, :string)
  attr(:show, :boolean)
  attr(:return_to, :string, default: "close_small_modal")
  slot(:inner_block, required: true)

  def fullscreen(assigns) do
    ~H"""
    <div
      id={@id}
      phx-mounted={@show && CoreComponents.show_modal(@id)}
      phx-remove={CoreComponents.hide_modal(@id)}
      style="backdrop-filter: blur(5px)"
      class="fixed left-0 top-0 z-[99999] flex h-screen w-full items-center justify-center bg-black/40 p-4 xl:p-8 visible opacity-100"
    >
      <div class="flex h-full w-full flex-col rounded-lg">
        <div class="z-[99999] mb-4 flex w-full flex-col items-center justify-between rounded-lg border border-[#d8dfff] bg-white px-3 py-4 sm:px-5 md:flex-row lg:px-8">
          <div class="mb-3 md:mb-0">
            <span class="pl-4 text-lg font-semibold text-black md:text-base lg:text-lg">
              <%= @title %>
            </span>
            <br /> <span class="pl-4 text-sm font-semibold text-black lg:text-sm"></span>
          </div>

          <div class="flex w-full justify-between md:w-auto md:justify-end">
            <button
              phx-click={@return_to}
              class="ml-4 flex h-10 w-10 items-center justify-center rounded-lg border border-[#d8dfff] text-body-color duration-300"
            >
              <svg viewBox="0 0 25 25" class="h-4 w-4 fill-current">
                <path d="M13.9489 12.4742L24.7499 1.63422C25.0228 1.36127 25.0228 0.932348 24.7499 0.659399C24.4769 0.38645 24.048 0.38645 23.7751 0.659399L12.9741 11.4994L2.13412 0.698392C1.86117 0.425443 1.43225 0.425443 1.1593 0.698392C0.886353 0.971341 0.886353 1.40026 1.1593 1.67321L11.9993 12.4742L1.19829 23.3142C0.925345 23.5871 0.925345 24.016 1.19829 24.289C1.31527 24.4059 1.51024 24.4839 1.66621 24.4839C1.82218 24.4839 2.01714 24.4059 2.13412 24.289L12.9741 13.449L23.7751 24.289C23.892 24.4059 24.087 24.4839 24.243 24.4839C24.3989 24.4839 24.5939 24.4059 24.7109 24.289C24.9838 24.016 24.9838 23.5871 24.7109 23.3142L13.9489 12.4742Z">
                </path>
              </svg>
            </button>
          </div>
        </div>

        <div class="h-full flex-1 overflow-y-auto rounded-lg bg-[#F5F8FF]">
          <div class="h-full rounded-lg p-4">
            <%= render_slot(@inner_block) %>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @doc ~S"""
  Renders a table with generic styling.

  ## Examples

      <Model.code_preview title="Edit" />
  """
  attr(:title, :string, default: "Code")
  attr(:code, :string, default: "{}")
  attr(:return_to, :string, default: "close_code_preview_model")

  def code_preview(assigns) do
    ~H"""
    <div
      id={@id}
      phx-mounted={@show && CoreComponents.show_modal(@id)}
      phx-remove={CoreComponents.hide_modal(@id)}
      class="relative z-50 hidden"
    >
      <div
        id={"#{@id}-bg"}
        class="fixed inset-0 bg-black-50/90 transition-opacity"
        aria-hidden="true"
      />
      <div
        style="backdrop-filter: blur(5px);background-color: rgba(0, 0, 0, 0.4);"
        class="fixed inset-0 overflow-y-auto"
        aria-labelledby={"#{@id}-title"}
        aria-describedby={"#{@id}-description"}
        role="dialog"
        aria-modal="true"
        tabindex="0"
      >
        <div class="flex min-h-full  items-center justify-center">
          <div
            id="modal-content"
            class="inline-block overflow px-4 pt-5 pb-4 text-left align-bottom bg-white rounded-lg shadow-xl transition-all transform phx-modal-content sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full sm:p-6"
            role="dialog"
            aria-modal="true"
            aria-labelledby="modal-headline"
            phx-click-away={@return_to}
            phx-window-keydown={@return_to}
            phx-key="escape"
          >
            <div class="w-full max-w-3xl p-4 sm:p-6 lg:py-8">
              <div class="absolute top-6 left-5"><%= @title %></div>

              <div class="absolute top-6 right-5">
                <button
                  phx-click={@return_to}
                  type="button"
                  class="-m-3 flex-none p-3 opacity-20 hover:opacity-40"
                  aria-label={gettext("close")}
                >
                  <span class="hero-x-mark-solid h-5 w-5 stroke-current" />
                </button>
              </div>
            </div>

            <article class="prose prose-img:rounded-xl prose-headings:underline prose-a:text-blue-600 w-full max-w-3xl">
              <%= if !String.contains?(String.downcase(@title), "endpoint") do %>
                <.preview_code code={@code} />
              <% else %>
                <.preview_code_text code={@code} />
              <% end %>
            </article>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp preview_code(assigns) do
    ~H"""
    <pre x-data={"{ message: JSON.stringify(#{String.trim(@code)}, undefined, 2) }"} x-text="message"></pre>
    """
  end

  defp preview_code_text(assigns) do
    ~H"""
    <pre><%= String.trim(@code) %></pre>
    """
  end

  @doc ~S"""
  Renders a table with generic styling.

  ## Examples

      <Model.confirmation_model
        :if={@live_action == :confirm}
        show
        id="confirmation_model-modal"
        model_title={@confirmation_model_title}
        body_text={@confirmation_model_text}
        agree_function={@confirmation_model_agree}
        reject_function={@confirmation_model_reject}
        params={Jason.encode!(@confirmation_model_params)}
        icon={@confirmation_model_icon}
      />
  """
  attr(:model_title, :string, required: true)
  attr(:id, :string)
  attr(:show, :boolean)
  attr(:body_text, :string, default: "")
  attr(:params, :string, default: "")
  attr(:reject_text, :string, default: "Cancel")
  attr(:agree_text, :string, default: "Proceed")
  attr(:reject_function, :string, default: "reject")
  attr(:agree_function, :string, default: "agree")
  attr(:icon, :string, default: "exclamation_circle")

  def confirmation_model(assigns) do
    ~H"""
    <div
      id={@id}
      phx-mounted={@show && CoreComponents.show_modal(@id)}
      phx-remove={CoreComponents.hide_modal(@id)}
      class="relative z-50"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

      <div
        class="fixed inset-0 z-10 overflow-y-auto"
        style="display: block; padding-right: 15px; background-color: rgba(0, 0, 0, 0.4); backdrop-filter: blur(5px);"
      >
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
            <div>
              <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <%= if @icon == "check" do %>
                  <span class="hero-check h-6 w-6 text-green-600" />
                <% end %>

                <%= if @icon == "exclamation_circle" do %>
                  <span class="hero-exclamation-circle h-6 w-6 text-green-600" />
                <% end %>

                <%= if @icon == "loading" do %>
                  <div class="animate-spin">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" id="loading">
                      <path d="M27.02 22.82a.182.182 1080 1 0 .364 0 .182.182 1080 1 0-.364 0zm-4.018 4.146a.362.362 1080 1 0 .724 0 .362.362 1080 1 0-.724 0zM17.586 29.1a.544.544 1080 1 0 1.088 0 .544.544 1080 1 0-1.088 0zm-5.83-.286a.724.724 1080 1 0 1.448 0 .724.724 1080 1 0-1.448 0zM6.584 26.16a.906.906 1080 1 0 1.812 0 .906.906 1080 1 0-1.812 0zm-3.582-4.512a1.088 1.088 1080 1 0 2.176 0 1.088 1.088 1080 1 0-2.176 0zm-1.344-5.54a1.268 1.268 1080 1 0 2.536 0 1.268 1.268 1080 1 0-2.536 0zm1.106-5.504a1.45 1.45 1080 1 0 2.9 0 1.45 1.45 1080 1 0-2.9 0zm3.318-4.438a1.632 1.632 1080 1 0 3.264 0 1.632 1.632 1080 1 0-3.264 0zm4.872-2.542a1.812 1.812 1080 1 0 3.624 0 1.812 1.812 1080 1 0-3.624 0zm5.472-.158a1.994 1.994 1080 1 0 3.988 0 1.994 1.994 1080 1 0-3.988 0zm5.01 2.254a2.174 2.174 1080 1 0 4.348 0 2.174 2.174 1080 1 0-4.348 0zm3.56 4.234a2.356 2.356 1080 1 0 4.712 0 2.356 2.356 1080 1 0-4.712 0zm1.416 5.484a2.538 2.538 1080 1 0 5.076 0 2.538 2.538 1080 1 0-5.076 0z">
                      </path>
                    </svg>
                  </div>
                <% end %>
              </div>

              <div class="mt-3 text-center sm:mt-5">
                <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-title">
                  <%= @model_title %>
                </h3>

                <div class="mt-2">
                  <p class="text-sm text-gray-500"><%= @body_text %></p>
                </div>
              </div>
            </div>

            <%= if @icon != "loading" do %>
              <div class="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                <button
                  type="button"
                  phx-click={@agree_function}
                  phx-value-params={@params}
                  class="inline-flex w-full justify-center rounded-md border border-transparent bg-brand-1 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-brand-2 focus:ring-offset-2 sm:col-start-2 sm:text-sm"
                >
                  <%= @agree_text %>
                </button>

                <button
                  type="button"
                  phx-click={@reject_function}
                  class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-brand-2 focus:ring-offset-2 sm:col-start-1 sm:mt-0 sm:text-sm"
                >
                  <%= @reject_text %>
                </button>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    """
  end

  attr(:model_title, :string, required: true)
  attr(:id, :string)
  attr(:show, :boolean)
  attr(:body_text, :string, default: "")
  attr(:params, :string, default: "")
  attr(:reject_text, :string, default: "Cancel")
  attr(:agree_text, :string, default: "Proceed")
  attr(:reject_function, :string, default: "reject")
  attr(:agree_function, :string, default: "agree")
  attr(:icon, :string, default: "exclamation_circle")

  def confirmation_model_component(assigns) do
    ~H"""
    <div
      id={@id}
      phx-mounted={@show && CoreComponents.show_modal(@id)}
      phx-remove={CoreComponents.hide_modal(@id)}
      class="relative z-50"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

      <div
        class="fixed inset-0 z-10 overflow-y-auto"
        style="display: block; padding-right: 15px; background-color: rgba(0, 0, 0, 0.4); backdrop-filter: blur(5px);"
      >
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
            <div>
              <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <%= if @icon == "check" do %>
                  <span class="hero-check h-6 w-6 text-green-600" />
                <% end %>

                <%= if @icon == "exclamation_circle" do %>
                  <span class="hero-exclamation-circle h-6 w-6 text-green-600" />
                <% end %>

                <%= if @icon == "loading" do %>
                  <div class="animate-spin">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" id="loading">
                      <path d="M27.02 22.82a.182.182 1080 1 0 .364 0 .182.182 1080 1 0-.364 0zm-4.018 4.146a.362.362 1080 1 0 .724 0 .362.362 1080 1 0-.724 0zM17.586 29.1a.544.544 1080 1 0 1.088 0 .544.544 1080 1 0-1.088 0zm-5.83-.286a.724.724 1080 1 0 1.448 0 .724.724 1080 1 0-1.448 0zM6.584 26.16a.906.906 1080 1 0 1.812 0 .906.906 1080 1 0-1.812 0zm-3.582-4.512a1.088 1.088 1080 1 0 2.176 0 1.088 1.088 1080 1 0-2.176 0zm-1.344-5.54a1.268 1.268 1080 1 0 2.536 0 1.268 1.268 1080 1 0-2.536 0zm1.106-5.504a1.45 1.45 1080 1 0 2.9 0 1.45 1.45 1080 1 0-2.9 0zm3.318-4.438a1.632 1.632 1080 1 0 3.264 0 1.632 1.632 1080 1 0-3.264 0zm4.872-2.542a1.812 1.812 1080 1 0 3.624 0 1.812 1.812 1080 1 0-3.624 0zm5.472-.158a1.994 1.994 1080 1 0 3.988 0 1.994 1.994 1080 1 0-3.988 0zm5.01 2.254a2.174 2.174 1080 1 0 4.348 0 2.174 2.174 1080 1 0-4.348 0zm3.56 4.234a2.356 2.356 1080 1 0 4.712 0 2.356 2.356 1080 1 0-4.712 0zm1.416 5.484a2.538 2.538 1080 1 0 5.076 0 2.538 2.538 1080 1 0-5.076 0z">
                      </path>
                    </svg>
                  </div>
                <% end %>
              </div>

              <div class="mt-3 text-center sm:mt-5">
                <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-title">
                  <%= @model_title %>
                </h3>

                <div class="mt-2">
                  <p class="text-sm text-gray-500"><%= @body_text %></p>
                </div>
              </div>
            </div>

            <%= if @icon != "loading" do %>
              <div class="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                <button
                  type="button"
                  phx-target={@myself}
                  phx-click={@agree_function}
                  phx-value-params={@params}
                  class="inline-flex w-full justify-center rounded-md border border-transparent bg-brand-1 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-brand-2 focus:ring-offset-2 sm:col-start-2 sm:text-sm"
                >
                  <%= @agree_text %>
                </button>

                <button
                  type="button"
                  phx-target={@myself}
                  phx-click={@reject_function}
                  class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-brand-2 focus:ring-offset-2 sm:col-start-1 sm:mt-0 sm:text-sm"
                >
                  <%= @reject_text %>
                </button>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @doc ~S"""
  Renders a table with generic styling.

  ## Examples

      <Model.confirmation_with_des
        :if={@live_action == :confirm}
        show
        id="confirmation_model-modal"
        model_title={@confirmation_model_title}
        body_text={@confirmation_model_text}
        agree_function={@confirmation_model_agree}
        reject_function={@confirmation_model_reject}
        params={Jason.encode!(@confirmation_model_params)}
        icon={@confirmation_model_icon}
      />
  """
  attr(:model_title, :string, required: true)
  attr(:id, :string)
  attr(:show, :boolean)
  attr(:body_text, :string, default: "")
  attr(:params, :string, default: "")
  attr(:reject_text, :string, default: "Cancel")
  attr(:agree_text, :string, default: "Proceed")
  attr(:reject_function, :string, default: "reject")
  attr(:agree_function, :string, default: "agree")
  attr(:icon, :string, default: "exclamation_circle")

  def confirmation_with_des(assigns) do
    ~H"""
    <div
      id={@id}
      phx-mounted={@show && CoreComponents.show_modal(@id)}
      phx-remove={CoreComponents.hide_modal(@id)}
      class="relative z-50"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

      <div
        class="fixed inset-0 z-10 overflow-y-auto"
        style="display: block; padding-right: 15px; background-color: rgba(0, 0, 0, 0.4); backdrop-filter: blur(5px);"
      >
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
            <div>
              <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <%= if @icon == "check" do %>
                  <span class="hero-check h-6 w-6 text-green-600" />
                <% end %>

                <%= if @icon == "exclamation_circle" do %>
                  <span class="hero-exclamation-circle h-6 w-6 text-green-600" />
                <% end %>

                <%= if @icon == "loading" do %>
                  <div class="animate-spin">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" id="loading">
                      <path d="M27.02 22.82a.182.182 1080 1 0 .364 0 .182.182 1080 1 0-.364 0zm-4.018 4.146a.362.362 1080 1 0 .724 0 .362.362 1080 1 0-.724 0zM17.586 29.1a.544.544 1080 1 0 1.088 0 .544.544 1080 1 0-1.088 0zm-5.83-.286a.724.724 1080 1 0 1.448 0 .724.724 1080 1 0-1.448 0zM6.584 26.16a.906.906 1080 1 0 1.812 0 .906.906 1080 1 0-1.812 0zm-3.582-4.512a1.088 1.088 1080 1 0 2.176 0 1.088 1.088 1080 1 0-2.176 0zm-1.344-5.54a1.268 1.268 1080 1 0 2.536 0 1.268 1.268 1080 1 0-2.536 0zm1.106-5.504a1.45 1.45 1080 1 0 2.9 0 1.45 1.45 1080 1 0-2.9 0zm3.318-4.438a1.632 1.632 1080 1 0 3.264 0 1.632 1.632 1080 1 0-3.264 0zm4.872-2.542a1.812 1.812 1080 1 0 3.624 0 1.812 1.812 1080 1 0-3.624 0zm5.472-.158a1.994 1.994 1080 1 0 3.988 0 1.994 1.994 1080 1 0-3.988 0zm5.01 2.254a2.174 2.174 1080 1 0 4.348 0 2.174 2.174 1080 1 0-4.348 0zm3.56 4.234a2.356 2.356 1080 1 0 4.712 0 2.356 2.356 1080 1 0-4.712 0zm1.416 5.484a2.538 2.538 1080 1 0 5.076 0 2.538 2.538 1080 1 0-5.076 0z">
                      </path>
                    </svg>
                  </div>
                <% end %>
              </div>

              <div class="mt-3 text-center sm:mt-5">
                <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-title">
                  <%= @model_title %>
                </h3>

                <div class="mt-2">
                  <p class="text-sm text-gray-500"><%= raw(@body_text) %></p>
                </div>
              </div>
            </div>

            <form phx-submit={@agree_function}>
              <%= if @icon != "loading" do %>
                <input type="hidden" name="params" value={@params} required /> <textarea
                  class="w-full bg-gray-100 text-gray-800 rounded-lg p-2 mt-2 border border-gray-300"
                  placeholder="Enter Reason Here"
                  name="reason"
                  required
                ></textarea>
                <div class="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                  <button
                    type="submit"
                    phx-value-params={@params}
                    class="inline-flex w-full justify-center rounded-md border border-transparent bg-brand-1 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-brand-2 focus:ring-offset-2 sm:col-start-2 sm:text-sm"
                  >
                    <%= @agree_text %>
                  </button>

                  <button
                    type="button"
                    phx-click={@reject_function}
                    class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-brand-2 focus:ring-offset-2 sm:col-start-1 sm:mt-0 sm:text-sm"
                  >
                    <%= @reject_text %>
                  </button>
                </div>
              <% end %>
            </form>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # Amount confirmation model

  attr(:model_title, :string, required: true)
  attr(:id, :string)
  attr(:show, :boolean)
  attr(:body_text, :string, default: "")
  attr(:params, :string, default: "")
  attr(:reject_text, :string, default: "Cancel")
  attr(:agree_text, :string, default: "Proceed")
  attr(:reject_function, :string, default: "reject")
  attr(:agree_function, :string, default: "agree")
  attr(:icon, :string, default: "exclamation_circle")

  def confirmation_amount(assigns) do
    ~H"""
    <div
      id={@id}
      phx-mounted={@show && CoreComponents.show_modal(@id)}
      phx-remove={CoreComponents.hide_modal(@id)}
      class="relative z-50"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

      <div
        class="fixed inset-0 z-10 overflow-y-auto"
        style="display: block; padding-right: 15px; background-color: rgba(0, 0, 0, 0.4); backdrop-filter: blur(5px);"
      >
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
            <div>
              <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <%= if @icon == "check" do %>
                  <span class="hero-check h-6 w-6 text-green-600" />
                <% end %>

                <%= if @icon == "exclamation_circle" do %>
                  <span class="hero-exclamation-circle h-6 w-6 text-green-600" />
                <% end %>

                <%= if @icon == "loading" do %>
                  <div class="animate-spin">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" id="loading">
                      <path d="M27.02 22.82a.182.182 1080 1 0 .364 0 .182.182 1080 1 0-.364 0zm-4.018 4.146a.362.362 1080 1 0 .724 0 .362.362 1080 1 0-.724 0zM17.586 29.1a.544.544 1080 1 0 1.088 0 .544.544 1080 1 0-1.088 0zm-5.83-.286a.724.724 1080 1 0 1.448 0 .724.724 1080 1 0-1.448 0zM6.584 26.16a.906.906 1080 1 0 1.812 0 .906.906 1080 1 0-1.812 0zm-3.582-4.512a1.088 1.088 1080 1 0 2.176 0 1.088 1.088 1080 1 0-2.176 0zm-1.344-5.54a1.268 1.268 1080 1 0 2.536 0 1.268 1.268 1080 1 0-2.536 0zm1.106-5.504a1.45 1.45 1080 1 0 2.9 0 1.45 1.45 1080 1 0-2.9 0zm3.318-4.438a1.632 1.632 1080 1 0 3.264 0 1.632 1.632 1080 1 0-3.264 0zm4.872-2.542a1.812 1.812 1080 1 0 3.624 0 1.812 1.812 1080 1 0-3.624 0zm5.472-.158a1.994 1.994 1080 1 0 3.988 0 1.994 1.994 1080 1 0-3.988 0zm5.01 2.254a2.174 2.174 1080 1 0 4.348 0 2.174 2.174 1080 1 0-4.348 0zm3.56 4.234a2.356 2.356 1080 1 0 4.712 0 2.356 2.356 1080 1 0-4.712 0zm1.416 5.484a2.538 2.538 1080 1 0 5.076 0 2.538 2.538 1080 1 0-5.076 0z">
                      </path>
                    </svg>
                  </div>
                <% end %>
              </div>

              <div class="mt-3 text-center sm:mt-5">
                <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-title">
                  <%= @model_title %>
                </h3>

                <div class="mt-2">
                  <p class="text-sm text-gray-500"><%= raw(@body_text) %></p>
                </div>
              </div>
            </div>

            <form phx-submit={@agree_function}>
              <%= if @icon != "loading" do %>
                <input type="hidden" name="params" value={@params} required />
                <input
                  phx-hook="validateAmountsHook"
                  id="paidAmount"
                  type="text"
                  class="w-full bg-gray-100 text-gray-800 rounded-lg p-2 mt-2 border border-gray-300"
                  placeholder="Enter Amount Paid Here"
                  name="amount_paid"
                  required
                />
                <div class="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                  <button
                    type="submit"
                    phx-value-params={@params}
                    class="inline-flex w-full justify-center rounded-md border border-transparent bg-brand-1 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-brand-2 focus:ring-offset-2 sm:col-start-2 sm:text-sm"
                  >
                    <%= @agree_text %>
                  </button>

                  <button
                    type="button"
                    phx-click={@reject_function}
                    class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-brand-2 focus:ring-offset-2 sm:col-start-1 sm:mt-0 sm:text-sm"
                  >
                    <%= @reject_text %>
                  </button>
                </div>
              <% end %>
            </form>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # Meeting Number

  attr(:model_title, :string, required: true)
  attr(:id, :string)
  attr(:show, :boolean)
  attr(:body_text, :string, default: "")
  attr(:params, :string, default: "")
  attr(:reject_text, :string, default: "Cancel")
  attr(:agree_text, :string, default: "Proceed")
  attr(:reject_function, :string, default: "reject")
  attr(:agree_function, :string, default: "agree")
  attr(:icon, :string, default: "exclamation_circle")

  def confirmation_with_comments(assigns) do
    ~H"""
    <div
      id={@id}
      phx-mounted={@show && CoreComponents.show_modal(@id)}
      phx-remove={CoreComponents.hide_modal(@id)}
      class="relative z-50"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

      <div
        class="fixed inset-0 z-10 overflow-y-auto"
        style="display: block; padding-right: 15px; background-color: rgba(0, 0, 0, 0.4); backdrop-filter: blur(5px);"
      >
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
            <div>
              <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <%= if @icon == "check" do %>
                  <span class="hero-check h-6 w-6 text-green-600" />
                <% end %>

                <%= if @icon == "exclamation_circle" do %>
                  <span class="hero-exclamation-circle h-6 w-6 text-green-600" />
                <% end %>

                <%= if @icon == "loading" do %>
                  <div class="animate-spin">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" id="loading">
                      <path d="M27.02 22.82a.182.182 1080 1 0 .364 0 .182.182 1080 1 0-.364 0zm-4.018 4.146a.362.362 1080 1 0 .724 0 .362.362 1080 1 0-.724 0zM17.586 29.1a.544.544 1080 1 0 1.088 0 .544.544 1080 1 0-1.088 0zm-5.83-.286a.724.724 1080 1 0 1.448 0 .724.724 1080 1 0-1.448 0zM6.584 26.16a.906.906 1080 1 0 1.812 0 .906.906 1080 1 0-1.812 0zm-3.582-4.512a1.088 1.088 1080 1 0 2.176 0 1.088 1.088 1080 1 0-2.176 0zm-1.344-5.54a1.268 1.268 1080 1 0 2.536 0 1.268 1.268 1080 1 0-2.536 0zm1.106-5.504a1.45 1.45 1080 1 0 2.9 0 1.45 1.45 1080 1 0-2.9 0zm3.318-4.438a1.632 1.632 1080 1 0 3.264 0 1.632 1.632 1080 1 0-3.264 0zm4.872-2.542a1.812 1.812 1080 1 0 3.624 0 1.812 1.812 1080 1 0-3.624 0zm5.472-.158a1.994 1.994 1080 1 0 3.988 0 1.994 1.994 1080 1 0-3.988 0zm5.01 2.254a2.174 2.174 1080 1 0 4.348 0 2.174 2.174 1080 1 0-4.348 0zm3.56 4.234a2.356 2.356 1080 1 0 4.712 0 2.356 2.356 1080 1 0-4.712 0zm1.416 5.484a2.538 2.538 1080 1 0 5.076 0 2.538 2.538 1080 1 0-5.076 0z">
                      </path>
                    </svg>
                  </div>
                <% end %>
              </div>

              <div class="mt-3 text-center sm:mt-5">
                <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-title">
                  <%= @model_title %>
                </h3>

                <div class="mt-2">
                  <p class="text-sm text-gray-500"><%= raw(@body_text) %></p>
                </div>
              </div>
            </div>

            <form phx-submit={@agree_function}>
              <%= if @icon != "loading" do %>
                <input type="hidden" name="params" value={@params} required /> <textarea
                  class="w-full bg-gray-100 text-gray-800 rounded-lg p-2 mt-2 border border-gray-300"
                  placeholder="Enter Comments Here"
                  name="comments"
                  required
                ></textarea>
                <div class="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                  <button
                    type="submit"
                    phx-value-params={@params}
                    class="inline-flex w-full justify-center rounded-md border border-transparent bg-brand-1 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-brand-2 focus:ring-offset-2 sm:col-start-2 sm:text-sm"
                  >
                    <%= @agree_text %>
                  </button>

                  <button
                    type="button"
                    phx-click={@reject_function}
                    class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-brand-2 focus:ring-offset-2 sm:col-start-1 sm:mt-0 sm:text-sm"
                  >
                    <%= @reject_text %>
                  </button>
                </div>
              <% end %>
            </form>
          </div>
        </div>
      </div>
    </div>
    """
  end
end

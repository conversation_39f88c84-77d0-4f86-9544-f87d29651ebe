defmodule Card do
  use Phoenix.Component
  import Phoenix.HTML
  #  alias <PERSON>.LiveView.JS
  # import AppWeb.Gettext

  def dashboard(assigns) do
    assigns =
      assign_new(assigns, :description, fn -> nil end)
      |> assign_new(:title, fn -> nil end)

    ~H"""
    <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
      <dt class="truncate text-sm font-medium text-gray-500"><%= @title %></dt>

      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900"><%= @description %></dd>
    </div>
    """
  end

  def extract_validation(assigns) do
    assigns =
      assign_new(assigns, :valid_record, fn -> 0 end)
      |> assign_new(:valid_record_callback, fn -> "view_valid_records" end)
      |> assign_new(:invalid_record, fn -> 0 end)
      |> assign_new(:total_record, fn -> 0 end)
      |> assign_new(:invalid_record_callback, fn -> "view_invalid_records" end)

    ~H"""
    <dl class="mt-5 grid grid-cols-1 gap-6 sm:grid-cols-3">
      <button
        type="button"
        phx-click={@valid_record_callback}
        class="group relative overflow-hidden rounded-xl bg-white px-6 py-5 shadow-sm ring-1 ring-gray-200 transition-all duration-200 hover:shadow-md hover:ring-green-500 hover:-translate-y-0.5"
      >
        <div class="flex items-center">
          <div class="rounded-full bg-green-100 p-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 text-green-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>

          <div class="ml-4 text-left">
            <dt class="text-sm font-medium text-gray-600 group-hover:text-gray-900">Valid Records</dt>

            <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
              <%= @valid_record %>
            </dd>
          </div>
        </div>

        <div class="absolute bottom-0 left-0 right-0 h-1 bg-green-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-200">
        </div>
      </button>

      <button
        type="button"
        phx-click={@invalid_record_callback}
        class="group relative overflow-hidden rounded-xl bg-white px-6 py-5 shadow-sm ring-1 ring-gray-200 transition-all duration-200 hover:shadow-md hover:ring-red-500 hover:-translate-y-0.5"
      >
        <div class="flex items-center">
          <div class="rounded-full bg-red-100 p-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>

          <div class="ml-4 text-left">
            <dt class="text-sm font-medium text-gray-600 group-hover:text-gray-900">
              Invalid Records
            </dt>

            <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
              <%= @invalid_record %>
            </dd>
          </div>
        </div>

        <div class="absolute bottom-0 left-0 right-0 h-1 bg-red-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-200">
        </div>
      </button>

      <button
        type="button"
        class="group relative overflow-hidden rounded-xl bg-white px-6 py-5 shadow-sm ring-1 ring-gray-200 transition-all duration-200 hover:shadow-md hover:ring-blue-500 hover:-translate-y-0.5"
      >
        <div class="flex items-center">
          <div class="rounded-full bg-blue-100 p-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 text-blue-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M20 12H4M12 4v16"
              />
            </svg>
          </div>

          <div class="ml-4 text-left">
            <dt class="text-sm font-medium text-gray-600 group-hover:text-gray-900">Total Records</dt>

            <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
              <%= @total_record %>
            </dd>
          </div>
        </div>

        <div class="absolute bottom-0 left-0 right-0 h-1 bg-blue-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-200">
        </div>
      </button>
    </dl>
    """
  end

  def metrics_with_icons(assigns) do
    assigns =
      assign_new(assigns, :icon, fn ->
        """
           <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
               <path d="M19.7712 13.1046C20.7714 12.1044 21.3333 10.7478 21.3333 9.33333C21.3333 7.91885 20.7714 6.56229 19.7712 5.5621C18.771 4.5619 17.4145 4 16 4C14.5855 4 13.2289 4.5619 12.2288 5.5621C11.2286 6.56229 10.6667 7.91885 10.6667 9.33333C10.6667 10.7478 11.2286 12.1044 12.2288 13.1046C13.2289 14.1048 14.5855 14.6667 16 14.6667C17.4145 14.6667 18.771 14.1048 19.7712 13.1046Z" stroke="#FBBF24" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
               <path d="M9.40033 21.4003C11.1507 19.65 13.5246 18.6667 16 18.6667C18.4753 18.6667 20.8493 19.65 22.5997 21.4003C24.35 23.1507 25.3333 25.5246 25.3333 28H6.66666C6.66666 25.5246 7.64999 23.1507 9.40033 21.4003Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
           </svg>
        """
      end)
      |> assign_new(:title, fn -> "Active Users" end)
      |> assign_new(:value, fn -> 0 end)

    ~H"""
    <div class="rounded-lg p-5 bg-white shadow-sm">
      <div class="flex items-center space-x-4">
        <div>
          <div class="flex items-center justify-center w-12 h-12 rounded-full bg-fuchsia-50 text-fuchsia-400">
            <%= raw(@icon) %>
          </div>
        </div>

        <div>
          <div class="text-gray-400"><%= @title %></div>

          <div class="text-2xl font-bold text-gray-900"><%= @value %></div>
        </div>
      </div>
    </div>
    """
  end

  def metrics_with_icons_rounded(assigns) do
    assigns =
      assign_new(assigns, :icon, fn ->
        """
           <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
               <path d="M19.7712 13.1046C20.7714 12.1044 21.3333 10.7478 21.3333 9.33333C21.3333 7.91885 20.7714 6.56229 19.7712 5.5621C18.771 4.5619 17.4145 4 16 4C14.5855 4 13.2289 4.5619 12.2288 5.5621C11.2286 6.56229 10.6667 7.91885 10.6667 9.33333C10.6667 10.7478 11.2286 12.1044 12.2288 13.1046C13.2289 14.1048 14.5855 14.6667 16 14.6667C17.4145 14.6667 18.771 14.1048 19.7712 13.1046Z" stroke="#FBBF24" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
               <path d="M9.40033 21.4003C11.1507 19.65 13.5246 18.6667 16 18.6667C18.4753 18.6667 20.8493 19.65 22.5997 21.4003C24.35 23.1507 25.3333 25.5246 25.3333 28H6.66666C6.66666 25.5246 7.64999 23.1507 9.40033 21.4003Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
           </svg>
        """
      end)
      |> assign_new(:title, fn -> "Active Users" end)
      |> assign_new(:value, fn -> 0 end)

    ~H"""
    <div class="relative flex flex-col min-w-0 break-words bg-white rounded-lg mb-6 xl:mb-0 shadow-lg">
      <div class="flex-auto p-4">
        <div class="flex flex-wrap">
          <div class="relative w-full pr-4 max-w-full flex-grow flex-1">
            <h5 class="text-blueGray-400 uppercase font-bold text-xs"><%= @title %></h5>
            <span class="font-bold text-xl"><%= @value %></span>
          </div>

          <div class="relative w-auto pl-4 flex-initial">
            <%= raw(@icon) %>
          </div>
        </div>

        <p class="text-sm text-blueGray-500 mt-4">
          <span class="text-emerald-500 mr-2"><i class="fas fa-arrow-up"></i></span>
          <span class="whitespace-nowrap"></span>
        </p>
      </div>
    </div>
    """
  end

  attr :title, :string, required: true
  attr :count, :integer, required: true
  attr :description, :string, required: true
  attr :navigate_to, :string, required: true
  attr :icon, :string, default: "clock"
  attr :gradient_from, :string, default: "from-blue-500"
  attr :gradient_to, :string, default: "to-blue-600"
  attr :class, :string, default: ""
  slot :inner_block, required: true

  def application_card(assigns) do
    ~H"""
    <div class={[
      "group relative bg-white shadow-sm hover:shadow-xl rounded-2xl p-6 border border-gray-200",
      "transition-all duration-300 ease-in-out hover:scale-[1.02] hover:-translate-y-1",
      "cursor-pointer overflow-hidden",
      @class
    ]}>
      <!-- Subtle background gradient overlay -->
      <div class="absolute inset-0 bg-gradient-to-br from-gray-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl">
      </div>

      <.link navigate={@navigate_to} class="block relative z-10">
        <div class="flex items-center justify-between">
          <div class="flex items-center min-w-0 flex-1">
            <!-- Icon with improved gradient and shadow -->
            <div class={[
              "flex items-center justify-center w-14 h-14 rounded-xl shadow-lg",
              "bg-gradient-to-br #{@gradient_from} #{@gradient_to}",
              "group-hover:shadow-xl transition-shadow duration-300"
            ]}>
              <%= render_slot(@inner_block) %>
            </div>
            <!-- Content with better typography -->
            <div class="ml-5 min-w-0 flex-1">
              <h3 class="text-lg font-semibold text-gray-900 mb-1 group-hover:text-gray-800 transition-colors">
                <%= @title %>
              </h3>
              <p class="text-3xl font-bold text-gray-800 mb-1 tabular-nums">
                <%= NumberF.comma_separated(@count, 0) %>
              </p>
              <p class="text-sm text-gray-500 font-medium">
                <%= @description %>
              </p>
            </div>
          </div>
        </div>
      </.link>
    </div>
    """
  end

  attr :title, :string, required: true
  attr :subtitle, :string, required: true
  attr :gradient_from, :string, default: "from-brand-10"
  attr :gradient_to, :string, default: "to-brand-2"
  attr :class, :string, default: ""
  slot :inner_block, required: true

  def section_with_header(assigns) do
    ~H"""
    <div class={["bg-white shadow rounded-xl overflow-hidden border border-gray-200", @class]}>
      <div class={"bg-gradient-to-r #{@gradient_from} #{@gradient_to} px-6 py-5"}>
        <div class="flex justify-between items-center">
          <div>
            <h2 class="text-xl font-bold text-white"><%= @title %></h2>
            <p class="text-white text-sm mt-1"><%= @subtitle %></p>
          </div>
        </div>
      </div>
      <%= render_slot(@inner_block) %>
    </div>
    """
  end
end

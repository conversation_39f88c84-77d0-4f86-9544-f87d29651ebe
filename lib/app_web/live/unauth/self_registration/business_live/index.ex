defmodule AppWeb.UserRegistration.BusinessLive.Index do
  @moduledoc false
  use AppWeb, :live_view

  alias App.{Users, Companies}
  alias AppWeb.UserLoginLive

  @impl true
  def mount(_params, _session, socket) do
    socket
    |> assign(page_title: "Self Registration")
    |> assign_form(Companies.self_change_company())
    |> assign_form(Users.company_change_users(), :user_form)
    |> assign(data: nil)
    |> assign(params: nil)
    |> assign(:show_password, false)
    |> assign(menu_open: false)
    |> assign(:form_data, "block")
    |> assign(current_position: 0)
    |> ok()
  end

  @impl true
  def handle_event(target, attrs, %{assigns: assigns} = socket) do
    case target do
      "basic_info_save" ->
        {:noreply, basic_info_save(assigns.params, socket)}

      "validate_company" ->
        validate_company(attrs, socket)
        |> noreply()

      "validate_user" ->
        validate_user(attrs, socket)
        |> noreply()

      "view_terms" ->
        {:noreply, socket |> next_position()}

      "toggle_show_password" ->
        assign(socket, :show_password, !socket.assigns.show_password)
        |> noreply()

      "reset_show_password" ->
        if socket.assigns.show_password do
          assign(socket, :show_password, false)
          |> noreply()
        else
          noreply(socket)
        end

      "validate_email" ->
        {:noreply, check_email(assigns.params, socket)}

      "validate_otp" ->
        validate_otp(attrs, socket)

      "resend_otp" ->
        {:noreply, resend_otp(assigns.params, socket)}

      "toggle-menu" ->
        {:noreply, assign(socket, menu_open: !assigns.menu_open)}

      "back" ->
        {:noreply, pre_position(socket)}
    end
  end

  def check_email(params, socket) do
    Users.send_validation_otp(params)
    |> case do
      :ok ->
        socket
        |> next_position()
        |> assign(:otp_field_number, 4)
        |> put_flash(:info, "Successfully sent email confirmation!")

      {:error, _} ->
        socket
        |> put_flash(:info, "Successfully sent email confirmation!")
    end
  end

  defp resend_otp(user, socket) do
    Users.resend_self_reg_otp(user)
    |> case do
      :ok ->
        socket
        |> put_flash(:info, "Successfully sent otp!")

      {:error, _} ->
        socket
        |> put_flash(:error, "Failed to send otp!")
    end
  end

  def validate_otp(params, socket) do
    Users.validate_self_reg_otp(socket, socket.assigns.params, params)
    |> case do
      :ok ->
        save_user(socket)

      :error ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid OTP!")}
    end
  end

  defp save_user(%{assigns: assigns} = socket) do
    case Companies.create_self_reg_company(
           Map.merge(assigns.form.source.changes, assigns.user_form.source.changes)
         ) do
      {:ok, _record} ->
        {:noreply, UserLoginLive.login_submit(%{"user" => assigns.params}, socket)}

      {:error, "users", %Ecto.Changeset{} = changeset, _} ->
        error =
          changeset
          |> extract_first_error()
          |> to_string()

        {:noreply, put_flash(socket, :error, error)}

      {:error, message} ->
        {:noreply, put_flash(socket, :error, message)}
    end
  end

  defp extract_first_error(changeset) do
    changeset.errors
    |> Enum.map(fn {field, {message, _opts}} ->
      "#{String.capitalize(to_string(field))} #{message}"
    end)
    |> List.first()
  end

  def validate_company(%{"company" => params} = _attrs, socket) do
    changeset =
      Companies.self_change_company(params)
      |> Map.put(:action, :validate)

    assign_form(socket, changeset)
    |> assign(:params, params)
  end

  def validate_user(%{"user" => params} = _attrs, socket) do
    changeset =
      Users.company_change_users(params)
      |> Map.put(:action, :validate)

    assign_form(socket, changeset, :user_form)
    |> assign(:params, params)
  end

  defp basic_info_save(attrs, socket) do
    socket
    |> assign(:params, attrs)
    |> next_position()
  end

  defp next_position(socket), do: update(socket, :current_position, &(&1 + 1))

  defp pre_position(%{assigns: %{current_position: current_position}} = socket)
       when current_position >= 1,
       do: update(socket, :current_position, &(&1 - 1))

  defp pre_position(socket), do: socket
end

<.navbar
  menu_open={@menu_open}
  login_url={~p"/users/log_in"}
  registration_url={~p"/users/signup"}
  img_src="/images/pbs-logo.png"
  current_page="SELF_REG"
/>
<div class="relative isolate pt-10 mt-10 overflow-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50 min-h-screen">
  <!-- Background decorative elements -->
  <div class="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80">
    <div class="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-blue-400 to-indigo-600 opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]">
    </div>
  </div>

  <div class="w-full flex md:flex-row flex-col z-10 max-w-7xl mx-auto border shadow-xl rounded-lg h-[80vh] bg-white mt-10 overflow-hidden">
    <!-- LEFT SIDE : WIZARD -->
    <div class="w-full md:w-5/12 lg:w-4/12 flex flex-col gap-8 text-white bg-gradient-to-l from-brand-1 to-brand-2 md:h-full md:inset-y-0 sticky top-0 py-4 px-6 transition-all duration-300 ease-in-out">
      <p class="font-bold text-2xl text-center md:text-left text-white px-4 py-2">
        Self Registration
      </p>

      <.progress_indicator
        current={@current_position}
        data={[
          %{
            title: "User Details",
            icon: "hero-user-circle",
            desc: "Enter your details."
          },
          %{
            title: "Review",
            icon: "hero-clipboard-document-check",
            desc: "Review your information."
          },
          %{
            title: "Validate and Submit",
            icon: "hero-finger-print",
            desc: "Validate and submit your information."
          }
        ]}
      />
    </div>
    <!-- RIGHT SIDE : FORM -->
    <div class="relative flex flex-col justify-between w-full md:w-7/12 lg:w-8/12 dark_scrollbar hidden_scrollbar_container overflow-y-scroll">
      <!-- USER FORM -->
      <%= if @current_position == 0 do %>
        <div
          class="w-full animate-slide-in transition-all duration-500 ease-in-out"
          phx-hook="FormTransitions"
          id="contact-form-section"
        >
          <.simple_form
            for={@form}
            id="card-form"
            phx-submit="basic_info_save"
            phx-change="validate_user"
            class={"px-20 pb-5 #{@form_data}"}
          >
            <div class="flex justify-between gap-8 w-full">
              <div class="space-y-2">
                <p class="font-bold text-2xl text-fg-2">User Details</p>

                <p class="text-fg-4">
                  Register an account.
                </p>
              </div>
              <.icon name="hero-user" class="w-8 h-8 bg-brand-1 text-white rounded-full" />
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-1 lg:grid-cols-2 gap-8">
              <.input_form_with_clickable_icon
                field={@form[:first_name]}
                type="text"
                icon_start="hero-user"
                placeholder="Enter your First Name"
                label={raw(~c"First Name <span class='text-rose-500'>*</span>")}
                class="pl-10 w-full border border-gray-300 rounded-lg py-2 px-4 focus:ring-2 focus:ring-brand-200 focus:border-transparent transition duration-300"
                required
              />
              <.input_form_with_clickable_icon
                field={@form[:last_name]}
                type="text"
                icon_start="hero-user"
                placeholder="Enter your Last Name"
                label={raw(~c"Last Name <span class='text-rose-500'>*</span>")}
                class="pl-10 w-full border border-gray-300 rounded-lg py-2 px-4 focus:ring-2 focus:ring-brand-200 focus:border-transparent transition duration-300"
                required
              />
              <.input
                field={@form[:sex]}
                type="select"
                prompt="--Select Sex--"
                options={["MALE", "FEMALE"]}
                required
                label={raw(~c"Sex <span class='text-rose-500'>*</span>")}
              />
              <.input_form_with_clickable_icon
                field={@form[:email]}
                type="text"
                icon_start="hero-at-symbol"
                label={raw(~c"Email Address <span class='text-rose-500'>*</span>")}
                required
                placeholder="Enter your Email"
                class="pl-10 w-full border border-gray-300 rounded-lg py-2 px-4 focus:ring-2 focus:ring-brand-200 focus:border-transparent transition duration-300"
              />
              <.input
                phx-hook="validateNumberHook"
                field={@form[:mobile]}
                placeholder="e.g. 971234567"
                type="mobile"
                label={raw(~c"Mobile Number <span class='text-rose-500'>*</span>")}
                required
              />
              <.input_form_with_clickable_icon
                field={@form[:password]}
                type={if @show_password, do: "text", else: "password"}
                icon_start="hero-lock-closed"
                icon_end={if @show_password, do: "hero-eye-slash", else: "hero-eye"}
                label={raw(~c"New Password <span class='text-rose-500'>*</span>")}
                required
                placeholder="Enter your Password"
                class="pl-10 pr-10 w-full border border-gray-300 rounded-lg py-2 px-4 focus:ring-2 focus:ring-brand-200 focus:border-transparent transition duration-300"
              />
              <.input_form_with_clickable_icon
                field={@form[:confirm_password]}
                type={if @show_password, do: "text", else: "password"}
                icon_start="hero-lock-closed"
                icon_end={if @show_password, do: "hero-eye-slash", else: "hero-eye"}
                label={raw(~c"Confirm Password <span class='text-rose-500'>*</span>")}
                required
                placeholder="Confirm new Password"
                class="pl-10 pr-10 w-full border border-gray-300 rounded-lg py-2 px-4 focus:ring-2 focus:ring-brand-200 focus:border-transparent transition duration-300"
              />
              <.ns_input_form
                field={@form[:device]}
                type="hidden"
                phx-hook="DeviceUuid"
                label_class="text-white"
                required
              />
            </div>

            <div class="flex flex-wrap justify-end items-center gap-4 w-full bg-white sticky bottom-0 py-4 px-4 md:px-8 lg:px-12">
              <.button
                type="button"
                onclick="history.back()"
                class="items-center gap-2 tracking-wide bg-brand-1 transition-all duration-500 text-white border rounded-lg hover:opacity-70 focus:outline-none focus:bg-brand-1 focus:ring focus:ring-brand-1 focus:ring-opacity-50"
              >
                <.icon name="hero-chevron-left" class="w-4 h-4" /> Back
              </.button>

              <%= if @form.source.valid? do %>
                <.button class="items-center gap-2 tracking-wide text-white transition-all duration-500 bg-brand-1 rounded-lg hover:opacity-70 focus:outline-none focus:bg-brand-1 focus:ring focus:ring-brand-1 focus:ring-opacity-50">
                  Next <.icon name="hero-chevron-right" class="w-4 h-4" />
                </.button>
              <% end %>
            </div>
          </.simple_form>
        </div>
      <% end %>
      <!-- SUMMARY  -->
      <%= if @current_position == 1 do %>
        <div
          class="animate-fade-in transition-all duration-500 ease-in-out"
          phx-hook="FormTransitions"
          id="summary-section"
        >
          <div class="px-4 md:px-8 lg:px-12 mt-8 w-full">
            <div class="flex justify-between gap-8 w-full pt-2 pb-5 border-b">
              <div class="space-y-2">
                <p class="font-bold text-2xl text-fg-2">Summary</p>

                <p class="text-fg-4">Review the information provided.</p>
              </div>

              <.icon
                name="hero-clipboard-document-check"
                class="w-8 h-8 bg-brand-1 text-white rounded-full"
              />
            </div>

            <div class="gap-4 gap-y-12 mt-8">
              <div class="flex flex-col">
                <div class="rounded-lg bg-gray-50 shadow-sm ring-1 ring-gray-900/5">
                  <!-- User  Info Section -->
                  <div class="p-4 border-t border-gray-300 flex">
                    <!-- User Section -->
                    <div class="flex-1 pr-8">
                      <div class="mb-4 border-b pb-2">
                        <h2 class="text-lg font-bold">User Information</h2>
                      </div>

                      <dl class="grid grid-cols-1 sm:grid-cols-2 gap-y-4">
                        <div>
                          <dt class="text-sm font-medium text-gray-500">First Name:</dt>

                          <dd class="text-lg font-semibold"><%= @params["first_name"] %></dd>
                        </div>

                        <div>
                          <dt class="text-sm font-medium text-gray-500">Last Name:</dt>

                          <dd class="text-lg font-semibold"><%= @params["last_name"] %></dd>
                        </div>

                        <div>
                          <dt class="text-sm font-medium text-gray-500">Sex:</dt>

                          <dd class="text-lg font-semibold"><%= @params["sex"] %></dd>
                        </div>

                        <div>
                          <dt class="text-sm font-medium text-gray-500">Mobile:</dt>

                          <dd class="text-lg font-semibold">
                            <%= @params["mobile"] %>
                          </dd>
                        </div>

                        <div>
                          <dt class="text-sm font-medium text-gray-500">Email Address:</dt>

                          <dd class="text-lg font-semibold">
                            <%= @params["email"] %>
                          </dd>
                        </div>
                      </dl>
                    </div>
                  </div>

                  <div class="flex flex-wrap justify-end items-center gap-4 w-full bg-gray-50 sticky bottom-0 py-4 px-4 md:px-8 lg:px-12 mt-6 border-t border-gray-900/5">
                    <.button
                      type="button"
                      phx-click="back"
                      class="items-center gap-2 tracking-wide bg-brand-1 transition-all duration-500 text-white border rounded-lg hover:opacity-70 focus:outline-none focus:bg-brand-1 focus:ring focus:ring-brand-1 focus:ring-opacity-50"
                    >
                      <.icon name="hero-chevron-left" class="w-4 h-4" /> Previous
                    </.button>

                    <.button
                      type="button"
                      phx-disable-with="Submitting..."
                      phx-click="validate_email"
                      class="items-center gap-2 tracking-wide text-white transition-all duration-500 bg-brand-1 rounded-lg hover:opacity-70 focus:outline-none focus:bg-brand-1 focus:ring focus:ring-brand-10 focus:ring-opacity-50"
                    >
                      <.icon name="hero-check-circle" /> Submit
                    </.button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <%= if @current_position == 2 do %>
        <div
          class="w-full animate-slide-in transition-all duration-500 ease-in-out"
          phx-hook="FormTransitions"
          id="contact-form-section"
        >
          <.simple_form
            for={@form}
            id="login_form"
            phx-submit="validate_otp"
            class={"px-20 pb-5 pt-10 #{@form_data}"}
          >
            <div class="flex justify-between gap-8 w-full">
              <div class="space-y-2">
                <p class="font-bold text-2xl text-fg-2">Enter the OTP Received</p>
              </div>

              <.icon name="hero-finger-print" class="w-8 h-8 bg-brand-1 text-white rounded-full" />
            </div>

            <div class="flex flex-col space-y-6 pt-10">
              <div
                class="flex flex-row items-center justify-between mx-auto w-full max-w-xs"
                phx-update="ignore"
                phx-hook="OTPInput"
                id="otp"
              >
                <%= for i <- 1..@otp_field_number do %>
                  <input
                    name={"#{i}"}
                    type="text"
                    id={"#{i}"}
                    maxlength="1"
                    class="m-2 w-full h-full flex flex-col items-center justify-center text-center px-5 outline-none rounded-xl border border-gray-200 text-lg bg-white focus:bg-gray-50 focus:ring-1 ring-blue-700"
                  />
                <% end %>
              </div>

              <div class="flex flex-col space-y-5">
                <div>
                  <.button
                    phx-disable-with="Validating..."
                    class=" w-full bg-gradient-to-r from-brand-10 to-brand-2 text-white py-2 rounded-lg hover:opacity-90 transition duration-300 ease-in-out transform hover:scale-[1.02] shadow-lg hover:text-white"
                  >
                    Validate <span aria-hidden="true">→</span>
                  </.button>
                </div>

                <div class="flex flex-row items-center justify-center text-center text-sm font-medium space-x-1 text-gray-500">
                  <p>Didn't receive the code?</p>

                  <a
                    class="flex flex-row items-center text-brand-1 hover:text-brand-2 transition duration-300 cursor-pointer"
                    phx-click="resend_otp"
                  >
                    Resend
                  </a>
                </div>
              </div>
            </div>
          </.simple_form>
        </div>
      <% end %>
    </div>
  </div>
  <!-- Bottom decorative element -->
  <div class="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]">
    <div class="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-indigo-400 to-blue-600 opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]">
    </div>
  </div>
</div>

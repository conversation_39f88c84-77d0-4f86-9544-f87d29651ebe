defmodule AppWeb.Model.SessionCountDown do
  @moduledoc """
  LiveComponent for displaying a session timeout countdown with a circular timer and action buttons.
  Used by the Tracking module to notify users of impending session expiration.
  """
  use AppWeb, :live_component

  @defaults %{
    id: "SessionCountDown-kkk",
    time: "00:00",
    left_button: "Sign Out",
    right_button: "Continue Working",
    max: 500,
    current: 500,
    right_button_action: nil,
    left_button_action: nil,
    # Default blue color
    theme_color: "#3b82f6",
    # Seconds threshold for warning color
    warning_threshold: 30,
    # Added to control animation state
    show: true
  }

  @impl true
  def render(assigns) do
    assigns = assign_new(assigns, :theme_color, fn -> @defaults.theme_color end)
    assigns = assign_new(assigns, :warning_threshold, fn -> @defaults.warning_threshold end)
    assigns = assign_new(assigns, :show, fn -> @defaults.show end)

    # Calculate if we're in warning state (parse time to get seconds)
    warning_state =
      case Regex.run(~r/(\d+):(\d+)/, assigns.time) do
        [_, min, sec] ->
          total_seconds = String.to_integer(min) * 60 + String.to_integer(sec)
          total_seconds <= assigns.warning_threshold

        _ ->
          false
      end

    # Set appropriate color based on warning state and assign warning_state to assigns
    assigns =
      assigns
      |> assign(:circle_color, if(warning_state, do: "#dc2626", else: assigns.theme_color))
      |> assign(:warning_state, warning_state)

    ~H"""
    <div
      class="fixed inset-0 z-50 overflow-y-auto"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
      id={"#{@id}-modal"}
    >
      <div
        class={"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity duration-300 ease-out #{if @show, do: "opacity-100", else: "opacity-0"}"}
        phx-click="ignore"
        phx-target={@myself}
      >
      </div>
      <div class="flex min-h-full items-center justify-center p-4 text-center sm:p-0">
        <div
          class={"relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:max-w-lg w-full duration-300 ease-out #{if @show, do: "translate-y-0 opacity-100", else: "translate-y-20 opacity-0"}"}
          phx-click-away="ignore"
          phx-target={@myself}
        >
          <div class="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start sm:justify-center">
              <div class="mt-3 text-center sm:mt-0">
                <h3 class="text-lg font-semibold leading-6 text-gray-900" id="modal-title">
                  <%= @title %>
                </h3>

                <div class="mt-2 mb-6">
                  <p class="text-sm text-gray-500"><%= @sub_title %></p>
                </div>

                <div class="flex justify-center">
                  <div class="timer-container relative" style="width: 200px; height: 200px;">
                    <svg class="timer-svg" viewBox="0 0 250 250" width="200" height="200">
                      <!-- Background circle -->
                      <circle
                        cx="125"
                        cy="125"
                        r="100"
                        stroke="#e5e7eb"
                        stroke-width="15"
                        fill="transparent"
                      >
                      </circle>
                      <!-- Progress circle with animation -->
                      <circle
                        cx="125"
                        cy="125"
                        r="100"
                        stroke={@circle_color}
                        stroke-width="15"
                        fill="transparent"
                        stroke-dasharray={@max}
                        stroke-dashoffset={@current}
                        stroke-linecap="round"
                        class="transition-all duration-1000 ease-linear"
                      >
                      </circle>
                    </svg>
                    <!-- Timer display -->
                    <div class="absolute inset-0 flex flex-col items-center justify-center">
                      <span class={"text-3xl font-bold #{if @warning_state, do: "text-red-600", else: "text-gray-800"}"}>
                        <%= @time %>
                      </span>
                      <span class="text-xs mt-1 text-gray-500">remaining</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 px-4 py-4 sm:flex sm:flex-row-reverse sm:px-6 gap-3">
            <button
              phx-click="right_button"
              phx-target={@myself}
              class="inline-flex justify-center rounded-md px-4 py-2 text-sm font-medium text-white shadow-sm bg-brand-1 hover:bg-brand-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto transition-colors"
            >
              <%= @right_button %>
            </button>

            <button
              phx-click="left_button"
              phx-target={@myself}
              class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-0 sm:w-auto transition-colors"
            >
              <%= @left_button %>
            </button>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def mount(socket) do
    socket =
      assign(socket, @defaults)
      |> assign(:title, "Inactive Session")
      |> assign(:page_title, "Inactive Session")
      |> assign(
        :sub_title,
        "We have noticed your account has been inactive for some time. Your session will expire in:"
      )

    ok(socket)
  end

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(Map.take(assigns, Map.keys(@defaults)))
      |> maybe_assign_timer_state(assigns)

    ok(socket)
  end

  # Parse time and update visual state if needed
  defp maybe_assign_timer_state(socket, %{time: time} = _assigns) do
    case Regex.run(~r/(\d+):(\d+)/, time) do
      [_, min, sec] ->
        total_seconds = String.to_integer(min) * 60 + String.to_integer(sec)
        warning_threshold = socket.assigns[:warning_threshold] || @defaults.warning_threshold

        socket
        |> assign(:timer_seconds, total_seconds)
        |> assign(:warning_state, total_seconds <= warning_threshold)

      _ ->
        socket
    end
  end

  defp maybe_assign_timer_state(socket, _assigns), do: socket

  @impl true
  # Improved event handling with phx-click instead of phx-submit
  def handle_event(event, params, socket) do
    case event do
      "left_button" -> handle_button_click(socket.assigns.left_button_action, params, socket)
      "right_button" -> handle_button_click(socket.assigns.right_button_action, params, socket)
      # Handle clicks on the backdrop
      "ignore" -> {:noreply, socket}
    end
  end

  defp handle_button_click(action, params, socket) when is_function(action, 1) do
    # If action is a function, call it with params
    action.(params)
    {:noreply, socket}
  end

  defp handle_button_click(action, params, socket) do
    # Send message to parent LiveView
    send(self(), {__MODULE__, :button_clicked, %{action: action, param: params}})
    noreply(socket)
  end
end

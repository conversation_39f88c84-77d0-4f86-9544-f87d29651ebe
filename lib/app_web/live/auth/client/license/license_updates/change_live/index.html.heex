<div class="py-8 px-4 sm:px-6 lg:px-8">
  <div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="text-center mb-12">
      <h1 class="text-4xl font-bold text-gray-900 mb-4">Notice of Changes</h1>

      <p class="text-lg text-gray-600">Manage notice of changes.</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Current Client Details Section -->
      <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <div class="bg-gradient-to-r from-blue-600 to-brand-10 px-8 py-6">
          <h2 class="text-2xl font-bold text-white flex items-center">
            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              >
              </path>
            </svg>
            Current Details
          </h2>
        </div>

        <div class="px-8 py-8 space-y-6">
          <!-- Name -->
          <div class="flex items-start space-x-4">
            <div class="bg-blue-100 rounded-full p-2">
              <svg
                class="w-5 h-5 text-blue-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                >
                </path>
              </svg>
            </div>

            <div class="flex-1">
              <label class="text-sm font-medium text-gray-500 uppercase tracking-wide">
                Full Name
              </label>

              <p class="text-xl font-semibold text-gray-900 mt-1">
                <%= @client.first_name %> <%= @client.last_name %>
              </p>
            </div>
          </div>
          <!-- Email -->
          <div class="flex items-start space-x-4">
            <div class="bg-green-100 rounded-full p-2">
              <svg
                class="w-5 h-5 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                >
                </path>
              </svg>
            </div>

            <div class="flex-1">
              <label class="text-sm font-medium text-gray-500 uppercase tracking-wide">
                Email Address
              </label>

              <p class="text-lg text-gray-900 mt-1 break-all"><%= @client.email %></p>
            </div>
          </div>
          <!-- Phone -->
          <div class="flex items-start space-x-4">
            <div class="bg-purple-100 rounded-full p-2">
              <svg
                class="w-5 h-5 text-purple-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                >
                </path>
              </svg>
            </div>

            <div class="flex-1">
              <label class="text-sm font-medium text-gray-500 uppercase tracking-wide">
                Phone Number
              </label>

              <p class="text-lg text-gray-900 mt-1"><%= @client.mobile %></p>
            </div>
          </div>
          <!-- Current Company -->
          <div class="flex items-start space-x-4">
            <div class="bg-orange-100 rounded-full p-2">
              <svg
                class="w-5 h-5 text-orange-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                >
                </path>
              </svg>
            </div>

            <div class="flex-1">
              <label class="text-sm font-medium text-gray-500 uppercase tracking-wide">
                Company
              </label>

              <p class="text-lg font-semibold text-gray-900 mt-1">
                <%= @company.name %>
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- Company Selection Section -->
      <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <div class="bg-gradient-to-r from-blue-600 to-brand-10 px-8 py-6">
          <h2 class="text-2xl font-bold text-white flex items-center">
            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
              >
              </path>
            </svg>
            Notice of Changes
          </h2>
        </div>

        <div class="px-8 py-8">
          <p class="text-gray-600 mb-6">
            Select a company below.
          </p>

          <.simple_form for={@form} id="city-form" phx-change="validate" phx-submit="save">
            <div>
              <div class="relative">
                <.input
                  field={@form[:company_id]}
                  type="select"
                  prompt="--Select Company--"
                  options={@companies}
                  placeholder="Select Company"
                  required
                  label={raw(~c"Company <span class='text-rose-500'>*</span>")}
                />
              </div>
            </div>
            <!-- Selected Company Preview -->
            <div
              :if={@selected_company}
              class="mt-6 p-4 bg-indigo-50 rounded-xl border border-indigo-200"
            >
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-indigo-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  >
                  </path>
                </svg>

                <div>
                  <p class="text-sm font-medium text-brand-10">Selected Company</p>

                  <p class="text-sm text-brand-10">
                    <%= @selected_company["company"] || "No company selected" %>
                  </p>
                </div>
              </div>
            </div>

            <:actions>
              <%= if @form.source.valid? do %>
                <div class="w-full flex justify-center">
                  <.button class="rounded-full" type="submit" phx-disable-with="submitting...">
                    Submit
                  </.button>
                </div>
              <% end %>
            </:actions>
          </.simple_form>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Confirmation Modal -->
<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>

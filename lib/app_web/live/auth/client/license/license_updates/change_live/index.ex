defmodule AppWeb.Auth.LicenceChangeLive.Index do
  @moduledoc false
  use AppWeb, :live_view

  alias App.Companies

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("client_applications-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Notice of Changes", "Accessed Notice of Changes Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      company =
        if !is_nil(assigns.current_user.company_id) do
          Companies.get_company!(assigns.current_user.company_id)
        else
          Companies.get_company_by_user!(assigns.current_user.id)
        end

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(params: params)
        |> assign(client: assigns.current_user)
        |> assign(company: company)
        |> assign_form(Companies.client_change_company())
        |> assign(:companies, Companies.list_active_companies())
        |> assign(selected_company: nil)
        |> assign(confirmation_model: false)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(session_id: session["live_socket_id"])
        |> assign(live_socket_id: session["live_socket_id"])

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Notice of Changes", "Accessed Notice of Changes Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    # if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "Notice of Changes")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {AppWeb.LiveFunctions, message} ->
        {
          :noreply,
          socket
          |> put_flash(:info, message)
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, %{assigns: _assigns} = socket) do
    case target do
      "close_model" ->
        {:noreply, assign(socket, :live_action, :index)}

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :index)
        }

      "validate" ->
        validate(value, socket)

      "save" ->
        {
          :noreply,
          assign(socket, :live_action, :confirm)
          |> assign(
            :confirmation_model_text,
            "Are you sure you want to post this change?"
          )
          |> assign(:confirmation_model_params, Map.merge(value, %{"action" => "cancel"}))
          |> assign(:confirmation_model_agree, "process_attach")
        }

      "process_attach" ->
        attach_user(socket, value)

      _ ->
        {:noreply, socket}
    end
  end

  def attach_user(socket, value) do
    new_params = Map.merge(Jason.decode!(value["params"]), socket.assigns.params)

    Companies.submit_attach_request(
      socket,
      new_params
    )
    |> case do
      {:ok, _message} ->
        success_message(socket, "Attachment Request Successfully Submitted.")

      {:error, message} ->
        error_message(socket, message)
    end
  end

  defp validate(%{"user" => user}, socket) do
    changeset =
      socket.assigns.current_user
      |> Companies.client_change_company2(user)
      |> Map.put(:action, :validate)

    socket
    |> assign_form(changeset)
    |> assign(
      selected_company:
        CustomFunctions.get_value_from_select_data2(
          user,
          socket.assigns.companies,
          "company_id"
        )
    )
    |> noreply()
  end

  defp success_message(%{assigns: _assigns} = socket, message) do
    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign_form(Companies.client_change_company())
      |> assign(selected_company: nil)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "success"
      )
    }
  end

  defp error_message(%{assigns: _assigns} = socket, message) do
    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "error"
      )
    }
  end
end

defmodule AppWeb.Auth.ClientApplicationsLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias App.Files.UploadedFile
  alias App.{Licenses, Registration}
  alias App.Service.Table.ClientLicenceApplications, as: TableUserQuery

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do

    #  {record, associates} = Licenses.get_application_w_assoc(socket.assigns.current_user.id)
    if Audit.page_access("client_applications-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Applications", "Accessed Applications Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

    #  summary = App.Licenses.get_license_mapping_by_license_id_and_user_id(
    #     params["license_id"],
    #     assigns.current_user.id
    #  ) |> IO.inspect(label: "WHICH OF THE FAVORS OF YOUR LORD WOULD DENY!!!!!!!!!!!")

      filter_data = %{
        "record_name" => nil,
        "status" => nil,
        "start_date" => nil,
        "end_date" => nil
      }

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(give_condition_reasons: false)
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()
        |> assign(params: params)
        |> assign(
          query_expired_condition:
            App.Licenses.query_user_with_expired_conditions(assigns.current_user.id)
        )
        |> assign(confirmation_model: false)
        # |> assign(db_files: Licenses.get_uploaded_files_by_application!(record.id))
        |> assign(condition_application: nil)
        |> assign(db_files: [])
        |> assign(submitted_license_summary: Licenses.get_submitted_application_license(assigns.current_user.id) )
        |> assign(replace_application: "hide")
        |> assign(view_submitted_license: "hide")
        |> assign(reprint_application: "hide")
        |> assign(open_condition_upload: false)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(session_id: session["live_socket_id"])
        |> assign(live_socket_id: session["live_socket_id"])
        |> assign(showFilter: false)
        |> assign(form: useform(filter_data))
        |> assign(:uploaded_files, [])
       |> assign(:is_declined, App.Accounts.get_license_account_declined_by_user_id(socket.assigns.current_user.id))
        |> assign(
          :upload_form,
          to_form(Licenses.change_upload_file(%UploadedFile{}), as: "upload")
        )
        |> allow_upload(:file,
          accept: ~w(.jpg .jpeg .png .pdf .docx .doc .xlsx),
          max_entries: 1,
          max_file_size: 10_000_000
        )

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Applications", "Accessed Applications Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  defp useform(data) do
    to_form(
      data,
      as: "filter"
    )
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "Applications")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {:upload_file, file} ->
        upload_file(socket, file)

      {:search_records, params} ->
        send(self(), {:get_list, %{"filter" => params}})
        noreply(socket)

      {AppWeb.Component.DateFilter, {:filtered, data}} ->
        send(self(), {:get_list, %{"filter" => data}})

        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }

      {AppWeb.LiveFunctions, message} ->
        send(self(), {:get_list, %{}})

        {
          :noreply,
          socket
          |> put_flash(:info, message)
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }

      {AppWeb.LicenceDetailsLive.UploadfileComponent, {:saved, _user_index, msg}} ->
        send(self(), {:get_data, %{}})

        {:noreply,
         socket
         |> assign(:live_action, :index)
         |> LiveFunctions.sweet_alert(msg, "success")
         |> assign(:page_title, "Application Details")}
    end
  end

  @impl true
  def handle_event("cancel-entry", params, socket) do
    cancel_upload(params, socket)
  end

  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def cancel_upload(%{"ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, :file, ref)}
  end

  def handle_event_switch(target, value, %{assigns: assigns} = socket) do
    case target do
      "replace_application" ->
        replace_application(value, socket)

      "view_submitted_license" ->
        view_submitted_license(value, socket)

      "give_condition_reasons" ->
        give_condition_reasons(value, socket)

      "submit_condition_reason" ->
        submit_condition_reason(value, socket)

      "reprint_application" ->
        reprint_application(value, socket)

      "submit_replacement_request" ->
        submit_replacement_request(value, socket)

      "submit_reprint_request" ->
        submit_reprint_request(value, socket)

      "close_modal" ->
        close_modal(socket)

      "upload_file" ->
        {:noreply,
         assign(socket, :live_action, :upload_files)
         |> assign(:page_title, "Uploads")
         |> assign(application_id: value["application_id"])
         |> assign(
           condition_application:
             App.LicenseConditions.query_condition_by_user_id(value["application_id"])
         )}

      "close_model" ->
        {:noreply, assign(socket, :live_action, :index)}

      "export" ->
        LiveFunctions.export_records(socket, value, "users_service", assigns.form.params())

      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "filter_change" ->
        assign(socket, form: useform(value["filter"]))
        |> noreply()

      "filter" ->
        value = if socket.assigns.showFilter == true, do: false, else: true

        assign(socket, :showFilter, value)
        |> noreply()

      "reset_filter" ->
        socket
        |> assign(form: useform(%{}))
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        noreply(
          socket
          |> assign(checked_ids: [])
          |> assign(param_list: [])
        )

      "view_document" ->
        socket =
          socket
          |> assign(:page_title, "View Document")
          |> assign(:live_action, :view_document)
          |> assign(:doc_name, value["doc_name"])
          |> assign(:licence_form, ~p"/license/certificate/#{value["id"]}")

        {:noreply, socket}

      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :index)
        }

      "apply" ->
        get_url = Registration.get_current_step_url!(value["form_number"], value["current_step"])
        IO.inspect get_url, label: "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"

        {:noreply, push_navigate(socket, to: "#{get_url.url}#{value["form_number"]}")}

      "ammend_details" ->
        get_url = Registration.get_current_step_url!(value["form_number"], value["current_step"])

        {:noreply, push_navigate(socket, to: "#{get_url.url}#{value["form_number"]}")}

      "cancel_application" ->
        {
          :noreply,
          assign(socket, :live_action, :confirm)
          |> assign(
            :confirmation_model_text,
            "Are you sure you want to cancel this application?"
          )
          |> assign(:confirmation_model_params, Map.merge(value, %{"action" => "cancel"}))
          |> assign(:confirmation_model_agree, "process_cancel")
        }

      "process_cancel" ->
        cancel_application(Jason.decode!(value["params"]), socket)

      "open_condition_upload" ->
        open_condition_upload(socket)

      _ ->
        {:noreply, socket}
    end
  end

  def open_condition_upload(socket) do
    {:noreply, assign(socket, open_condition_upload: true)}
  end

  def replace_application(value, socket) do
    {:noreply, assign(socket, replace_application: "show", license_id: value["license_id"])}
  end

  def view_submitted_license(value, socket) do
    {:noreply, assign(socket, view_submitted_license: "show", license_id: value["license_id"])}
  end

  def give_condition_reasons(value, socket) do


    {:noreply,
     assign(socket,
       give_condition_reasons: true,
       license_name: value["licence_name"],
       date: value["expiring_date"] |> Date.from_iso8601!() |> Calendar.strftime("%A, %B %d, %Y"),
       license_id: value["id"]
     )}
  end

  def submit_condition_reason(value, socket) do


    App.Licenses.upate_license_expired_condition(value, socket)
    |> case do
      {:ok, _} ->
        {:noreply,
         socket
         |> assign(give_condition_reasons: false)
         |> put_flash(:info, "Condition reason successfully submitted")}

      {:error, reason} ->
        {:noreply,
         socket |> put_flash(:error, "Failed to submit condition reason: #{inspect(reason)}")}
    end
  end

  def reprint_application(value, socket) do
    {:noreply, assign(socket, reprint_application: "show", license_id: value["license_id"])}
  end

  def submit_replacement_request(value, socket) do
    id = value["license_id"]

    case App.Licenses.update_application_replace(id, socket, value) do
      {:ok, %{updates: _reason}} ->
        {:noreply,
         socket
         |> assign(replace_application: "hide")
         |> put_flash(:info, "Request successfully sent")}

      {:error, %{updates: reason}} ->
        {:noreply, socket |> put_flash(:error, "Failed to update license: #{inspect(reason)}")}
    end
  end

  def submit_reprint_request(value, socket) do
    id = value["license_id"]

    case App.Licenses.update_application_reprint(id, socket, value) do
      {:ok, %{updates: _reason}} ->
        {:noreply,
         socket
         |> assign(reprint_application: "hide")
         |> put_flash(:info, "Request successfully sent")}

      {:error, %{updates: reason}} ->
        {:noreply, socket |> put_flash(:error, "Failed to update license: #{inspect(reason)}")}
    end
  end

  def close_modal(socket) do
    {:noreply,
     assign(socket,
       replace_application: "hide",
       reprint_application: "hide",
       view_submitted_license: "hide",
       give_condition_reasons: false
     )}
  end

  defp list(socket, params) do
    {
      :noreply,
      assign(
        socket,
        :data,
        TableUserQuery.index(
          LivePageControl.create_table_params(socket, params),
          Map.put(params, "user_id", socket.assigns.current_user.id)
        )
      )
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end

  def cancel_application(value, socket) do
    Licenses.cancel_application(value["id"])
    |> case do
      {:ok, _message} ->
        success_message(socket, "Application Cancelled Successfully.")

      {:error, message} ->
        error_message(socket, message)
    end
  end

  defp success_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_list, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "success"
      )
    }
  end

  defp upload_file(socket, file) do
    Licenses.upload_level_file_condition(socket, file)
    |> case do
      {:ok, _} ->
        socket
        |> assign(loader: false)
        |> success_message("File Successfully Uploaded.")

      {:error, error} ->
        socket
        |> assign(loader: false)
        |> error_message(error)
    end
  end

  defp error_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_list, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "error"
      )
    }
  end


end

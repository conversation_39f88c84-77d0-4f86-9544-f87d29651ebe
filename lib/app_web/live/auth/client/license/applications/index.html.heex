<div class="px-4 sm:px-6 lg:px-8 mt-5">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">
        Applications
      </h1>
    </div>
  </div>
  <!-- FILTERS -->
  <%= if @showFilter do %>
    <div class="border overflow-hidden animate-slide-in-from-top transition-all ease-in-out duration-500 rounded-lg bg-white my-4 flex flex-col gap-4 items-start">
      <h1 class="px-4 py-2 bg-gray-50 border-b font-medium w-full">Filter Applications</h1>

      <FormJ.filter_form
        for={@form}
        class="w-full px-4"
        id="filter"
        phx-change="filter_change"
        phx-submit="search"
      >
        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 mb-4">
          <FormJ.input_filter
            field={@form[:record_name]}
            type="text"
            label="Record Name"
            placeholder="Enter Record Name"
          />
          <FormJ.input_filter
            field={@form[:status]}
            type="select"
            label="Status"
            prompt="--Select Status--"
            options={[
              {"All", ""},
              {"PENDING APPROVAL", 0},
              {"DECLINED", -1}
            ]}
          />
        </div>

        <p class="text-gray-500 font-medium">Date Filters</p>

        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 pt-2">
          <FormJ.input_filter field={@form[:start_date]} type="date" label="From" />
          <FormJ.input_filter field={@form[:end_date]} type="date" label="To" />
        </div>

        <div class="flex gap-4 items-center justify-start mt-4 p-4 w-full border-t font-medium">
          <.button type="reset" class="cursor-pointer hover:text-brand-1 py-2">Reset</.button>

          <.button type="submit" class="cursor-pointer hover:text-brand-1 py-2">Search</.button>
        </div>
      </FormJ.filter_form>
    </div>
  <% end %>
  <!-- END OF FILTERS -->
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <Table.main_table id="adverts" rows={@data} params={@params} data_loader={@data_loader}>
          <:col :let={rj} label={table_link(@params, "Registration Date", :inserted_at)}>
            <%= Calendar.strftime(
              NaiveDateTime.add(rj.inserted_at, 7200, :second),
              "%d %B %Y %H:%M:%S"
            ) %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Licence Name", :licence_name)}>
            <%= rj.licence_name %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Record Name", :record_name)}>
            <%= if rj.condition_tracking do %>
              <.link navigate={~p"/license/conditions/#{rj.id}"} class=" hover:text-brand-10">
                <%= rj.record_name %>
              </.link>
            <% else %>
              <%= rj.record_name %>
            <% end %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Status", :status)}>
            <Table.client_default_status
              status={rj.status}
              condition_tracking={rj.condition_tracking}
              approved={rj.approved}
              approval_status={rj.approval_status}
            />
          </:col>

          <%!--
          <:action :let={rj}>
            <.button
              type="button"
              phx-click="upload_file"
              phx-value-application_id={rj.id}
              class="btn btn-primary"
            >
              Upload
            </.button>
          </:action> --%>
          <:action :let={rj}>
            <%= if @params["status"] in ["draft", "resubmitted", "returned", "submitted", "approved", "under_consideration"]  do %>
              <Option.bordered>
                <.link
                  :if={rj.approved}
                  phx-click="view_document"
                  phx-value-doc_name={rj.licence_name}
                  phx-value-id={rj.license_id}
                  phx-value-path={rj.data["pop_upload"]}
                  class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-white"
                >
                  View License
                </.link>

                <.link
                  :if={rj.approved}
                  phx-click="replace_application"
                  phx-value-license_id={rj.license_id}
                  class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-white"
                >
                  Replace
                </.link>

                <.link
                  :if={rj.approved}
                  phx-click="reprint_application"
                  phx-value-license_id={rj.license_id}
                  class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-white"
                >
                  Reprint
                </.link>
              <%= if  @is_declined && @is_declined.declined == true do %>
                <.link
                  :if={rj.status == -1}
                  phx-click="apply"
                  phx-value-form_number={rj.license_id}
                  phx-value-current_step={3}
                  class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-white"
                >
                  Ammend Proof of Payment
                </.link>
                <% else %>
                  <.link :if={rj.status==-1} phx-click="apply" phx-value-form_number={rj.license_id} phx-value-current_step={1}
                    class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-white">
                    Ammend Details
                  </.link>
                <% end %>

                <%= if rj.status == 0 and rj.approval_status == false do %>
                  <.link
                    phx-click="apply"
                    phx-value-form_number={rj.license_id}
                    phx-value-current_step={rj.current_step}
                    class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-white"
                  >
                    Continue Application
                  </.link>

                  <.link
                    phx-click="cancel_application"
                    phx-value-id={rj.id}
                    class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-white"
                  >
                    Cancel Application
                  </.link>
                <% end %>

            <%= if rj.status > 0 and rj.approval_status == false or rj.approval_status == true  do %>
            <.link
            phx-click="view_submitted_license"
            phx-value-license_id={rj.license_id}
         
            class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-white"
            >
            View Submitted Application
            </.link>

            <% end %>


              </Option.bordered>
            <% end %>
          </:action>
        </Table.main_table>
      </div>
    </div>
  </div>
</div>

<%= if @replace_application == "show" do %>
  <modal>
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true">
    </div>

    <div class="fixed inset-0 z-10 overflow-y-auto">
      <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
          <div class="absolute right-0 top-0 pr-4 pt-4">
            <button
              type="button"
              class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none"
              phx-click="close_modal"
            >
              <span class="sr-only">Close</span>
              <svg
                class="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-brand-10 sm:mx-0 sm:h-10 sm:w-10">
              <svg
                class="h-6 w-6 text-white"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
                />
              </svg>
            </div>

            <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
              <h3 class="text-base font-semibold leading-6 text-gray-900">
                Request License Replacement
              </h3>

              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  Please provide details for your license replacement request.
                </p>
              </div>
            </div>
          </div>

          <form phx-submit="submit_replacement_request" class="mt-6 space-y-4">
            <div>
              <label for="reason" class="block text-sm font-medium text-gray-700">
                Reason for Replacement
              </label>

              <select
                id="reason"
                name="replace_reason"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-brand-10 focus:ring-brand-10 sm:text-sm"
              >
                <option value="">Select a reason</option>

                <option value="theft">Theft</option>

                <option value="damage">Damage</option>

                <option value="loss">Loss</option>

                <option value="other">Other</option>
              </select>
            </div>
            <input type="hidden" name="license_id" value={@license_id} />
            <div>
              <label for="description" class="block text-sm font-medium text-gray-700">
                Additional Details
              </label>
              <textarea
                id="description"
                name="replace_comments"
                rows="3"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-brand-10 focus:ring-brand-10 sm:text-sm"
                placeholder="Please provide any additional details about your request..."
              ></textarea>
            </div>

            <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                class="inline-flex w-full justify-center rounded-md bg-brand-10 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-brand-20 sm:ml-3 sm:w-auto"
              >
                Submit Request
              </button>

              <button
                type="button"
                phx-click="close_modal"
                class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </modal>
<% end %>

<%= if @view_submitted_license =="show" do %>

  <div class="fixed inset-0 bg-gray-500 bg-opacity-60" aria-hidden="true"></div>
  <div class="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto">
    <div class="relative w-full h-full flex items-center justify-center">
      <div class="relative w-full h-full bg-white border border-gray-200 rounded-lg overflow-auto p-0 sm:p-0" style="max-width: 900px; max-height: 90vh;">
        <div class="absolute right-0 top-0 pr-4 pt-4 z-10">
          <button
            type="button"
            class="rounded-full bg-white text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-brand-10 focus:ring-offset-2 border border-gray-200"
            phx-click="close_modal"
            aria-label="Close"
          >
            <svg class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="max-w-5xl mx-auto py-8 px-4 sm:px-8 md:px-12 lg:px-16 xl:px-20">
          <div class="mb-8 border-b pb-6">
            <h3 class="text-2xl font-bold text-gray-900 mb-1">Submitted Application Details</h3>
            <p class="text-gray-600 text-base mb-0">Below are the details of your submitted application and associated uploaded documents.</p>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <%= for summary <- @submitted_license_summary do %>
              <!-- License Details Section -->
              <div class="bg-gray-50 rounded-xl border border-gray-200 shadow-sm p-6 flex flex-col min-h-[340px]">
                <h4 class="text-xl font-bold text-brand-10 border-l-4 border-brand-10 pl-3 mb-6">License Details</h4>
                <dl class="divide-y divide-gray-200">
                  <%= for {label, value} <- [
                    {"License Type", summary.license.name},
                    {"Applicant Name", summary.user.first_name <> " " <> summary.user.last_name},
                    {"NRC", summary.data["national_id"]},
                    {"Phone number", summary.data["phone_number"]},
                    {"Firm Type", summary.data["firm_type"]},
                    {"Contact details", summary.data["contact_details"]},
                    {"Company ID", summary.data["company_ids"]},
                    {"Innovation description", summary.data["innovation_description"]},
                    {"Principal Business Nature", summary.data["principal_business_nature"]},
                    {"Source of Funds", summary.data["source_of_funds"]},
                    {"Place Of Incorp", summary.data["place_of_incorporation"]},
                    {"Date", Calendar.strftime(summary.inserted_at, "%d %B %Y")}
                  ], value != nil do %>
                    <div class="py-3 grid grid-cols-1 sm:grid-cols-3 gap-x-4 items-center">
                      <dt class="text-xs text-gray-500 font-semibold uppercase tracking-wider col-span-1"><%= label %>:</dt>
                      <dd class="text-sm text-gray-700 font-normal col-span-2"><%= value %></dd>
                    </div>
                  <% end %>
                </dl>
              </div>
              <!-- Uploaded Documents Section -->
              <div class="bg-gray-50 rounded-xl border border-gray-200 shadow-sm p-6 flex flex-col min-h-[340px]">
                <h4 class="text-xl font-bold text-brand-10 border-l-4 border-brand-10 pl-3 mb-6">Uploaded Documents</h4>
                <div class="overflow-x-auto max-h-96 overflow-y-auto pr-2">
                  <table class="min-w-full divide-y divide-gray-200 text-sm md:text-base">
                    <thead class="bg-gray-100 sticky top-0 z-10">
                      <tr>
                        <th class="px-4 py-2 text-left font-semibold text-gray-700">Document</th>
                        <th class="px-4 py-2 text-left font-semibold text-gray-700">Status</th>
                      </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-100">
                      <%= for {label, key} <- (
                        if summary.data["national_id"] == nil do
                          [
                            {"Proof Of Payment (Pop)", "pop_upload"},
                            {"Directors Id", "directors_id"},
                            {"Other Upload", "other_upload"},
                            {"Business Plan", "business_plan"},
                            {"Share Capital", "share_capital"},
                            {"Auditor Letter", "auditor_letter"},
                            {"Capital Markets", "capital_markets"},
                            {"Sign Off Upload", "sign_off_upload"},
                            {"Director Address", "directors_address"},
                            {"Shareholder Upload", "shareholder_upload"},
                            {"Controlling Persons", "controlling_persons"}
                          ]
                        else
                          [
                            {"Proof Of Payment (Pop)", "id_upload"},
                            {"Certificate Upload", "certificate_upload"},
                            {"Other Upload", "other_upload"},
                            {"Curriculum upload", "curriculum_upload"},
                            {"Cmaz Upload Upload", "cmaz_upload"},
                            {"Course Certificate Upload", "course_certificate_upload"},
                            {"Proof of residence upload", "proof_of_residence_upload"},
                            {"reference_upload", "reference_upload"},
                            {"supporting_certificate", "supporting_certificate"}
                          ]
                        end
                        if summary.data["attachment_business_plan"] != nil do
                        [
                          {"Attachment business plan", "attachment_business_plan"},
                          {"Attachment business profile", "attachment_business_profile"},
                          {"Attachment risk framework", "attachment_risk_framework"},
                          {"Partner firms", "partner_firms"},
                          {"Owners details", "owners_details"},
                          {"Key contacts", "key_contacts"},
                          {"Attachment test plan", "attachment_test_plan"},
                          {"Attachment key personnel profiles", "attachment_key_personnel_profiles"},
                          {"Attachment financial statements", "attachment_financial_statements"},

                        ]



                        end
                      ) do %>
                        <tr class="even:bg-white odd:bg-gray-50">
                          <td class="py-2 pr-4 font-medium text-gray-700 whitespace-nowrap"><%= label %></td>
                          <td class="py-2 text-gray-900">
                            <%= if summary.data[key] do %>
                              <span class="inline-block px-3 py-1 rounded-full bg-green-100 text-green-800 text-xs font-semibold border border-green-200">Uploaded</span>
                            <% else %>
                              <span class="inline-block px-3 py-1 rounded-full bg-gray-200 text-gray-500 text-xs font-semibold border border-gray-300">Not Uploaded</span>
                            <% end %>
                          </td>
                        </tr>
                      <% end %>
                    </tbody>
                  </table>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

<% end %>

<%= if @reprint_application == "show" do %>
  <modal>
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true">
    </div>

    <div class="fixed inset-0 z-10 overflow-y-auto">
      <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
          <div class="absolute right-0 top-0 pr-4 pt-4">
            <button
              type="button"
              class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none"
              phx-click="close_modal"
            >
              <span class="sr-only">Close</span>
              <svg
                class="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-brand-10 sm:mx-0 sm:h-10 sm:w-10">
              <svg
                class="h-6 w-6 text-white"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
                />
              </svg>
            </div>

            <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
              <h3 class="text-base font-semibold leading-6 text-gray-900">
                Request License Reprinting
              </h3>

              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  Please provide details for your license reprint request.
                </p>
              </div>
            </div>
          </div>

          <form phx-submit="submit_reprint_request" class="mt-6 space-y-4">
            <div>
              <label for="reason" class="block text-sm font-medium text-gray-700">
                Reason for Reprint
              </label>

              <select
                id="reason"
                name="reprint_reason"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-brand-10 focus:ring-brand-10 sm:text-sm"
              >
                <option value="">Select a reason</option>

                <option value="address">Address</option>

                <option value="mobile_number">Mobile Number</option>

                <option value="surname">Surname</option>

                <option value="first_name">Firstname</option>
              </select>
            </div>
            <input type="hidden" name="license_id" value={@license_id} />
            <div>
              <label for="description" class="block text-sm font-medium text-gray-700">
                Additional Details
              </label>
              <textarea
                id="description"
                name="reprint_comments"
                rows="3"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-brand-10 focus:ring-brand-10 sm:text-sm"
                placeholder="Please provide any additional details about your request..."
              ></textarea>
            </div>

            <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                class="inline-flex w-full justify-center rounded-md bg-brand-10 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-brand-20 sm:ml-3 sm:w-auto"
              >
                Submit Request
              </button>

              <button
                type="button"
                phx-click="close_modal"
                class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </modal>
<% end %>

<%= if @give_condition_reasons do %>
  <modal>
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true">
    </div>

    <div class="fixed inset-0 z-10 overflow-y-auto">
      <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
          <div class="absolute right-0 top-0 pr-4 pt-4"></div>

          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-yellow-400 sm:mx-0 sm:h-10 sm:w-10">
              <svg
                class="h-6 w-6 text-white"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 9v2m0 4h.01M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"
                />
              </svg>
            </div>

            <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
              <h2>The conditiion set for this license expired on <%= @date %> .</h2>

              <div class="mt-2">
                <h3 class="text-sm text-gray-500">
                  Please explain why you did not meet the required conditions for your application.
                </h3>
              </div>
            </div>
          </div>

          <form phx-submit="submit_condition_reason" class="mt-6 space-y-4">
            <div>
              <label for="condition_reason" class="block text-sm font-medium text-gray-700">
                Reason
              </label>

              <select
                id="condition_reason"
                name="condition_reason"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-yellow-400 focus:ring-yellow-400 sm:text-sm"
                required
              >
                <option value="">Select a reason</option>

                <option value="lack_of_resources">Lack of resources</option>

                <option value="awaiting_approval">
                  Awaiting approval from another authority
                </option>

                <option value="documentation_delay">Delay in documentation</option>

                <option value="other">Other</option>
              </select>
            </div>
            <input type="hidden" name="license_id" value={@license_id} />
            <input type="hidden" name="license_name" value={@license_name} />
            <div>
              <label for="condition_comments" class="block text-sm font-medium text-gray-700">
                Additional Details
              </label>
              <textarea
                id="condition_comments"
                name="condition_comments"
                rows="3"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-yellow-400 focus:ring-yellow-400 sm:text-sm"
                placeholder="Please provide any additional details..."
              ></textarea>
            </div>

            <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                class="inline-flex w-full justify-center rounded-md bg-brand-10 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-brand-20 sm:ml-3 sm:w-auto"
              >
                Submit Reason
              </button>

              <button
                type="button"
                phx-click="close_modal"
                class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </modal>
<% end %>

<Model.small :if={@live_action in [:filter]} id="transaction-modal" show return_to="close_model">
  <.live_component
    module={AppWeb.Component.DateFilter}
    id={:filter}
    title={@page_title}
    @click.outside="open = false"
    params={@params}
  />
</Model.small>

<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
<Model.small
  :if={@live_action == :upload_files}
  id="user-modal"
  show
  return_to="close_model"
  title={@page_title}
>
  <.live_component
    module={AppWeb.LicenceDetailsLive.UploadfileComponent}
    id={@current_user.id}
    title={@page_title}
    application_id={@application_id}
    condition_application={@condition_application}
    action={@live_action}
    browser_info={@browser_info}
    current_user={@current_user}
  />
</Model.small>

<Model.fullscreen
  :if={@live_action in [:view_document]}
  id="view_licence-modal"
  title="Licence Form"
  show
  return_to="close_model"
>
  <iframe
    src={@licence_form}
    id="licence_iframe"
    title="Licence"
    style="width: 100%;"
    height="700"
    name="Licence"
  >
  </iframe>
</Model.fullscreen>

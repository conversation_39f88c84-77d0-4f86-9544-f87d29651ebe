defmodule AppWeb.Auth.RegistrationLive.Entry do
  use AppWeb, :live_view
  alias App.{Licenses, Registration}
  alias App.Licenses.DynamicSchema
  alias App.Validators.DynamicFormValidator

  def init(socket, params, service \\ "text_fields") do
    cond do
      service == "text_fields" ->
        text_fields(socket, params)

      service == "uploads" ->
        uploads(socket, params)

      service == "payments" ->
        payments(socket, params)

      service == "summary" ->
        summary(socket, params)
    end
  end

  # defp text_fields(%{assigns: assigns} = socket, params) do
  #   license_fields = Licenses.get_license_data(params["license_id"])

  #   # Organize fields

  #   upload_fields = Enum.filter(license_fields, &(&1.field_type == "upload"))

  #   text_fields =
  #     Enum.filter(
  #       license_fields,
  #       &(&1.field_type in [
  #           "text",
  #           "date",
  #           "select",
  #           "number",
  #           "mobile",
  #           "textarea",
  #           "checkbox",
  #           "radio",
  #           "checkbox_group"
  #         ])
  #     )
  #     |> Enum.sort_by(fn field ->
  #       case field.field_type do
  #         "text" -> 0
  #         "mobile" -> 1
  #         "number" -> 2
  #         "date" -> 3
  #         "textarea" -> 5
  #         _ -> 4
  #       end
  #     end)

  #   input_fields =
  #     text_fields
  #     |> Enum.map(fn field ->
  #       {String.to_atom(field.field_name), field.field_name}
  #     end)

  #   record =
  #     Licenses.get_license_mapping_by_license_id_and_user_id(
  #       params["license_id"],
  #       assigns.current_user.id
  #     )

  #   data =
  #     if is_nil(record),
  #       do: %{},
  #       else: record.data

  #   # Create initial changeset with the dynamic validator
  #   initial_changeset =
  #     DynamicFormValidator.validate_step(
  #       data,
  #       text_fields,
  #       step: 0
  #     )

  #   socket
  #   |> assign(data_loader: false)
  #   |> assign(min_date: Date.utc_today() |> Date.to_iso8601())
  #   |> assign(record: record)
  #   |> assign(steps: get_steps(params["license_id"]))
  #   |> assign(current_position: List.first(Registration.get_license_steps!(params["license_id"])))
  #   |> assign(chosen_licence: Licenses.get_license!(params["license_id"]))
  #   # Convert changeset to form here
  #   |> assign(:form, to_form(initial_changeset, as: "license_mapping"))
  #   |> assign(:upload_form, to_form(initial_changeset, as: "license_mapping"))
  #   |> assign(get_attention_field: App.LicenseReviews.get_attention_field(params["license_id"]))
  #   |> assign(license_id: params["license_id"])
  #   |> assign(license_fields: license_fields)
  #   |> assign(upload_fields1: upload_fields)
  #   |> assign(text_fields: text_fields)
  #   |> assign(input_fields: Keyword.keys(input_fields))
  #   |> noreply()
  # end

  defp text_fields(%{assigns: assigns} = socket, params) do
    license_fields = Licenses.get_license_data(params["license_id"])

    upload_fields =
      Enum.filter(license_fields, &(&1.field_type == "upload"))

    text_fields =
      Enum.filter(
        license_fields,
        &(&1.field_type in [
            "text",
            "date",
            "select",
            "number",
            "mobile",
            "textarea",
            "checkbox",
            "radio",
            "checkbox_group"
          ])
      )
      |> Enum.sort_by(fn field ->
        case field.field_type do
          "text" -> 0
          "mobile" -> 1
          "number" -> 2
          "date" -> 3
          "textarea" -> 5
          _ -> 4
        end
      end)

    input_fields =
      text_fields
      |> Enum.map(fn field ->
        {String.to_atom(field.field_name), field.field_name}
      end)

    record =
      Licenses.get_license_mapping_by_license_id_and_user_id(
        params["license_id"],
        assigns.current_user.id
      )

    data =
      if is_nil(record),
        do: %{},
        else: record.data

    applicant_name =
      [assigns.current_user.first_name, assigns.current_user.last_name]
      |> Enum.reject(&is_nil/1)
      |> Enum.join(" ")

    merged_data =
      data
      |> Map.put_new("first_name", assigns.current_user.first_name)
      |> Map.put_new("mobile", assigns.current_user.mobile)
      |> Map.put_new("last_name", assigns.current_user.last_name)
      |> Map.put_new("applicant_name", applicant_name)
      |> Map.put_new("email", assigns.current_user.email)

    initial_changeset =
      DynamicFormValidator.validate_step(
        merged_data,
        text_fields,
        step: 0
      )

    socket
    |> assign(data_loader: false)
    |> assign(min_date: Date.utc_today() |> Date.to_iso8601())
    |> assign(record: record)
    |> assign(steps: get_steps(params["license_id"]))
    |> assign(current_position: List.first(Registration.get_license_steps!(params["license_id"])))
    |> assign(chosen_licence: Licenses.get_license!(params["license_id"]))
    |> assign(:form, to_form(initial_changeset, as: "license_mapping"))
    |> assign(:upload_form, to_form(initial_changeset, as: "license_mapping"))
    |> assign(get_attention_field: App.LicenseReviews.get_attention_field(params["license_id"]))
    |> assign(license_id: params["license_id"])
    |> assign(license_fields: license_fields)
    |> assign(upload_fields1: upload_fields)
    |> assign(text_fields: text_fields)
    |> assign(input_fields: Keyword.keys(input_fields))
    |> noreply()
  end

  defp uploads(%{assigns: assigns} = socket, params) do
    license_fields = Licenses.get_license_data(params["license_id"])

    # Organize fields

    upload_fields =
      Enum.filter(license_fields, &(&1.field_type == "upload" && &1.field_name != "pop_upload"))

    record =
      Licenses.get_license_mapping_by_license_id_and_user_id(
        params["license_id"],
        assigns.current_user.id
      )

    data =
      if is_nil(record),
        do: %{},
        else: record.data

    upload_fields1 =
      upload_fields
      |> Enum.filter(fn field ->
        DynamicSchema.should_show_field?(field, data)
      end)

    available_upload =
      Enum.filter(upload_fields1, fn field ->
        !(record.data && Map.has_key?(record.data, field.field_name) &&
            Map.get(record.data, field.field_name) != nil)
      end)

    # Create upload changeset
    upload_changeset =
      DynamicFormValidator.validate_step(
        data,
        available_upload,
        step: 1
      )

    all_form_data =
      case record do
        nil -> %{}
        record -> record.data
      end

    # upload_fields
    # |> Enum.filter(fn field ->
    #   DynamicSchema.should_show_field?(field, data) &&
    #     !(record.data && Map.has_key?(record.data, field.field_name) &&
    #         Map.get(record.data, field.field_name) != nil)
    # end)
    # |> Enum.map(fn field ->
    #   {String.to_atom(field.field_name), ~w(.pdf .jpg .png .jpeg)}
    # end)

    uploads =
      upload_fields
      |> Enum.filter(fn field ->
        DynamicSchema.should_show_field?(field, data)
      end)
      |> Enum.map(fn field ->
        {String.to_atom(field.field_name), ~w(.pdf .jpg .png .jpeg)}
      end)

    socket
    |> assign(data_loader: false)
    |> assign(record: record)
    |> assign(steps: get_steps(params["license_id"]))
    |> assign(current_position: record.current_step)
    |> assign(chosen_licence: Licenses.get_license!(params["license_id"]))
    |> configure_uploads(uploads)
    |> assign(upload_fields: Keyword.keys(uploads))
    |> assign(all_form_data: all_form_data)
    |> assign(get_attention_field: App.LicenseReviews.get_attention_field(params["license_id"]))
    # Initialize as empty map instead of nil
    |> assign(:doc_details, %{})
    |> assign(license_id: params["license_id"])
    |> assign(license_fields: license_fields)
    |> assign(upload_fields1: upload_fields1)
    |> assign(:upload_form, to_form(upload_changeset, as: "upload"))
    |> assign(available_uploads: available_upload)
    |> noreply()
  end

  defp payments(%{assigns: assigns} = socket, params) do
    license_fields = Licenses.get_license_data(params["license_id"])
    upload_fields = get_payment_upload_fields(license_fields)

    record = get_license_record(params["license_id"], assigns.current_user.id)
    data = get_record_data(record)

    upload_changeset = create_upload_changeset(data, upload_fields)
    all_form_data = get_all_form_data(record)

    {upload_fields1, uploads} = prepare_upload_fields(upload_fields, all_form_data, record)

    payment_totals = calculate_payment_totals(record, params["license_id"])

    socket
    |> assign_payment_data(record, params["license_id"], payment_totals)
    |> assign_upload_data(uploads, upload_fields1, upload_changeset, all_form_data)
    |> assign_license_data(license_fields, params["license_id"])
    |> noreply()
  end

  defp summary(%{assigns: assigns} = socket, params) do
    license_fields = Licenses.get_license_data(params["license_id"])

    # Organize fields
    text_fields =
      Enum.filter(
        license_fields,
        &(&1.field_type in [
            "text",
            "date",
            "select",
            "number",
            "textarea",
            "checkbox",
            "radio",
            "checkbox_group"
          ])
      )
      |> Enum.sort_by(fn field ->
        case field.field_type do
          "text" -> 0
          "date" -> 1
          "textarea" -> 3
          _ -> 2
        end
      end)

    upload_fields = Enum.filter(license_fields, &(&1.field_type == "upload"))

    record =
      Licenses.get_license_mapping_by_license_id_and_user_id(
        params["license_id"],
        assigns.current_user.id
      )

    associates = Licenses.get_associated_licenses!(record.id)

    data =
      if is_nil(record),
        do: %{},
        else: record.data

    # Create initial changeset with the dynamic validator
    initial_changeset =
      DynamicFormValidator.validate_step(
        data,
        text_fields,
        step: 0
      )

    assign(socket, data: [])
    |> assign(data_loader: false)
    |> assign(chosen_licence: Licenses.get_license!(params["license_id"]))
    |> assign(record: record)
    |> assign(associates: associates)
    |> assign(steps: get_steps(params["license_id"]))
    |> assign(:form, to_form(initial_changeset, as: "license_mapping"))
    |> assign(params: params)
    |> assign(license_fields: license_fields)
    |> assign(upload_fields1: upload_fields)
    |> assign(current_position: record.current_step)
    |> noreply()
  end

  # Helper Functions Here -------------->>>

  # Payment helper functions ------>

  defp get_payment_upload_fields(license_fields) do
    Enum.filter(license_fields, &(&1.field_name == "pop_upload"))
  end

  defp get_license_record(license_id, user_id) do
    Licenses.get_license_mapping_by_license_id_and_user_id(license_id, user_id)
  end

  defp get_record_data(record) do
    if is_nil(record), do: %{}, else: record.data
  end

  defp create_upload_changeset(data, upload_fields) do
    DynamicFormValidator.validate_step(data, upload_fields, step: 1)
  end

  defp get_all_form_data(record) do
    case record do
      nil -> %{}
      record -> record.data || %{}
    end
  end

  defp prepare_upload_fields(upload_fields, all_form_data, record) do
    upload_fields1 =
      upload_fields
      |> Enum.filter(fn field ->
        DynamicSchema.should_show_field?(field, all_form_data)
      end)

    uploads =
      upload_fields
      |> Enum.filter(fn field ->
        DynamicSchema.should_show_field?(field, all_form_data) &&
          !(record.data && Map.has_key?(record.data, field.field_name) &&
              Map.get(record.data, field.field_name) != nil)
      end)
      |> Enum.map(fn field ->
        {String.to_atom(field.field_name), ~w(.pdf .jpg .png .jpeg)}
      end)

    {upload_fields1, uploads}
  end

  defp calculate_payment_totals(record, license_id) do
    associates = Licenses.get_associated_licenses!(record.id)
    associates_count = Licenses.count_associates_at_reg(record.id)
    chosen_licence = Licenses.get_license!(license_id)

    total_assoc_amount =
      case List.first(associates) do
        nil -> Decimal.new(0)
        first_assoc -> Decimal.mult(associates_count, first_assoc.license.amount)
      end

    total_amount =
      Decimal.add(chosen_licence.amount, total_assoc_amount)
      |> Decimal.add(chosen_licence.other_fees || 0)

    %{
      associates: associates,
      associates_count: associates_count,
      chosen_licence: chosen_licence,
      total_assoc_amount: total_assoc_amount,
      total_amount: total_amount
    }
  end

  defp assign_payment_data(socket, record, license_id, payment_totals) do
    socket
    |> assign(data_loader: false)
    |> assign(record: record)
    |> assign(steps: get_steps(license_id))
    |> assign(
      totals: %{
        total_amount: payment_totals.total_amount,
        total_assoc_amount: payment_totals.total_assoc_amount
      }
    )
    |> assign(associates: payment_totals.associates)
    |> assign(current_position: record.current_step)
    |> assign(associates_count: payment_totals.associates_count)
    |> assign(chosen_licence: payment_totals.chosen_licence)
  end

  defp assign_upload_data(socket, uploads, upload_fields1, upload_changeset, all_form_data) do
    available_uploads =
      Enum.filter(upload_fields1, fn field ->
        record = socket.assigns.record

        !(record.data && Map.has_key?(record.data, field.field_name) &&
            Map.get(record.data, field.field_name) != nil)
      end)

    socket
    |> configure_uploads(uploads)
    |> assign(upload_fields: Keyword.keys(uploads))
    |> assign(all_form_data: all_form_data)
    |> assign(:doc_details, %{})
    |> assign(upload_fields1: upload_fields1)
    |> assign(:upload_form, to_form(upload_changeset, as: "upload"))
    |> assign(available_uploads: available_uploads)
  end

  defp assign_license_data(socket, license_fields, license_id) do
    socket
    |> assign(get_attention_field: App.LicenseReviews.get_attention_field(license_id))
    |> assign(license_id: license_id)
    |> assign(license_fields: license_fields)
  end

  # steps ---------------->

  defp get_steps(license_id) do
    Registration.get_reg_steps!(license_id)
  end

  def next_position(socket, record) do
    get_next_page =
      socket.assigns.steps
      |> Enum.find(fn step -> step.number > socket.assigns.current_position end)

    Licenses.update_current_step(socket.assigns.record, get_next_page.number)

    push_navigate(socket, to: "#{get_next_page.step.url}#{record.license_id}")
  end

  def previous_position(socket) do
    get_prev_page =
      socket.assigns.steps
      |> Enum.filter(fn step -> step.number < socket.assigns.current_position end)
      |> Enum.max_by(& &1.number, fn -> nil end)

    path =
      if get_prev_page do
        Licenses.update_current_step(socket.assigns.record, get_prev_page.number)
        "#{get_prev_page.step.url}#{socket.assigns.record.license_id}"
      else
        "/dashboard"
      end

    push_navigate(socket, to: path)
  end

  # uploads ------>

  defp configure_uploads(socket, upload_types) do
    Enum.reduce(upload_types, socket, fn {field, _accepts}, acc ->
      allow_upload(acc, field,
        accept: ~w(.pdf .jpg .png .jpeg),
        max_entries: 1,
        max_file_size: 10_000_000,
        auto_upload: true,
        progress: &handle_progress/3
      )
    end)
  end

  # Handle progress for auto-upload
  defp handle_progress(upload_field, entry, socket) do
    if not entry.done? do
      {:noreply, socket}
    else
      file_path = process_uploaded_file(socket, entry)
      updated_data = build_updated_data(socket, upload_field, file_path)

      case Licenses.update_data_key(socket, updated_data) do
        {:ok, record} ->
          handle_successful_upload(socket, entry, upload_field, record)

        {:error, _error} ->
          handle_failed_upload(socket)
      end
    end
  end

  defp process_uploaded_file(socket, entry) do
    path = LiveFunctions.static_path("/uploads/licence_registration")

    consume_uploaded_entry(socket, entry, fn %{path: temp_path} = _meta ->
      copy_file_to_destination(temp_path, path, entry)
    end)

    "#{path}/#{Path.rootname(entry.client_name)}.#{LiveFunctions.ext(entry)}"
  end

  defp copy_file_to_destination(temp_path, path, entry) do
    file_name = "/#{Path.rootname(entry.client_name)}.#{LiveFunctions.ext(entry)}"
    dest = Path.join(path, file_name)

    ensure_directory_exists(path)
    File.cp(temp_path, dest)
    {:ok, dest}
  end

  defp ensure_directory_exists(path) do
    unless File.exists?(path <> "/") do
      File.mkdir_p(path <> "/")
    end
  end

  defp build_updated_data(socket, upload_field, file_path) do
    Map.put(socket.assigns.record.data || %{}, Atom.to_string(upload_field), file_path)
  end

  defp handle_successful_upload(socket, entry, upload_field, record) do
    updated_available_uploads =
      Enum.filter(socket.assigns.available_uploads, fn field ->
        field.field_name != Atom.to_string(upload_field)
      end)

    socket
    |> LiveFunctions.sweet_toast_alert("#{entry.client_name} uploaded successfully", "success")
    |> assign(:record, record)
    |> assign(:available_uploads, updated_available_uploads)
    |> push_event("file_uploaded", %{field: upload_field, filename: entry.client_name})
    |> noreply()
  end

  defp handle_failed_upload(socket) do
    socket
    |> LiveFunctions.sweet_toast_alert("Failed to save uploaded file", "error")
    |> noreply()
  end

  def validate_doc(%{"_target" => [field]}, %{assigns: assigns} = socket) do
    upload_field = String.to_atom(field)

    attrs =
      assigns.upload_fields
      |> Enum.reduce(%{}, fn key, acc ->
        # check existing entries
        existing_entries =
          assigns.uploads[key]
          |> Map.get(:entries, [])
          |> Enum.map(& &1.client_name)
          |> Enum.at(0)

        # If the current key matches field validating, fetch the entries
        updated_entries =
          if key == upload_field do
            assigns.uploads[upload_field]
            |> Map.get(:entries, [])
            |> Enum.map(& &1.client_name)
            |> Enum.at(0)
          else
            existing_entries
          end

        Map.put(acc, key, updated_entries)
      end)

    # Use our dynamic validator for uploads
    changeset =
      DynamicFormValidator.validate_step(
        attrs,
        assigns.available_uploads,
        step: assigns.current_position
      )
      |> Map.put(:action, :validate)

    assign(socket, :upload_form, to_form(changeset, as: "upload"))
    |> noreply()
  end

  def validate_upload(%{"_target" => [field]}, %{assigns: assigns} = socket) do
    upload_field = String.to_atom(field)

    attrs =
      assigns.upload_fields
      |> Enum.reduce(%{}, fn key, acc ->
        # check existing entries
        existing_entries =
          assigns.uploads[key]
          |> Map.get(:entries, [])
          |> Enum.map(& &1.client_name)
          |> Enum.at(0)

        # If the current key matches field validating, fetch the entries
        updated_entries =
          if key == upload_field do
            assigns.uploads[upload_field]
            |> Map.get(:entries, [])
            |> Enum.map(& &1.client_name)
            |> Enum.at(0)
          else
            existing_entries
          end

        Map.put(acc, key, updated_entries)
      end)

    correction_uploads =
      Enum.filter(assigns.upload_fields1, fn field ->
        Enum.any?(assigns.get_attention_field, fn attention ->
          attention.attention_field == field.field_label
        end)
      end)

    # Use our dynamic validator for uploads
    changeset =
      DynamicFormValidator.validate_step(
        attrs,
        correction_uploads,
        step: assigns.current_position
      )
      |> Map.put(:action, :validate)

    assign(socket, :upload_form, to_form(changeset, as: "upload"))
    |> noreply()
  end

  def consume_upload(socket, upload_type, _attrs) do
    new_path = LiveFunctions.static_path("/uploads/licence_registration")

    consume_uploaded_entries(socket, upload_type, fn %{path: path}, entry ->
      file_name = "/#{Path.rootname(entry.client_name)}.#{LiveFunctions.ext(entry)}"
      dest = Path.join(new_path, file_name)

      if File.exists?(new_path <> "/") do
        File.cp(path, dest)
        {:ok, dest}
      else
        File.mkdir_p(new_path <> "/")
        File.cp_r(path, dest)
        {:ok, dest}
      end
    end)
  end

  def document_uploads(attrs, socket) do
    # Process each upload type and return the updated attributes
    updated_attrs =
      socket.assigns.upload_fields
      |> Enum.reduce(attrs, fn upload_type, acc ->
        acc |> Map.put("#{upload_type}", handle_upload(socket, upload_type, attrs))
      end)

    changeset =
      DynamicFormValidator.validate_step(
        updated_attrs,
        socket.assigns.available_uploads,
        step: socket.assigns.current_position
      )
      |> Map.put(:action, :validate)

    if changeset.valid? do
      Licenses.create_registration(
        socket,
        socket.assigns.record,
        Map.merge(
          updated_attrs,
          socket.assigns.all_form_data
          |> Jason.encode!()
          |> Jason.decode!()
        )
      )
    else
      # If validation fails, return the form with errors
      socket
      |> assign(:form, %{source: Map.put(changeset, :action, :validate)})
    end
  end

  defp handle_upload(socket, upload_type, attrs) do
    current_attachment = Map.get(socket.assigns, upload_type)

    if current_attachment && length(socket.assigns.uploads[upload_type].entries) > 0 do
      File.rm!(current_attachment)
      consume_upload(socket, upload_type, attrs)
    else
      # if length(socket.assigns.uploads[upload_type].entries) > 0 do
      List.first(consume_upload(socket, upload_type, attrs))
      # else
      #   List.first(current_attachment)
      # end
    end
  end
end

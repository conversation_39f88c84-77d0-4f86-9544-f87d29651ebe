defmodule AppWeb.Registration.NavigationComponent do
  use AppWeb, :live_component

  @impl true
  def render(assigns) do
    ~H"""
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 pt-6 sticky top-12 z-10">
      <div class="bg-white rounded-lg shadow-md p-4">
        <ol class="flex flex-wrap md:flex-nowrap items-center w-full">
          <%= for {step, index} <- Enum.with_index(@steps) do %>
            <li class={"flex items-center #{if index < length(@steps) - 1, do: "w-full md:flex-1"} #{if index > 0, do: "mt-4 md:mt-0"}"}>
              <div class={"flex items-center justify-center #{if @current_position >= step.number, do: "bg-brand-10 text-white", else: "bg-gray-200 text-gray-500"} rounded-full h-8 w-8 md:h-10 md:w-10 flex-shrink-0"}>
                <.icon name={step.step.icon} class="h-4 w-4 md:h-5 md:w-5" />
              </div>

              <div class="ml-2 flex-1">
                <p class={"text-xs md:text-sm font-medium #{if @current_position >= step.number, do: "text-brand-10", else: "text-gray-500"}"}>
                  <%= step.step.name %>
                </p>
              </div>

              <%= if index < length(@steps) - 1 do %>
                <div class="hidden md:block w-full bg-gray-200 h-0.5 mx-2 md:mx-4">
                  <div
                    class={"#{if @current_position > step.number, do: "bg-brand-10", else: "bg-gray-200"} h-0.5"}
                    style="width: 100%"
                  >
                  </div>
                </div>
              <% end %>
            </li>
          <% end %>
        </ol>
      </div>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    {
      :ok,
      socket
      |> assign(assigns)
    }
  end
end

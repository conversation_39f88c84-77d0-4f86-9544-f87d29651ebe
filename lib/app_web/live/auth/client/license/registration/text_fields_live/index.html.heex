<!-- Enhanced License Registration Template - Aligned with Backend -->
<%= if @data_loader == false do %>
  <%= if @get_attention_field==[] do %>
    <.live_component
      module={AppWeb.Registration.NavigationComponent}
      id="navigation"
      steps={@steps}
      current_position={@current_position}
    /> <% # Calculate field pagination using backend values
    total_fields = length(@text_fields)
    fields_per_page = @fields_per_page || 10
    current_sub_step = @current_sub_step || 0
    should_paginate = total_fields > fields_per_page

    # Calculate pagination info
    {current_fields, pagination_info} =
      if should_paginate do
        total_pages = ceil(total_fields / fields_per_page)
        start_index = current_sub_step * fields_per_page
        _end_index = min(start_index + fields_per_page - 1, total_fields - 1)
        current_fields = Enum.slice(@text_fields, start_index, fields_per_page)

        {current_fields,
         %{
           current_page: current_sub_step + 1,
           total_pages: total_pages,
           has_previous: current_sub_step > 0,
           has_next: current_sub_step < total_pages - 1,
           start_field: start_index + 1,
           end_field: min(start_index + fields_per_page, total_fields),
           total_fields: total_fields
         }}
      else
        {@text_fields, nil}
      end %>
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8 flex flex-col lg:flex-row gap-8">
      <!-- Main Form Container -->
      <div class="flex-1 bg-white rounded-lg shadow-md p-8">
        <div class="mb-8 border-b pb-5">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-2xl font-bold text-gray-900">
                <%= @chosen_licence.name %> Registration
              </h1>
              
              <p class="mt-2 text-sm text-gray-600">
                Please complete all required fields to register your license.
              </p>
              <!-- Field Progress Indicator -->
              <%= if should_paginate do %>
                <div class="mt-4 flex items-center space-x-2">
                  <span class="text-sm text-gray-500">
                    Fields <%= pagination_info.start_field %>-<%= pagination_info.end_field %> of <%= pagination_info.total_fields %>
                  </span>
                  <!-- Mini Progress Bar -->
                  <div class="flex-1 max-w-xs bg-gray-200 rounded-full h-2">
                    <div
                      class="bg-brand-10 h-2 rounded-full transition-all duration-300"
                      style={"width: #{Float.round(pagination_info.end_field / pagination_info.total_fields * 100, 1)}%"}
                    >
                    </div>
                  </div>
                  
                  <span class="text-xs text-brand-10 font-medium">
                    <%= Float.round(
                      pagination_info.end_field / pagination_info.total_fields * 100,
                      0
                    ) %>%
                  </span>
                </div>
              <% end %>
            </div>
            <!-- Page Indicator -->
            <%= if should_paginate do %>
              <div class="flex items-center space-x-1">
                <%= for page <- 1..pagination_info.total_pages do %>
                  <div class={"w-2 h-2 rounded-full #{if page - 1 == current_sub_step, do: "bg-brand-10", else: "bg-gray-300"}"}>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
          <!-- Attention Fields -->
          <%!-- <%= for attention <- @get_attention_field do %>
          <div class="mt-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-md shadow-sm max-w-2xl w-full">
            <div class="flex items-start">
              <svg
                class="h-5 w-5 text-red-500 mr-3 mt-0.5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              
              <div>
                <p class="text-sm font-semibold text-red-700">
                  <%= attention.attention_field %> <span class="text-rose-500">*</span>
                </p>
                
                <p class="text-xs text-orange-600 mt-1">
                  <%= attention.reason %>
                </p>
              </div>
            </div>
          </div>
        <% end %> --%>
        </div>
        
        <.simple_form
          for={@form}
          id="license-registration-form"
          phx-submit={
            if (should_paginate and pagination_info) && pagination_info.has_next,
              do: "next_field_page",
              else: "reg_info_save"
          }
          phx-change="validate"
          class="space-y-8 mt-8"
        >
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <%= for field <- current_fields do %>
              <% should_show =
                App.Licenses.DynamicSchema.should_show_field?(field, @form.source.params || %{}) %>
              <%= if should_show do %>
                <div class="space-y-2">
                  <%= case field.field_type do %>
                    <% "date" -> %>
                      <.dynamic_input form={@form} field={field} type="date" />
                    <% "text" when field.field_name in ["national_id"] -> %>
                      <.dynamic_input
                        form={@form}
                        field={field}
                        hook="nrcInputHook"
                        pattern="\d{6}/\d{2}/\d"
                      />
                    <% "text" when field.field_name in ["passport_number"] -> %>
                      <.dynamic_input form={@form} field={field} hook="validatePassportHook" />
                    <% "text" -> %>
                      <%= if String.contains?(field.field_name, "_name") do %>
                        <.dynamic_input form={@form} field={field} hook="validateAlphaHook" />
                      <% else %>
                        <.dynamic_input form={@form} field={field} />
                      <% end %>
                    <% "mobile" -> %>
                      <.dynamic_input
                        form={@form}
                        field={field}
                        type="mobile"
                        hook="validateNumberHook"
                      />
                    <% "number" -> %>
                      <.input
                        field={@form[String.to_atom(field.field_name)]}
                        type="number"
                        phx-debounce="blur"
                        placeholder={"Enter #{field.field_label}"}
                        required={field.required}
                        min={field.field_validations["min"] || 1}
                        max={field.field_validations["max"] || 10000}
                        label={
                          if field.required,
                            do:
                              raw(~c"#{field.field_label} <span class='text-rose-500'>*</span>"),
                            else: field.field_label
                        }
                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-brand-2 focus:ring-brand-2"
                      />
                    <% "textarea" -> %>
                      <.dynamic_input form={@form} field={field} type="textarea" rows={4} />
                    <% "checkbox" -> %>
                      <div class="flex items-start">
                        <.input
                          field={@form[String.to_atom(field.field_name)]}
                          type="checkbox"
                          phx-debounce="blur"
                          required={field.required}
                          label={
                            if field.required,
                              do:
                                raw(~c"#{field.field_label} <span class='text-rose-500'>*</span>"),
                              else: field.field_label
                          }
                          class="h-4 w-4 rounded border-gray-300 text-brand-10 focus:ring-brand-2 mt-1 mr-2"
                        />
                      </div>
                    <% "select" when field.field_name in ["nationality"] -> %>
                      <.dynamic_input
                        form={@form}
                        field={field}
                        type="select"
                        options={CountryHelpers.get_nationality()}
                      />
                    <% "select" -> %>
                      <.dynamic_input
                        form={@form}
                        field={field}
                        type="select"
                        options={field.field_options}
                      />
                    <% "radio" -> %>
                      <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700">
                          <%= field.field_label %>
                          <%= if field.required do %>
                            <span class="text-rose-500">*</span>
                          <% end %>
                        </label>
                        
                        <div class="space-y-2">
                          <%= for option <- field.field_options || [] do %>
                            <div class="flex items-center">
                              <.input
                                field={@form[String.to_atom(field.field_name)]}
                                type="radio"
                                name={field.field_name}
                                value={option}
                                required={field.required}
                                label={option}
                                phx-debounce="blur"
                                checked={
                                  Phoenix.HTML.Form.input_value(
                                    @form,
                                    String.to_atom(field.field_name)
                                  ) == option
                                }
                                class="h-4 w-4 border-gray-300 text-brand-10 focus:ring-brand-2 mr-2"
                              />
                            </div>
                          <% end %>
                        </div>
                      </div>
                    <% "checkbox_group" -> %>
                      <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700">
                          <%= field.field_label %>
                          <%= if field.required do %>
                            <span class="text-rose-500">*</span>
                          <% end %>
                        </label>
                        
                        <div class="space-y-2">
                          <%= for option <- field.field_options || [] do %>
                            <div class="flex items-center">
                              <.input
                                field={@form[String.to_atom(field.field_name)]}
                                type="checkbox"
                                phx-debounce="blur"
                                name={"#{field.field_name}[]"}
                                value={option}
                                label={option}
                                checked={
                                  option in (Phoenix.HTML.Form.input_value(
                                               @form,
                                               String.to_atom(field.field_name)
                                             ) || [])
                                }
                                class="h-4 w-4 rounded border-gray-300 text-brand-10 focus:ring-brand-2 mr-2"
                              />
                            </div>
                          <% end %>
                        </div>
                      </div>
                    <% _ -> %>
                      <div class="text-red-500 text-sm">
                        Unknown field type: <%= field.field_type %>
                      </div>
                  <% end %>
                  
                  <%= if Map.get(field, :field_description) do %>
                    <p class="text-xs text-gray-500 mt-1"><%= field.field_description %></p>
                  <% end %>
                </div>
              <% end %>
            <% end %>
          </div>
          <!-- Navigation Buttons -->
          <div class="flex justify-between pt-6 border-t">
            <div class="flex space-x-2">
              <!-- Back to Dashboard Button -->
              <.button type="button" phx-click="back" class="bg-gray-500 hover:bg-brand-11">
                <i class="hero-chevron-double-left"></i> Back
              </.button>
              <!-- Previous Field Page Button -->
              <%= if should_paginate and pagination_info.has_previous do %>
                <.button
                  type="button"
                  phx-click="prev_field_page"
                  class="bg-gray-500 hover:bg-brand-11"
                >
                  <i class="hero-chevron-left"></i> Previous Fields
                </.button>
              <% end %>
            </div>
            
            <div class="flex space-x-2">
              <!-- Next Field Page or Save & Continue Button -->
              <%= if (should_paginate and pagination_info && pagination_info.has_next) or (should_paginate and pagination_info && !pagination_info.has_next and @form.source.valid?) or (!should_paginate and @form.source.valid?) do %>
                <.button
                  type="submit"
                  class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium bg-brand-10 hover:bg-brand-11 text-white"
                >
                  <%= if should_paginate and pagination_info && pagination_info.has_next do %>
                    Next Fields <i class="hero-chevron-right ml-1"></i>
                  <% else %>
                    Next Step <i class="hero-chevron-double-right ml-1"></i>
                  <% end %>
                </.button>
              <% end %>
            </div>
          </div>
        </.simple_form>
      </div>
      <!-- Document Requirements Panel -->
      <div class="w-full lg:w-1/3 bg-white p-6 rounded-lg shadow-md border border-gray-200 sticky top-6">
        <div class="flex items-center mb-4">
          <svg
            class="h-5 w-5 text-brand-10 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          
          <h3 class="text-lg font-medium text-gray-900">Required Documents</h3>
        </div>
        
        <p class="text-sm text-gray-600 mb-4">
          Please prepare the following documents for upload in the next step:
        </p>
        
        <ul class="space-y-3">
          <%= for field <- @upload_fields1 do %>
            <% should_show =
              App.Licenses.DynamicSchema.should_show_field?(field, @form.source.params || %{}) %>
            <%= if should_show do %>
              <li class="flex items-start">
                <svg
                  class="h-5 w-5 text-brand-10 mr-2 mt-0.5 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                
                <div>
                  <span class="font-medium text-gray-700">
                    <%= field.field_label %>
                    <%= if field.required do %>
                      <span class="text-rose-500">*</span>
                    <% end %>
                  </span>
                  
                  <%= if Map.get(field, :field_description) do %>
                    <p class="text-xs text-gray-500 mt-1"><%= field.field_description %></p>
                  <% end %>
                </div>
              </li>
            <% end %>
          <% end %>
        </ul>
        
        <div class="mt-6 pt-4 border-t border-gray-200">
          <h4 class="text-sm font-medium text-gray-800 mb-2">Document Requirements:</h4>
          
          <ul class="text-xs text-gray-600 space-y-1">
            <li class="flex items-start">
              <svg
                class="h-3.5 w-3.5 text-gray-400 mr-1.5 mt-0.5 flex-shrink-0"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              File format: PDF, JPG, or PNG
            </li>
            
            <li class="flex items-start">
              <svg
                class="h-3.5 w-3.5 text-gray-400 mr-1.5 mt-0.5 flex-shrink-0"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              Maximum file size: 10MB per document
            </li>
            
            <li class="flex items-start">
              <svg
                class="h-3.5 w-3.5 text-gray-400 mr-1.5 mt-0.5 flex-shrink-0"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              Documents must be clear and legible
            </li>
          </ul>
        </div>
        
        <div class="mt-6 bg-yellow-50 p-3 rounded-md border border-yellow-200">
          <div class="flex">
            <svg
              class="h-5 w-5 text-yellow-600 mr-2 flex-shrink-0"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            
            <div>
              <p class="text-xs font-medium text-yellow-800">
                All documents must be valid and certified at the time of submission.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  <% else %>
    <.live_component
      module={AppWeb.Registration.NavigationComponent}
      id="navigation"
      steps={@steps}
      current_position={@current_position}
    /> <% # Calculate field pagination using backend values 
    total_fields = length(@text_fields)
    fields_per_page = @fields_per_page || 10
    current_sub_step = @current_sub_step || 0
    should_paginate = total_fields > fields_per_page

    # Calculate pagination info
    {_current_fields, pagination_info} =
      if should_paginate do
        total_pages = ceil(total_fields / fields_per_page)
        start_index = current_sub_step * fields_per_page
        _end_index = min(start_index + fields_per_page - 1, total_fields - 1)
        current_fields = Enum.slice(@text_fields, start_index, fields_per_page)

        {current_fields,
         %{
           current_page: current_sub_step + 1,
           total_pages: total_pages,
           has_previous: current_sub_step > 0,
           has_next: current_sub_step < total_pages - 1,
           start_field: start_index + 1,
           end_field:
             min(
               start_index +
                 fields_per_page,
               total_fields
             ),
           total_fields: total_fields
         }}
      else
        {@text_fields, nil}
      end %>
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-white rounded-lg shadow-md">
        <div class="p-6">
          <div class="text-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">
              <%= @chosen_licence.name %> Registration
            </h1>
            
            <p class="mt-2 text-sm text-gray-600">
              Please review and correct the highlighted field(s) below, or click "Next Step" to continue.
            </p>
          </div>
          
          <.simple_form
            for={@upload_form}
            id="license-correction-form"
            phx-submit="submit_correction"
            phx-change="validate"
          >
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <%= if Enum.any?(@text_fields, fn field ->
                Enum.any?(@get_attention_field, fn attention ->
                  attention.attention_field == field.field_label
                end)
              end) do %>
                <%= for field <- @text_fields  do %>
                  <%= for attention <- @get_attention_field  do %>
                    <%= if attention.attention_field == field.field_label do %>
                      <div class="space-y-4">
                        <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-md">
                          <div class="flex">
                            <div class="flex-shrink-0">
                              <svg
                                class="h-5 w-5 text-red-400"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path
                                  fill-rule="evenodd"
                                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                  clip-rule="evenodd"
                                />
                              </svg>
                            </div>
                            
                            <div class="ml-3">
                              <p class="text-sm text-red-700 font-medium">
                                <%= attention.attention_field %>
                              </p>
                              
                              <p class="mt-1 text-xs text-red-600">
                                <%= attention.reason %>
                              </p>
                            </div>
                          </div>
                        </div>
                         <input type="hidden" name="license_id" value={attention.license_id} />
                         <input type="hidden" name="field_id[]" value={attention.field_id} />
                        <input type="hidden" name="approver_id" value={attention.approver_id} />
                        <%= case field.field_type do %>
                          <% "date" -> %>
                            <.input
                              field={@form[String.to_atom(field.field_name)]}
                              type="date"
                              phx-debounce="blur"
                              required={field.required}
                              label={
                                if field.required,
                                  do:
                                    raw(
                                      ~c"#{field.field_label} <span class='text-rose-500'>*</span>"
                                    ),
                                  else: field.field_label
                              }
                              class="w-full"
                            />
                          <% "text" -> %>
                            <.input
                              field={@form[String.to_atom(field.field_name)]}
                              type="text"
                              phx-debounce="blur"
                              required={field.required}
                              label={
                                if field.required,
                                  do:
                                    raw(
                                      ~c"#{field.field_label} <span class='text-rose-500'>*</span>"
                                    ),
                                  else: field.field_label
                              }
                              class="w-full"
                            />
                          <% "number" -> %>
                            <.input
                              field={@form[String.to_atom(field.field_name)]}
                              type="number"
                              phx-debounce="blur"
                              required={field.required}
                              min={field.field_validations["min"] || 1}
                              max={field.field_validations["max"] || 10000}
                              label={
                                if field.required,
                                  do:
                                    raw(
                                      ~c"#{field.field_label} <span class='text-rose-500'>*</span>"
                                    ),
                                  else: field.field_label
                              }
                              class="w-full"
                            />
                          <% "textarea" -> %>
                            <.input
                              field={@form[String.to_atom(field.field_name)]}
                              type="textarea"
                              phx-debounce="blur"
                              required={field.required}
                              rows="3"
                              label={
                                if field.required,
                                  do:
                                    raw(
                                      ~c"#{field.field_label} <span class='text-rose-500'>*</span>"
                                    ),
                                  else: field.field_label
                              }
                              class="w-full"
                            />
                          <% "checkbox" -> %>
                            <.input
                              field={@form[String.to_atom(field.field_name)]}
                              type="checkbox"
                              phx-debounce="blur"
                              required={field.required}
                              label={
                                if field.required,
                                  do:
                                    raw(
                                      ~c"#{field.field_label} <span class='text-rose-500'>*</span>"
                                    ),
                                  else: field.field_label
                              }
                            />
                          <% "select" -> %>
                            <.input
                              field={@form[String.to_atom(field.field_name)]}
                              type="select"
                              prompt="Select an option"
                              options={field.field_options}
                              phx-debounce="blur"
                              required
                              label={
                                raw(~c"#{field.field_label} <span class='text-rose-500'>*</span>")
                              }
                              class="w-full"
                            />
                          <% "radio" -> %>
                            <div class="space-y-2">
                              <label class="block text-sm font-medium text-gray-700">
                                <%= field.field_label %>
                                <%= if field.required do %>
                                  <span class="text-rose-500">*</span>
                                <% end %>
                              </label>
                              
                              <div class="space-y-2">
                                <%= for option <- field.field_options || [] do %>
                                  <div class="flex items-center">
                                    <.input
                                      field={@form[String.to_atom(field.field_name)]}
                                      type="radio"
                                      name={field.field_name}
                                      value={option}
                                      required={field.required}
                                      label={option}
                                      phx-debounce="blur"
                                      checked={
                                        Phoenix.HTML.Form.input_value(
                                          @form,
                                          String.to_atom(field.field_name)
                                        ) == option
                                      }
                                    />
                                  </div>
                                <% end %>
                              </div>
                            </div>
                          <% "checkbox_group" -> %>
                            <div class="space-y-2">
                              <label class="block text-sm font-medium text-gray-700">
                                <%= field.field_label %>
                                <%= if field.required do %>
                                  <span class="text-rose-500">*</span>
                                <% end %>
                              </label>
                              
                              <div class="space-y-2">
                                <%= for option <- field.field_options || [] do %>
                                  <div class="flex items-center">
                                    <.input
                                      field={@form[String.to_atom(field.field_name)]}
                                      type="checkbox"
                                      phx-debounce="blur"
                                      name={"#{field.field_name}[]"}
                                      value={option}
                                      label={option}
                                      checked={
                                        option in (Phoenix.HTML.Form.input_value(
                                                     @form,
                                                     String.to_atom(field.field_name)
                                                   ) || [])
                                      }
                                    />
                                  </div>
                                <% end %>
                              </div>
                            </div>
                          <% _ -> %>
                            <div class="text-red-500 text-sm">
                              Unknown field type: <%= field.field_type %>
                            </div>
                        <% end %>
                        
                        <%= if Map.get(field, :field_description) do %>
                          <p class="text-xs text-gray-500 mt-2">
                            <%= field.field_description %>
                          </p>
                        <% end %>
                      </div>
                    <% end %>
                  <% end %>
                <% end %>
              <% else %>
                <div class="col-span-full py-8">
                  <div class="flex flex-col items-center justify-center text-center text-gray-600 text-sm space-y-2">
                    <svg class="h-6 w-6 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    
                    <span class="font-medium">
                      All required fields have already been filled.
                    </span>
                  </div>
                </div>
              <% end %>
            </div>
            
            <div class="flex justify-between mt-8 pt-6 border-t">
              <.button
                type="button"
                onclick="history.back()"
                class="inline-flex items-center px-6 py-2 text-sm font-medium text-white bg-brand-10 border border-transparent rounded-md shadow-sm hover:bg-brand-11 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-2"
              >
                <i class="hero-chevron-double-left"></i> Back
              </.button>
              
              <%= if should_paginate and pagination_info.has_next do %>
                <.button
                  type="submit"
                  class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium bg-brand-10 hover:bg-brand-11 text-white"
                >
                  Next Step <i class="hero-chevron-double-right ml-1"></i>
                </.button>
              <% end %>
            </div>
          </.simple_form>
        </div>
      </div>
    </div>
  <% end %>
<% end %>

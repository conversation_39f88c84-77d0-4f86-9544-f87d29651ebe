defmodule AppWeb.Auth.Registration.TextFieldsLive.Index do
  use AppWeb, :live_view

  alias App.Licenses
  alias App.Validators.DynamicFormValidator
  alias AppWeb.Auth.RegistrationLive.Entry
  alias Logs.Audit

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("license_registration", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Licence Registration", "Accessed Licence Registration Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(params: params)
        |> assign(live_socket_id: session["live_socket_id"])
        # Field pagination support
        |> assign(current_sub_step: 0)
        |> assign(fields_per_page: 10)

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Licence Registration", "Accessed Licence Registration Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_data, params})

    {
      :noreply,
      socket
      |> assign(:params, params)
      |> apply_action(socket.assigns.live_action, params)
    }
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {:get_data, params} ->
        get_update(socket, params)

      {:process_registration, params} ->
        submit(socket, params)
    end
  end

  @impl true
  def handle_event(target, attrs, %{assigns: assigns} = socket) do
    case target do
      "submit_correction" ->
        submit_correction(attrs, socket)

      "reg_info_save" ->
        reg_info_save(attrs, socket)
        |> noreply()

      "validate" ->
        validate(attrs, socket)
        |> noreply()

      "submit" ->
        send(self(), {:process_registration, assigns.data})

        {
          :noreply,
          socket
          |> assign(data_loader: true)
        }

      "back" ->
        {:noreply, Entry.previous_position(socket)}

      # Handle field pagination
      "next_field_page" ->
        next_field_page(attrs, socket)
        |> noreply()

      "prev_field_page" ->
        prev_field_page(attrs, socket)
        |> noreply()
    end
  end

  def validate(%{"license_mapping" => params} = _attrs, socket) do
    # Get existing data from record if available
    existing_data =
      case socket.assigns.record do
        nil ->
          %{}

        record ->
          record.data || %{}
      end

    # Merge existing data with current form params
    merged_data = Map.merge(existing_data, params)

    # Use merged data for validation
    changeset =
      DynamicFormValidator.validate_step(
        merged_data,
        socket.assigns.text_fields,
        step: socket.assigns.current_position
      )
      |> Map.put(:action, :validate)

    # Save with merged data
    Licenses.save_license_mapping_field(socket, merged_data)
    |> case do
      {:ok, %{"field_registration" => record}} ->
        IO.inspect(record, label: "Updated Record After Save")

        socket
        |> assign(:form, to_form(changeset, as: "license_mapping"))
        |> assign(:record, record)

      {:error, error} ->
        IO.inspect(error, label: "Validation Save Error")

        LiveFunctions.sweet_alert(
          socket,
          "Error processing registration: #{inspect(error)}",
          "error"
        )
    end
  end

  defp get_update(socket, params) do
    Entry.init(socket, params, "text_fields")
  end

  # Handle next field page navigation
  defp next_field_page(attrs, socket) do
    current_params = attrs["license_mapping"] || %{}

    # Get existing data from record if available
    existing_data =
      case socket.assigns.record do
        nil ->
          %{}

        record ->
          record.data || %{}
      end

    # Merge current form data with existing data
    merged_data = Map.merge(existing_data, current_params)

    # Create changeset for validation
    _changeset =
      DynamicFormValidator.validate_step(
        merged_data,
        socket.assigns.text_fields,
        step: socket.assigns.current_position
      )

    # Check if we need to validate current page before proceeding
    current_page_fields = get_current_page_fields(socket)

    current_page_changeset =
      DynamicFormValidator.validate_step(
        merged_data,
        current_page_fields,
        step: socket.assigns.current_position
      )

    if current_page_changeset.valid? do
      # Save current page data
      case Licenses.save_license_mapping_field(socket, merged_data) do
        {:ok, %{"field_registration" => record}} ->
          # Create new form with all data (existing + current page)
          updated_changeset =
            DynamicFormValidator.validate_step(
              record.data || %{},
              socket.assigns.text_fields,
              step: socket.assigns.current_position
            )

          new_sub_step = socket.assigns.current_sub_step + 1

          socket
          |> assign(:record, record)
          |> assign(:form, to_form(updated_changeset, as: "license_mapping"))
          |> assign(:current_sub_step, new_sub_step)

        {:error, error} ->
          IO.inspect(error, label: "Next Page Save Error")

          LiveFunctions.sweet_alert(
            socket,
            "Error saving field data: #{inspect(error)}",
            "error"
          )
      end
    else
      # If validation fails, show errors but don't proceed
      IO.inspect(current_page_changeset.errors, label: "Current Page Validation Errors")

      socket
      |> assign(
        :form,
        to_form(Map.put(current_page_changeset, :action, :validate), as: "license_mapping")
      )
    end
  end

  # Handle previous field page navigation
  defp prev_field_page(attrs, socket) do
    current_params = attrs["license_mapping"] || %{}

    # Get existing data from record if available
    existing_data =
      case socket.assigns.record do
        nil ->
          %{}

        record ->
          record.data || %{}
      end

    # Merge current form data with existing data
    merged_data = Map.merge(existing_data, current_params)

    # Save current page data before going back (don't require validation)
    case Licenses.save_license_mapping_field(socket, merged_data) do
      {:ok, %{"field_registration" => record}} ->
        # Create new form with all data
        updated_changeset =
          DynamicFormValidator.validate_step(
            record.data || %{},
            socket.assigns.text_fields,
            step: socket.assigns.current_position
          )

        new_sub_step = max(socket.assigns.current_sub_step - 1, 0)

        socket
        |> assign(:record, record)
        |> assign(:form, to_form(updated_changeset, as: "license_mapping"))
        |> assign(:current_sub_step, new_sub_step)

      {:error, error} ->
        IO.inspect(error, label: "Prev Page Save Error")
        # Even if save fails, allow navigation back
        new_sub_step = max(socket.assigns.current_sub_step - 1, 0)

        socket
        |> assign(:current_sub_step, new_sub_step)
    end
  end

  # Helper function to get current page fields
  defp get_current_page_fields(socket) do
    total_fields = length(socket.assigns.text_fields)
    fields_per_page = socket.assigns.fields_per_page
    current_sub_step = socket.assigns.current_sub_step

    start_index = current_sub_step * fields_per_page
    _end_index = min(start_index + fields_per_page - 1, total_fields - 1)

    Enum.slice(socket.assigns.text_fields, start_index, fields_per_page)
  end

  defp reg_info_save(attrs, socket) do
    # Get all form data (from all pages) by merging current attrs with existing record data
    all_form_data =
      case socket.assigns.record do
        nil -> attrs["license_mapping"] || %{}
        record -> Map.merge(record.data || %{}, attrs["license_mapping"] || %{})
      end

    # First validate with our dynamic validator using all form data
    changeset =
      DynamicFormValidator.validate_step(
        all_form_data,
        socket.assigns.text_fields,
        step: socket.assigns.current_position
      )

    if changeset.valid? do
      Licenses.create_registration(
        socket,
        socket.assigns.record,
        all_form_data
      )
      |> case do
        {:ok, %{"license_registration" => record}} ->
          socket
          |> assign(:record, record)
          # Reset sub-step for next main step
          |> assign(current_sub_step: 0)
          |> Entry.next_position(record)

        {:error, error} ->
          LiveFunctions.sweet_alert(
            socket,
            "Error processing registration: #{inspect(error)}",
            "error"
          )
      end
    else
      # If validation fails, return the form with errors
      IO.inspect(changeset.errors, label: "Final Validation Errors")

      socket
      |> assign(:form, to_form(Map.put(changeset, :action, :validate), as: "license_mapping"))
    end
  end

  defp submit(socket, _data) do
    attrs =
      Map.merge(socket.assigns.upload_form.params, socket.assigns.form.source.changes)
      |> Map.merge(socket.assigns.params)

    case Licenses.insert_or_update_registration(socket, socket.assigns.record, attrs) do
      {:ok, _record} ->
        {
          :noreply,
          push_navigate(
            LiveFunctions.sweet_alert(socket, "Application Submitted Successfully", "success"),
            to: ~p"/dashboard"
          )
          |> assign(:doc_details, %{})
        }

      {:error, message} ->
        LiveFunctions.sweet_alert(socket, message, "error")

        {
          :noreply,
          assign(socket, data_loader: false)
        }
    end
  end

  def submit_correction(attrs, socket) do
    case Licenses.update_license_correction(socket, socket.assigns.record.id, attrs) do
      {:ok, record} ->
        {
          :noreply,
          socket
          |> Entry.next_position(record["license_registration_correction"])
        }

      {:error, message} ->
        LiveFunctions.sweet_alert(socket, message, "error")

        {
          :noreply,
          assign(socket, data_loader: false)
        }
    end
  end
end

defmodule AppWeb.DocumentUploadComponent do
  @moduledoc """
  Component for handling document uploads with previews.
  """
  use Phoenix.Component

  def document_upload_field(assigns) do
    ~H"""
    <div class="flex items-center justify-center w-full">
      <label
        phx-drop-target={@uploads.ref}
        class="w-full border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
      >
        <%= if @uploads.entries == [] do %>
          <div class="flex flex-col items-center justify-center pt-3 pb-4">
            <svg
              aria-hidden="true"
              class="w-8 h-8 mb-2 text-brand-10"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              />
            </svg>

            <p class="mb-1 text-xs text-gray-500">
              <span class="font-semibold text-brand-10">Click to upload</span> or drag and drop
            </p>

            <p class="text-xs text-gray-500">e.g., png, docx, jpeg, pdf - Auto-uploads on selection</p>
          </div>
        <% else %>
          <div class="p-3">
            <%= for entry <- @uploads.entries do %>
              <div class="mt-4 bg-white rounded-lg border border-gray-200 p-4">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <div class="flex-shrink-0">
                      <%= if String.ends_with?(entry.client_name, [".jpg", ".jpeg", ".png"]) do %>
                        <svg
                          class="w-8 h-8 text-brand-10"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                      <% else %>
                        <svg
                          class="w-8 h-8 text-red-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                          />
                        </svg>
                      <% end %>
                    </div>

                    <div class="min-w-0 flex-1">
                      <p class="text-sm font-medium text-gray-900 truncate">
                        <%= String.slice(entry.client_name, 0, 20) <>
                          if String.length(entry.client_name) > 20, do: "...", else: "" %>
                      </p>

                      <p class="text-xs text-gray-500">
                        <%= NumberF.comma_separated(div(entry.client_size, 1000), 0) %> KB
                      </p>
                    </div>
                  </div>

                  <div class="flex items-center space-x-2">
                    <%= if entry.progress < 100 do %>
                      <div class="flex items-center">
                        <div class="w-16 bg-gray-200 rounded-full h-2.5 mr-2">
                          <div
                            class="bg-brand-10 h-2.5 rounded-full"
                            style={"width: #{entry.progress}%"}
                          />
                        </div>
                        <span class="text-xs text-gray-500"><%= entry.progress %>%</span>
                      </div>
                    <% end %>

                    <button
                      type="button"
                      phx-click="cancel-entry"
                      phx-value-ref={entry.ref}
                      class="inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      <svg
                        class="h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  </div>
                </div>

                <%= for err <- upload_errors(@uploads, entry) do %>
                  <div class="mt-2 text-sm text-red-600">
                    <%= error_to_string(err) %>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        <% end %>
        <.live_file_input upload={@uploads} class="hidden" />
      </label>
    </div>
    """
  end

  # Convert error atoms to readable strings
  defp error_to_string(:too_large), do: "The file is too large (max 10MB)"
  defp error_to_string(:not_accepted), do: "Unacceptable file type (only PDF, JPG, JPEG, PNG)"
  defp error_to_string(:too_many_files), do: "You can only upload one file"
  defp error_to_string(error), do: "Error: #{inspect(error)}"
end

# <div class="upload-container">
#   <div class="flex items-center justify-center w-full" phx-drop-target={@uploads.ref}>
#     <label
#       for={@uploads.ref}
#       class="flex flex-col items-center justify-center w-full h-56 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
#     >
#       <div class="flex flex-col items-center justify-center pt-5 pb-6">
#         <svg
#           class="w-8 h-8 mb-4 text-gray-500"
#           aria-hidden="true"
#           xmlns="http://www.w3.org/2000/svg"
#           fill="none"
#           viewBox="0 0 20 16"
#         >
#           <path
#             stroke="currentColor"
#             stroke-linecap="round"
#             stroke-linejoin="round"
#             stroke-width="2"
#             d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
#           />
#         </svg>

#         <p class="mb-2 text-sm text-gray-500">
#           <span class="font-semibold">Click to upload</span> or drag and drop
#         </p>

#         <p class="text-xs text-gray-500">PDF, PNG, JPG or JPEG (Max 5MB)</p>
#       </div>
#        <.live_file_input upload={@uploads} class="hidden" />
#     </label>
#   </div>

# <%= for entry <- @uploads.entries do %>
#   <div class="mt-4 bg-white rounded-lg border border-gray-200 p-4">
#     <div class="flex items-center justify-between">
#       <div class="flex items-center space-x-2">
# <div class="flex-shrink-0">
#   <%= if String.ends_with?(entry.client_name, [".jpg", ".jpeg", ".png"]) do %>
#     <svg
#       class="w-8 h-8 text-brand-10"
#       fill="none"
#       stroke="currentColor"
#       viewBox="0 0 24 24"
#       xmlns="http://www.w3.org/2000/svg"
#     >
#       <path
#         stroke-linecap="round"
#         stroke-linejoin="round"
#         stroke-width="2"
#         d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
#       />
#     </svg>
#   <% else %>
#     <svg
#       class="w-8 h-8 text-red-600"
#       fill="none"
#       stroke="currentColor"
#       viewBox="0 0 24 24"
#       xmlns="http://www.w3.org/2000/svg"
#     >
#       <path
#         stroke-linecap="round"
#         stroke-linejoin="round"
#         stroke-width="2"
#         d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
#       />
#     </svg>
#   <% end %>
# </div>

#         <div class="min-w-0 flex-1">
#           <p class="text-sm font-medium text-gray-900 truncate">
#             <%= entry.client_name %>
#           </p>

#           <p class="text-xs text-gray-500">
#             <%= NumberF.comma_separated(div(entry.client_size, 1000), 0) %> KB
#           </p>
#         </div>
#       </div>

#       <div class="flex items-center space-x-2">
#         <%= if entry.progress < 100 do %>
#           <div class="flex items-center">
#             <div class="w-16 bg-gray-200 rounded-full h-2.5 mr-2">
#               <div class="bg-brand-10 h-2.5 rounded-full" style={"width: #{entry.progress}%"} />
#             </div>
#              <span class="text-xs text-gray-500"><%= entry.progress %>%</span>
#           </div>
#         <% end %>

# <button
#   type="button"
#   phx-click="cancel-entry"
#   phx-value-ref={entry.ref}
#   class="inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
# >
#   <svg
#     class="h-4 w-4"
#     xmlns="http://www.w3.org/2000/svg"
#     fill="none"
#     viewBox="0 0 24 24"
#     stroke="currentColor"
#     aria-hidden="true"
#   >
#     <path
#       stroke-linecap="round"
#       stroke-linejoin="round"
#       stroke-width="2"
#       d="M6 18L18 6M6 6l12 12"
#     />
#   </svg>
# </button>
#       </div>
#     </div>

# <%= for err <- upload_errors(@uploads, entry) do %>
#   <div class="mt-2 text-sm text-red-600">
#     <%= error_to_string(err) %>
#   </div>
# <% end %>
#     </div>
#   <% end %>
# </div>

defmodule AppWeb.Auth.LicenceRegistrationLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias Logs.Audit
  alias App.Licenses
  alias App.Licenses.DynamicSchema
  alias App.Validators.DynamicFormValidator

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    IO.inspect(params, label: "Licence Registration Params")

    if Audit.page_access("license_registration", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Licence Registration", "Accessed Licence Registration Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      steps = [
        %{position: 0, name: "Registration Information", icon: "hero-clipboard-document"},
        %{position: 1, name: "Document Upload", icon: "hero-document-arrow-up"},
        %{position: 2, name: "Review & Submit", icon: "hero-clipboard-document-check"}
      ]

      license_fields = Licenses.get_license_data(params["license_id"])

      # Organize fields
      text_fields =
        Enum.filter(
          license_fields,
          &(&1.field_type in [
              "text",
              "date",
              "select",
              "number",
              "textarea",
              "checkbox",
              "radio",
              "checkbox_group"
            ])
        )
        |> Enum.sort_by(fn field ->
          case field.field_type do
            "text" -> 0
            "date" -> 1
            "textarea" -> 3
            _ -> 2
          end
        end)

      upload_fields = Enum.filter(license_fields, &(&1.field_type == "upload"))

      input_fields =
        text_fields
        |> Enum.map(fn field ->
          {String.to_atom(field.field_name), field.field_name}
        end)

      record =
        Licenses.get_license_mapping_by_license_id_and_user_id(
          params["license_id"],
          assigns.current_user.id
        )

      data =
        if is_nil(record),
          do: %{},
          else: record.data

      # Create initial changeset with the dynamic validator
      initial_changeset =
        DynamicFormValidator.validate_step(
          data,
          text_fields,
          step: 0
        )

      # Create upload changeset
      upload_changeset =
        DynamicFormValidator.validate_step(
          data,
          upload_fields,
          step: 1
        )

      socket =
        assign(socket, data: [])
        |> assign(data_loader: false)
        |> assign(min_date: Date.utc_today() |> Date.to_iso8601())
        |> assign(show_reason_modal: false)
        |> assign(
          get_attention_field: App.LicenseReviews.get_attention_field(params["license_id"])
        )
        |> assign(steps: steps)
        |> assign(chosen_licence: Licenses.get_license!(params["license_id"]))
        |> assign(record: record)
        |> assign(show_expiry_modal: false)
        # Convert changeset to form here
        |> assign(:form, to_form(initial_changeset, as: "license_mapping"))
        # Convert changeset to form here
        |> assign(:upload_form, to_form(upload_changeset, as: "upload"))
        |> assign(params: params)
        # Initialize as empty map instead of nil
        |> assign(:doc_details, %{})
        |> assign(license_id: params["license_id"])
        |> assign(license_fields: license_fields)
        |> assign(text_fields: text_fields)
        |> assign(input_fields: Keyword.keys(input_fields))
        |> assign(upload_fields1: upload_fields)
        |> assign(live_socket_id: session["live_socket_id"])
        |> assign(current_position: 0)
        # Field pagination support
        |> assign(current_sub_step: 0)
        |> assign(fields_per_page: 10)

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Licence Registration", "Accessed Licence Registration Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  defp configure_uploads(socket, upload_types) do
    Enum.reduce(upload_types, socket, fn {field, accepts}, acc ->
      allow_upload(acc, field, accept: accepts, max_entries: 1, max_file_size: 10_000_000)
    end)
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {:process_registration, params} ->
        IO.inspect(params, label: :ooooooooooggggggggggggggggggggggg)
        submit(socket, params)
    end
  end

  @impl true
  def handle_event("cancel-entry", params, socket) do
    cancel_upload(params, socket)
  end

  @impl true
  def handle_event(target, attrs, %{assigns: assigns} = socket) do
    case target |> IO.inspect(label: "ISUEEEEEEEEEEEEEEEEEE TARGET ") do
      "show_modal" ->
        show_modal_reason(attrs, socket)

      "submit_correction" ->
        submit_correction(attrs, socket)

      "reg_info_save" ->
        reg_info_save(attrs, socket)
        |> noreply()

      "doc_save" ->
        document_uploads(attrs["license_mapping"] || %{}, socket)
        |> noreply()

      "validate" ->
        validate(attrs, socket)
        |> noreply()

      "validate_doc" ->
        validate_doc(attrs, socket)

      "submit" ->
        send(self(), {:process_registration, assigns.data})

        {
          :noreply,
          socket
          |> assign(data_loader: true)
        }

      "back" ->
        {:noreply, pre_position(socket)}

      # Handle field pagination
      "next_field_page" ->
        IO.inspect(attrs, label: "Next Page Attrs")

        next_field_page(attrs, socket)
        |> noreply()

      "prev_field_page" ->
        IO.inspect(attrs, label: "Prev Page Attrs")

        prev_field_page(attrs, socket)
        |> noreply()
    end
  end

  def validate(%{"license_mapping" => params} = _attrs, socket) do
    IO.inspect(params, label: "Validate Params")

    # Get existing data from record if available
    existing_data =
      case socket.assigns.record do
        nil ->
          %{}

        record ->
          IO.inspect(record.data, label: "Existing Record Data")
          record.data || %{}
      end

    # Merge existing data with current form params
    merged_data = Map.merge(existing_data, params)
    IO.inspect(merged_data, label: "Merged Data for Validation")

    # Use merged data for validation
    changeset =
      DynamicFormValidator.validate_step(
        merged_data,
        socket.assigns.text_fields,
        step: socket.assigns.current_position
      )
      |> Map.put(:action, :validate)

    # Save with merged data
    Licenses.save_license_mapping_field(socket, merged_data)
    |> case do
      {:ok, %{"field_registration" => record}} ->
        IO.inspect(record, label: "Updated Record After Save")

        socket
        |> assign(:form, to_form(changeset, as: "license_mapping"))
        |> assign(:record, record)

      {:error, error} ->
        IO.inspect(error, label: "Validation Save Error")

        LiveFunctions.sweet_alert(
          socket,
          "Error processing registration: #{inspect(error)}",
          "error"
        )
    end
  end

  def show_modal_reason(_attrs, socket) do
    {:noreply, assign(socket, show_reason_modal: true)}
  end

  def submit_correction(attrs, socket) do
    case Licenses.update_license_correction(socket, attrs["license_id"], attrs) do
      {:ok, _record} ->
        {
          :noreply,
          push_navigate(
            LiveFunctions.sweet_alert(socket, "Application updated Successfully", "success"),
            to: ~p"/dashboard"
          )
          |> assign(:doc_details, %{})
        }

      {:error, message} ->
        LiveFunctions.sweet_alert(socket, message, "error")

        {
          :noreply,
          assign(socket, data_loader: false)
        }
    end
  end

  def maybe_show_expiry_modal(attrs, socket) do
    if attrs["expiry_date"] == "true" do
      {:noreply, assign(socket, show_expiry_modal: true)}
    else
      {:noreply, assign(socket, show_expiry_modal: false)}
    end
  end

  # Handle next field page navigation
  defp next_field_page(attrs, socket) do
    current_params = attrs["license_mapping"] || %{}
    IO.inspect(current_params, label: "Current Form Params")

    # Get existing data from record if available
    existing_data =
      case socket.assigns.record do
        nil ->
          %{}

        record ->
          IO.inspect(record.data, label: "Existing Record Data for Next Page")
          record.data || %{}
      end

    # Merge current form data with existing data
    merged_data = Map.merge(existing_data, current_params)
    IO.inspect(merged_data, label: "Merged Data for Next Page")

    # Create changeset for validation
    _changeset =
      DynamicFormValidator.validate_step(
        merged_data,
        socket.assigns.text_fields,
        step: socket.assigns.current_position
      )

    # Check if we need to validate current page before proceeding
    current_page_fields = get_current_page_fields(socket)

    current_page_changeset =
      DynamicFormValidator.validate_step(
        merged_data,
        current_page_fields,
        step: socket.assigns.current_position
      )

    if current_page_changeset.valid? do
      # Save current page data
      case Licenses.save_license_mapping_field(socket, merged_data) do
        {:ok, %{"field_registration" => record}} ->
          IO.inspect(record, label: "Record After Next Page Save")

          # Create new form with all data (existing + current page)
          updated_changeset =
            DynamicFormValidator.validate_step(
              record.data || %{},
              socket.assigns.text_fields,
              step: socket.assigns.current_position
            )

          new_sub_step = socket.assigns.current_sub_step + 1
          IO.inspect(new_sub_step, label: "New Sub Step")

          socket
          |> assign(:record, record)
          |> assign(:form, to_form(updated_changeset, as: "license_mapping"))
          |> assign(:current_sub_step, new_sub_step)

        {:error, error} ->
          IO.inspect(error, label: "Next Page Save Error")

          LiveFunctions.sweet_alert(
            socket,
            "Error saving field data: #{inspect(error)}",
            "error"
          )
      end
    else
      # If validation fails, show errors but don't proceed
      IO.inspect(current_page_changeset.errors, label: "Current Page Validation Errors")

      socket
      |> assign(
        :form,
        to_form(Map.put(current_page_changeset, :action, :validate), as: "license_mapping")
      )
    end
  end

  # Handle previous field page navigation
  defp prev_field_page(attrs, socket) do
    current_params = attrs["license_mapping"] || %{}
    IO.inspect(current_params, label: "Current Form Params for Prev")

    # Get existing data from record if available
    existing_data =
      case socket.assigns.record do
        nil ->
          %{}

        record ->
          IO.inspect(record.data, label: "Existing Record Data for Prev Page")
          record.data || %{}
      end

    # Merge current form data with existing data
    merged_data = Map.merge(existing_data, current_params)
    IO.inspect(merged_data, label: "Merged Data for Prev Page")

    # Save current page data before going back (don't require validation)
    case Licenses.save_license_mapping_field(socket, merged_data) do
      {:ok, %{"field_registration" => record}} ->
        IO.inspect(record, label: "Record After Prev Page Save")

        # Create new form with all data
        updated_changeset =
          DynamicFormValidator.validate_step(
            record.data || %{},
            socket.assigns.text_fields,
            step: socket.assigns.current_position
          )

        new_sub_step = max(socket.assigns.current_sub_step - 1, 0)
        IO.inspect(new_sub_step, label: "New Sub Step for Prev")

        socket
        |> assign(:record, record)
        |> assign(:form, to_form(updated_changeset, as: "license_mapping"))
        |> assign(:current_sub_step, new_sub_step)

      {:error, error} ->
        IO.inspect(error, label: "Prev Page Save Error")
        # Even if save fails, allow navigation back
        new_sub_step = max(socket.assigns.current_sub_step - 1, 0)

        socket
        |> assign(:current_sub_step, new_sub_step)
    end
  end

  # Helper function to get current page fields
  defp get_current_page_fields(socket) do
    total_fields = length(socket.assigns.text_fields)
    fields_per_page = socket.assigns.fields_per_page
    current_sub_step = socket.assigns.current_sub_step

    start_index = current_sub_step * fields_per_page
    _end_index = min(start_index + fields_per_page - 1, total_fields - 1)

    Enum.slice(socket.assigns.text_fields, start_index, fields_per_page)
  end

  defp reg_info_save(attrs, socket) do
    # Get all form data (from all pages) by merging current attrs with existing record data
    all_form_data =
      case socket.assigns.record do
        nil -> attrs["license_mapping"] || %{}
        record -> Map.merge(record.data || %{}, attrs["license_mapping"] || %{})
      end

    IO.inspect(all_form_data, label: "All Form Data for Final Save")

    # First validate with our dynamic validator using all form data
    changeset =
      DynamicFormValidator.validate_step(
        all_form_data,
        socket.assigns.text_fields,
        step: socket.assigns.current_position
      )

    if changeset.valid? do
      uploads =
        socket.assigns.upload_fields1
        |> Enum.filter(fn field ->
          DynamicSchema.should_show_field?(field, all_form_data)
        end)
        |> Enum.map(fn field ->
          {String.to_atom(field.field_name), ~w(.pdf .jpg .png .jpeg)}
        end)

      Licenses.create_registration(
        socket,
        socket.assigns.record,
        all_form_data
      )
      |> case do
        {:ok, %{"license_registration" => record}} ->
          IO.inspect("Moving to next step", label: "DEBUG")

          socket
          |> assign(:record, record)
          |> configure_uploads(uploads)
          |> assign(upload_fields: Keyword.keys(uploads))
          # Reset sub-step for next main step
          |> assign(current_sub_step: 0)
          |> next_position()

        {:error, error} ->
          IO.inspect(error, label: "Final Save Error")

          LiveFunctions.sweet_alert(
            socket,
            "Error processing registration: #{inspect(error)}",
            "error"
          )
      end
    else
      # If validation fails, return the form with errors
      IO.inspect(changeset.errors, label: "Final Validation Errors")

      socket
      |> assign(:form, to_form(Map.put(changeset, :action, :validate), as: "license_mapping"))
    end
  end

  defp submit(socket, _data) do
    attrs =
      Map.merge(socket.assigns.upload_form.params, socket.assigns.form.source.changes)
      |> Map.merge(socket.assigns.params)

    case Licenses.insert_or_update_registration(socket, socket.assigns.record, attrs) do
      {:ok, _record} ->
        {
          :noreply,
          push_navigate(
            LiveFunctions.sweet_alert(socket, "Application Submitted Successfully", "success"),
            to: ~p"/dashboard"
          )
          |> assign(:doc_details, %{})
        }

      {:error, message} ->
        LiveFunctions.sweet_alert(socket, message, "error")

        {
          :noreply,
          assign(socket, data_loader: false)
        }
    end
  end

  defp document_uploads(attrs, socket) do
    # Process each upload type and return the updated attributes
    updated_attrs =
      socket.assigns.upload_fields
      |> Enum.reduce(attrs, fn upload_type, acc ->
        acc |> Map.put("#{upload_type}", handle_upload(socket, upload_type, attrs))
      end)

    changeset =
      DynamicFormValidator.validate_step(
        updated_attrs,
        socket.assigns.upload_fields1,
        step: socket.assigns.current_position
      )
      |> Map.put(:action, :validate)

    if changeset.valid? do
      Licenses.create_registration(
        socket,
        socket.assigns.record,
        Map.merge(
          updated_attrs,
          socket.assigns.form.source.changes
          |> Jason.encode!()
          |> Jason.decode!()
        )
      )
      |> case do
        {:ok, %{"license_registration" => record}} ->
          assign(socket, :upload_form, to_form(changeset, as: "upload"))
          |> assign(:data, updated_attrs)
          |> assign(:doc_details, socket.assigns.uploads)
          |> assign(:record, record)
          |> next_position()

        {:error, error} ->
          LiveFunctions.sweet_alert(
            socket,
            "Error processing registration: #{inspect(error)}",
            "error"
          )
      end
    else
      # If validation fails, return the form with errors
      socket
      |> assign(:form, %{source: Map.put(changeset, :action, :validate)})
    end
  end

  defp handle_upload(socket, upload_type, attrs) do
    current_attachment = Map.get(socket.assigns, upload_type)

    if current_attachment && length(socket.assigns.uploads[upload_type].entries) > 0 do
      File.rm!(current_attachment)
      consume_upload(socket, upload_type, attrs)
    else
      if length(socket.assigns.uploads[upload_type].entries) > 0 do
        List.first(consume_upload(socket, upload_type, attrs))
      else
        List.first(current_attachment)
      end
    end
  end

  def cancel_upload(%{"ref" => ref}, socket) do
    get_field =
      socket.assigns.upload_fields
      |> Enum.find(fn field ->
        Enum.any?(socket.assigns.uploads[field].entries, fn entry -> entry.ref == ref end)
      end)

    if get_field do
      {:noreply, cancel_upload(socket, get_field, ref)}
    else
      {:noreply, socket}
    end
  end

  def validate_doc(%{"_target" => [field]}, %{assigns: assigns} = socket) do
    upload_field = String.to_atom(field)

    attrs =
      assigns.upload_fields
      |> Enum.reduce(%{}, fn key, acc ->
        # check existing entries
        existing_entries =
          assigns.uploads[key]
          |> Map.get(:entries, [])
          |> Enum.map(& &1.client_name)
          |> Enum.at(0)

        # If the current key matches field validating, fetch the entries
        updated_entries =
          if key == upload_field do
            assigns.uploads[upload_field]
            |> Map.get(:entries, [])
            |> Enum.map(& &1.client_name)
            |> Enum.at(0)
          else
            existing_entries
          end

        Map.put(acc, key, updated_entries)
      end)

    # Use our dynamic validator for uploads
    changeset =
      DynamicFormValidator.validate_step(
        attrs,
        assigns.upload_fields1,
        step: assigns.current_position
      )
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :upload_form, to_form(changeset, as: "upload"))}
  end

  def consume_upload(socket, upload_type, _attrs) do
    new_path = LiveFunctions.static_path("/uploads/licence_registration")

    consume_uploaded_entries(socket, upload_type, fn %{path: path}, entry ->
      file_name = "/#{Path.rootname(entry.client_name)}.#{LiveFunctions.ext(entry)}"
      dest = Path.join(new_path, file_name)

      if File.exists?(new_path <> "/") do
        File.cp(path, dest)
        {:ok, dest}
      else
        File.mkdir_p(new_path <> "/")
        File.cp_r(path, dest)
        {:ok, dest}
      end
    end)
  end

  defp next_position(socket), do: update(socket, :current_position, &(&1 + 1))

  defp pre_position(%{assigns: %{current_position: current_position}} = socket)
       when current_position >= 1,
       do: update(socket, :current_position, &(&1 - 1))

  defp pre_position(socket), do: socket

  def error_to_string(:too_large), do: "Too large"
  def error_to_string(:not_accepted), do: "You have selected an unacceptable file type"
  def error_to_string(:too_many_files), do: "You have selected too many files"
end

<!-- Enhanced License Registration Template - Aligned with Backend -->
<%= if @data_loader == false do %>
  <%= if @get_attention_field == [] do %>
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 pt-6 sticky top-12 z-10">
      <div class="bg-white rounded-lg shadow-md p-4">
        <ol class="flex flex-wrap md:flex-nowrap items-center w-full">
          <%= for {step, index} <- Enum.with_index(@steps) do %>
            <li class={"flex items-center #{if index < length(@steps) - 1, do: "w-full md:flex-1"} #{if index > 0, do: "mt-4 md:mt-0"}"}>
              <div class={"flex items-center justify-center #{if @current_position >= step.position, do: "bg-brand-10 text-white", else: "bg-gray-200 text-gray-500"} rounded-full h-8 w-8 md:h-10 md:w-10 flex-shrink-0"}>
                <.icon name={step.icon} class="h-4 w-4 md:h-5 md:w-5" />
              </div>

              <div class="ml-2 flex-1">
                <p class={"text-xs md:text-sm font-medium #{if @current_position >= step.position, do: "text-brand-10", else: "text-gray-500"}"}>
                  <%= step.name %>
                </p>
              </div>

              <%= if index < length(@steps) - 1 do %>
                <div class="hidden md:block w-full bg-gray-200 h-0.5 mx-2 md:mx-4">
                  <div
                    class={"#{if @current_position > step.position, do: "bg-brand-10", else: "bg-gray-200"} h-0.5"}
                    style="width: 100%"
                  >
                  </div>
                </div>
              <% end %>
            </li>
          <% end %>
        </ol>
      </div>
    </div>

    <%= if @current_position == 0 do %>
      <% # Calculate field pagination using backend values
      total_fields = length(@text_fields)
      fields_per_page = @fields_per_page || 10
      current_sub_step = @current_sub_step || 0
      should_paginate = total_fields > fields_per_page

      # Calculate pagination info
      {current_fields, pagination_info} =
        if should_paginate do
          total_pages = ceil(total_fields / fields_per_page)
          start_index = current_sub_step * fields_per_page
          _end_index = min(start_index + fields_per_page - 1, total_fields - 1)
          current_fields = Enum.slice(@text_fields, start_index, fields_per_page)

          {current_fields,
           %{
             current_page: current_sub_step + 1,
             total_pages: total_pages,
             has_previous: current_sub_step > 0,
             has_next: current_sub_step < total_pages - 1,
             start_field: start_index + 1,
             end_field: min(start_index + fields_per_page, total_fields),
             total_fields: total_fields
           }}
        else
          {@text_fields, nil}
        end %>
      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8 flex flex-col lg:flex-row gap-8">
        <!-- Main Form Container -->
        <div class="flex-1 bg-white rounded-lg shadow-md p-8">
          <div class="mb-8 border-b pb-5">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-2xl font-bold text-gray-900">
                  <%= @chosen_licence.name %> Registration
                </h1>

                <p class="mt-2 text-sm text-gray-600">
                  Please complete all required fields to register your license.
                </p>
                <!-- Field Progress Indicator -->
                <%= if should_paginate do %>
                  <div class="mt-4 flex items-center space-x-2">
                    <span class="text-sm text-gray-500">
                      Fields <%= pagination_info.start_field %>-<%= pagination_info.end_field %> of <%= pagination_info.total_fields %>
                    </span>
                    <!-- Mini Progress Bar -->
                    <div class="flex-1 max-w-xs bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-brand-10 h-2 rounded-full transition-all duration-300"
                        style={"width: #{Float.round(pagination_info.end_field / pagination_info.total_fields * 100, 1)}%"}
                      >
                      </div>
                    </div>

                    <span class="text-xs text-brand-10 font-medium">
                      <%= Float.round(
                        pagination_info.end_field / pagination_info.total_fields * 100,
                        0
                      ) %>%
                    </span>
                  </div>
                <% end %>
              </div>
              <!-- Page Indicator -->
              <%= if should_paginate do %>
                <div class="flex items-center space-x-1">
                  <%= for page <- 1..pagination_info.total_pages do %>
                    <div class={"w-2 h-2 rounded-full #{if page - 1 == current_sub_step, do: "bg-brand-10", else: "bg-gray-300"}"}>
                    </div>
                  <% end %>
                </div>
              <% end %>
            </div>
            <!-- Attention Fields -->
          </div>

          <.simple_form
            for={@form}
            id="license-registration-form"
            phx-submit={
              if should_paginate and pagination_info.has_next,
                do: "next_field_page",
                else: "reg_info_save"
            }
            phx-change="validate"
            class="space-y-8 mt-8"
          >
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <%= for field <- current_fields   do %>
                <% should_show =
                  App.Licenses.DynamicSchema.should_show_field?(field, @form.source.params || %{}) %>

                <%= if should_show do %>
                  <div class="space-y-2">
                    <%= case field.field_type do %>
                      <% "date" -> %>
                        <.input
                          field={@form[String.to_atom(field.field_name)]}
                          type="date"
                          phx-debounce="blur"
                          placeholder={"Enter #{field.field_label}"}
                          required={field.required}
                          label={
                            if field.required,
                              do:
                                raw(~c"#{field.field_label} <span class='text-rose-500'>*</span>"),
                              else: field.field_label
                          }
                          class="w-full rounded-md border-gray-300 shadow-sm focus:border-brand-2 focus:ring-brand-2"
                        />
                      <% "text" -> %>
                        <.input
                          field={@form[String.to_atom(field.field_name)]}
                          type="text"
                          phx-debounce="blur"
                          placeholder={"Enter #{field.field_label}"}
                          required={field.required}
                          label={
                            if field.required,
                              do:
                                raw(~c"#{field.field_label} <span class='text-rose-500'>*</span>"),
                              else: field.field_label
                          }
                          class="w-full rounded-md border-gray-300 shadow-sm focus:border-brand-2 focus:ring-brand-2"
                        />
                      <% "number" -> %>
                        <.input
                          field={@form[String.to_atom(field.field_name)]}
                          type="number"
                          phx-debounce="blur"
                          placeholder={"Enter #{field.field_label}"}
                          required={field.required}
                          min={field.field_validations["min"] || 1}
                          max={field.field_validations["max"] || 10000}
                          label={
                            if field.required,
                              do:
                                raw(~c"#{field.field_label} <span class='text-rose-500'>*</span>"),
                              else: field.field_label
                          }
                          class="w-full rounded-md border-gray-300 shadow-sm focus:border-brand-2 focus:ring-brand-2"
                        />
                      <% "textarea" -> %>
                        <.input
                          field={@form[String.to_atom(field.field_name)]}
                          type="textarea"
                          phx-debounce="blur"
                          placeholder={"Enter #{field.field_label}"}
                          required={field.required}
                          rows="4"
                          label={
                            if field.required,
                              do:
                                raw(~c"#{field.field_label} <span class='text-rose-500'>*</span>"),
                              else: field.field_label
                          }
                          class="w-full rounded-md border-gray-300 shadow-sm focus:border-brand-2 focus:ring-brand-2"
                        />
                      <% "checkbox" -> %>
                        <div class="flex items-start">
                          <.input
                            field={@form[String.to_atom(field.field_name)]}
                            type="checkbox"
                            phx-debounce="blur"
                            required={field.required}
                            label={
                              if field.required,
                                do:
                                  raw(
                                    ~c"#{field.field_label} <span class='text-rose-500'>*</span>"
                                  ),
                                else: field.field_label
                            }
                            class="h-4 w-4 rounded border-gray-300 text-brand-10 focus:ring-brand-2 mt-1 mr-2"
                          />
                        </div>
                      <% "select" -> %>
                        <.input
                          field={@form[String.to_atom(field.field_name)]}
                          type="select"
                          prompt="-----Select------"
                          options={field.field_options}
                          phx-debounce="blur"
                          required
                          label={
                            raw(~c"#{field.field_label} <span class='text-rose-500'>*</span>")
                          }
                          class="w-full rounded-md border-gray-300 shadow-sm focus:border-brand-2 focus:ring-brand-2"
                        />
                      <% "radio" -> %>
                        <div class="space-y-2">
                          <label class="block text-sm font-medium text-gray-700">
                            <%= field.field_label %>
                            <%= if field.required do %>
                              <span class="text-rose-500">*</span>
                            <% end %>
                          </label>

                          <div class="space-y-2">
                            <%= for option <- field.field_options || [] do %>
                              <div class="flex items-center">
                                <.input
                                  field={@form[String.to_atom(field.field_name)]}
                                  type="radio"
                                  name={field.field_name}
                                  value={option}
                                  required={field.required}
                                  label={option}
                                  phx-debounce="blur"
                                  checked={
                                    Phoenix.HTML.Form.input_value(
                                      @form,
                                      String.to_atom(field.field_name)
                                    ) == option
                                  }
                                  class="h-4 w-4 border-gray-300 text-brand-10 focus:ring-brand-2 mr-2"
                                />
                              </div>
                            <% end %>
                          </div>
                        </div>
                      <% "checkbox_group" -> %>
                        <div class="space-y-2">
                          <label class="block text-sm font-medium text-gray-700">
                            <%= field.field_label %>
                            <%= if field.required do %>
                              <span class="text-rose-500">*</span>
                            <% end %>
                          </label>

                          <div class="space-y-2">
                            <%= for option <- field.field_options || [] do %>
                              <div class="flex items-center">
                                <.input
                                  field={@form[String.to_atom(field.field_name)]}
                                  type="checkbox"
                                  phx-debounce="blur"
                                  name={"#{field.field_name}[]"}
                                  value={option}
                                  label={option}
                                  checked={
                                    option in (Phoenix.HTML.Form.input_value(
                                                 @form,
                                                 String.to_atom(field.field_name)
                                               ) || [])
                                  }
                                  class="h-4 w-4 rounded border-gray-300 text-brand-10 focus:ring-brand-2 mr-2"
                                />
                              </div>
                            <% end %>
                          </div>
                        </div>
                      <% _ -> %>
                        <div class="text-red-500 text-sm">
                          Unknown field type: <%= field.field_type %>
                        </div>
                    <% end %>

                    <%= if Map.get(field, :field_description) do %>
                      <p class="text-xs text-gray-500 mt-1"><%= field.field_description %></p>
                    <% end %>
                  </div>
                <% end %>
              <% end %>
            </div>
            <!-- Navigation Buttons -->
            <div class="flex justify-between pt-6 border-t">
              <div class="flex space-x-2">
                <!-- Back to Dashboard Button -->
                <.button
                  type="button"
                  onclick="history.back()"
                  class="bg-gray-500 hover:bg-brand-11"
                >
                  <i class="hero-chevron-double-left"></i> Back
                </.button>
                <!-- Previous Field Page Button -->
                <%= if should_paginate and pagination_info.has_previous do %>
                  <.button
                    type="button"
                    phx-click="prev_field_page"
                    class="bg-gray-500 hover:bg-brand-11"
                  >
                    <i class="hero-chevron-left"></i> Previous Fields
                  </.button>
                <% end %>
              </div>

              <div class="flex space-x-2">
                <!-- Next Field Page or Save & Continue Button -->
                <%= if should_paginate and pagination_info.has_next do %>
                  <.button
                    type="submit"
                    class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium bg-brand-10 hover:bg-brand-11 text-white"
                  >
                    Next Fields <i class="hero-chevron-right ml-1"></i>
                  </.button>
                <% else %>
                  <.button
                    type="submit"
                    class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium bg-brand-10 hover:bg-brand-11 text-white"
                  >
                    Next Step <i class="hero-chevron-double-right ml-1"></i>
                  </.button>
                <% end %>
              </div>
            </div>
          </.simple_form>
        </div>
        <!-- Document Requirements Panel -->
        <div class="w-full lg:w-1/3 bg-white p-6 rounded-lg shadow-md border border-gray-200 sticky top-6">
          <div class="flex items-center mb-4">
            <svg
              class="h-5 w-5 text-brand-10 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>

            <h3 class="text-lg font-medium text-gray-900">Required Documents</h3>
          </div>

          <p class="text-sm text-gray-600 mb-4">
            Please prepare the following documents for upload in the next step:
          </p>

          <ul class="space-y-3">
            <%= for field <- @upload_fields1 do %>
              <% should_show =
                App.Licenses.DynamicSchema.should_show_field?(field, @form.source.params || %{}) %>
              <%= if should_show do %>
                <li class="flex items-start">
                  <svg
                    class="h-5 w-5 text-brand-10 mr-2 mt-0.5 flex-shrink-0"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>

                  <div>
                    <span class="font-medium text-gray-700">
                      <%= field.field_label %>
                      <%= if field.required do %>
                        <span class="text-rose-500">*</span>
                      <% end %>
                    </span>

                    <%= if Map.get(field, :field_description) do %>
                      <p class="text-xs text-gray-500 mt-1"><%= field.field_description %></p>
                    <% end %>
                  </div>
                </li>
              <% end %>
            <% end %>
          </ul>

          <div class="mt-6 pt-4 border-t border-gray-200">
            <h4 class="text-sm font-medium text-gray-800 mb-2">Document Requirements:</h4>

            <ul class="text-xs text-gray-600 space-y-1">
              <li class="flex items-start">
                <svg
                  class="h-3.5 w-3.5 text-gray-400 mr-1.5 mt-0.5 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                File format: PDF, JPG, or PNG
              </li>

              <li class="flex items-start">
                <svg
                  class="h-3.5 w-3.5 text-gray-400 mr-1.5 mt-0.5 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                Maximum file size: 10MB per document
              </li>

              <li class="flex items-start">
                <svg
                  class="h-3.5 w-3.5 text-gray-400 mr-1.5 mt-0.5 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                Documents must be clear and legible
              </li>
            </ul>
          </div>

          <div class="mt-6 bg-yellow-50 p-3 rounded-md border border-yellow-200">
            <div class="flex">
              <svg
                class="h-5 w-5 text-yellow-600 mr-2 flex-shrink-0"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>

              <div>
                <p class="text-xs font-medium text-yellow-800">
                  All documents must be valid and certified at the time of submission.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>
    <!-- Keep existing steps 1 and 2 unchanged -->
    <%= if @current_position == 1 do %>
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 bg-white rounded-lg shadow-md mt-8">
        <div class="mb-8 border-b pb-5">
          <h1 class="text-2xl font-bold text-gray-900">
            <%= @chosen_licence.name %> Registration
          </h1>

          <p class="mt-2 text-sm text-gray-600">
            Please upload all required documents to proceed with your registration.
          </p>
        </div>

        <.simple_form
          for={@upload_form}
          id="upload-form"
          phx-submit="doc_save"
          phx-change="validate_doc"
          class="space-y-8"
        >
          <h2 class="text-lg font-medium text-gray-900 mb-4">Document Uploads</h2>

          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <%= for field <- Enum.filter(@license_fields, &(&1.field_type == "upload")) do %>
              <%= if is_nil(field.field_dependents) ||
                  field.dependent_selection == (if !Enum.empty?(field.field_dependents),
                    do: @form.source.changes[String.to_atom(List.first(field.field_dependents))]) do %>
                <div class="space-y-3">
                  <label class="block text-sm font-medium text-gray-700">
                    <%= field.field_label %> <span class="text-rose-500">*</span>
                  </label>

                  <div>
                    <AppWeb.DocumentUploadComponent.document_upload_field uploads={
                      @uploads[String.to_atom(field.field_name)]
                    } />
                    <%= if @upload_form.source.errors[String.to_atom(field.field_name)] do %>
                      <p class="mt-2 text-sm text-red-600">
                        <%= elem(@upload_form.errors[String.to_atom(field.field_name)], 0) %>
                      </p>
                    <% end %>
                  </div>

                  <%= if Map.get(field, :field_description) do %>
                    <p class="text-xs text-gray-500 mt-1"><%= field.field_description %></p>
                  <% end %>
                </div>
              <% end %>
            <% end %>
          </div>

          <div class="flex justify-end pt-6 border-t">
            <.button type="button" phx-click="back">
              <b><i class="hero-chevron-double-left position-left"></i></b> back
            </.button>

            <%= if @upload_form.source.valid? do %>
              <.button
                type="submit"
                class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium"
              >
                Next<b><i class="hero-chevron-double-right position-right"></i></b>
              </.button>
            <% end %>
          </div>
        </.simple_form>
      </div>
    <% end %>

    <%= if @current_position == 2 do %>
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 bg-white rounded-lg shadow-md mt-8">
        <div class="mb-8 border-b pb-5">
          <h1 class="text-2xl font-bold text-gray-900">
            <%= @chosen_licence.name %> Registration
          </h1>

          <p class="mt-2 text-sm text-gray-600">
            Please review your information and documents before submission.
          </p>
        </div>
        <%!-- Summary of information --%>
        <div class="bg-gray-50 p-6 rounded-lg border border-gray-200 mb-8">
          <h2 class="text-lg font-medium text-gray-900 mb-4">Summary of Information</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <%= for field <- Enum.filter(@license_fields, &(&1.field_type != "upload")) do %>
              <%= if App.Licenses.DynamicSchema.should_show_field?(field, @form.source.params || %{}) do %>
                <div class="py-2 border-b border-gray-100">
                  <div class="text-sm font-medium text-gray-500">
                    <%= field.field_label %>
                  </div>

                  <div class="text-md text-gray-900">
                    <%= case field.field_type do %>
                      <% "checkbox" -> %>
                        <%= if Phoenix.HTML.Form.input_value(
                                 @form,
                                 String.to_atom(field.field_name)
                               ),
                               do: "Yes",
                               else: "No" %>
                      <% "checkbox_group" -> %>
                        <%= values =
                          Phoenix.HTML.Form.input_value(@form, String.to_atom(field.field_name)) ||
                            []

                        Enum.join(values, ", ") %>
                      <% _ -> %>
                        <%= Phoenix.HTML.Form.input_value(@form, String.to_atom(field.field_name)) ||
                          "Not provided" %>
                    <% end %>
                  </div>
                </div>
              <% end %>
            <% end %>
          </div>

          <h3 class="text-md font-medium text-gray-900 mt-6 mb-2">Uploaded Documents</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <%= for field <- Enum.filter(@license_fields, &(&1.field_type == "upload")) do %>
              <%= if is_nil(field.field_dependents) ||
                              field.dependent_selection == (if !Enum.empty?(field.field_dependents),
                                do: @form.source.changes[String.to_atom(List.first(field.field_dependents))]) do %>
                <div class="py-2 border-b border-gray-100">
                  <div class="text-sm font-medium text-gray-500">
                    <%= field.field_label %>
                  </div>

                  <div class="text-md text-gray-900">
                    <%= if @doc_details && Map.has_key?(@doc_details, String.to_atom(field.field_name)) &&
                         Enum.any?(@doc_details[String.to_atom(field.field_name)].entries) do %>
                      <%= for entry <- @doc_details[String.to_atom(field.field_name)].entries do %>
                        <div class="flex items-start">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-5 w-5 text-green-500 mr-2 mt-0.5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            />
                          </svg>

                          <div>
                            <p class="font-medium"><%= entry.client_name %></p>

                            <p class="text-xs text-gray-500">
                              <%= if entry.client_type,
                                do: entry.client_type,
                                else: "Unknown type" %> &bull; <%= NumberF.comma_separated(
                                div(entry.client_size, 1000),
                                1
                              ) %> KB
                            </p>
                          </div>
                        </div>
                      <% end %>
                    <% else %>
                      <span class="text-orange-500">Not uploaded</span>
                    <% end %>
                  </div>
                </div>
              <% end %>
            <% end %>
          </div>

          <div class="mt-6 text-md text-gray-500">
            Please review the information above before submitting.
          </div>

          <div class="flex justify-end pt-6 border-t">
            <.button type="button" phx-click="back">
              <b><i class="hero-chevron-double-left position-left"></i></b> back
            </.button>

            <.button
              type="button"
              phx-disable-with="Submitting..."
              phx-click={if @current_position == 1, do: "doc_save", else: "submit"}
              class="ml-3 inline-flex items-center px-6 py-2 text-sm font-medium text-white bg-brand-10 border border-transparent rounded-md shadow-sm hover:bg-brand-11 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-2"
            >
              <.icon name="hero-check-circle" class="h-5 w-5 mr-2" /> Submit
            </.button>
          </div>
        </div>
      </div>
    <% end %>
  <% else %>
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-white rounded-lg shadow-md">
        <div class="p-6">
          <div class="text-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">
              <%= @chosen_licence.name %> Registration
            </h1>
            <p class="mt-2 text-sm text-gray-600">
              Please review and correct the highlighted fields below.
            </p>
          </div>
          <.simple_form
            for={@form}
            id="license-registration-form"
            phx-submit="submit_correction"
            phx-change="validate"
          >
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <%= for field <- @text_fields do %>
                <%= for attention <- @get_attention_field do %>
                  <%= if attention.attention_field == field.field_label do %>
                    <div class="space-y-4">
                      <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-md">
                        <div class="flex">
                          <div class="flex-shrink-0">
                            <svg
                              class="h-5 w-5 text-red-400"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fill-rule="evenodd"
                                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                clip-rule="evenodd"
                              />
                            </svg>
                          </div>
                          <div class="ml-3">
                            <p class="text-sm text-red-700 font-medium">
                              <%= attention.attention_field %>
                            </p>
                            <p class="mt-1 text-xs text-red-600">
                              <%= attention.reason %>
                            </p>
                          </div>
                        </div>
                      </div>
                      <input type="hidden" name="license_id" value={attention.license_id} />
                      <%= case field.field_type  do %>
                        <% "date" -> %>
                          <.input
                            field={@form[String.to_atom(field.field_name)]}
                            type="date"
                            phx-debounce="blur"
                            required={field.required}
                            label={
                              if field.required,
                                do:
                                  raw(
                                    ~c"#{field.field_label} <span class='text-rose-500'>*</span>"
                                  ),
                                else: field.field_label
                            }
                            class="w-full"
                          />
                        <% "text" -> %>
                          <.input
                            field={@form[String.to_atom(field.field_name)]}
                            type="text"
                            phx-debounce="blur"
                            required={field.required}
                            label={
                              if field.required,
                                do:
                                  raw(
                                    ~c"#{field.field_label} <span class='text-rose-500'>*</span>"
                                  ),
                                else: field.field_label
                            }
                            class="w-full"
                          />
                        <% "number" -> %>
                          <.input
                            field={@form[String.to_atom(field.field_name)]}
                            type="number"
                            phx-debounce="blur"
                            required={field.required}
                            min={field.field_validations["min"] || 1}
                            max={field.field_validations["max"] || 10000}
                            label={
                              if field.required,
                                do:
                                  raw(
                                    ~c"#{field.field_label} <span class='text-rose-500'>*</span>"
                                  ),
                                else: field.field_label
                            }
                            class="w-full"
                          />
                        <% "textarea" -> %>
                          <.input
                            field={@form[String.to_atom(field.field_name)]}
                            type="textarea"
                            phx-debounce="blur"
                            required={field.required}
                            rows="3"
                            label={
                              if field.required,
                                do:
                                  raw(
                                    ~c"#{field.field_label} <span class='text-rose-500'>*</span>"
                                  ),
                                else: field.field_label
                            }
                            class="w-full"
                          />
                        <% "checkbox" -> %>
                          <.input
                            field={@form[String.to_atom(field.field_name)]}
                            type="checkbox"
                            phx-debounce="blur"
                            required={field.required}
                            label={
                              if field.required,
                                do:
                                  raw(
                                    ~c"#{field.field_label} <span class='text-rose-500'>*</span>"
                                  ),
                                else: field.field_label
                            }
                          />
                        <% "select" -> %>
                          <.input
                            field={@form[String.to_atom(field.field_name)]}
                            type="select"
                            prompt="Select an option"
                            options={field.field_options}
                            phx-debounce="blur"
                            required
                            label={
                              raw(~c"#{field.field_label} <span class='text-rose-500'>*</span>")
                            }
                            class="w-full"
                          />
                        <% "radio" -> %>
                          <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                              <%= field.field_label %>
                              <%= if field.required do %>
                                <span class="text-rose-500">*</span>
                              <% end %>
                            </label>
                            <div class="space-y-2">
                              <%= for option <- field.field_options || [] do %>
                                <div class="flex items-center">
                                  <.input
                                    field={@form[String.to_atom(field.field_name)]}
                                    type="radio"
                                    name={field.field_name}
                                    value={option}
                                    required={field.required}
                                    label={option}
                                    phx-debounce="blur"
                                    checked={
                                      Phoenix.HTML.Form.input_value(
                                        @form,
                                        String.to_atom(field.field_name)
                                      ) == option
                                    }
                                  />
                                </div>
                              <% end %>
                            </div>
                          </div>
                        <% "checkbox_group" -> %>
                          <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                              <%= field.field_label %>
                              <%= if field.required do %>
                                <span class="text-rose-500">*</span>
                              <% end %>
                            </label>
                            <div class="space-y-2">
                              <%= for option <- field.field_options || [] do %>
                                <div class="flex items-center">
                                  <.input
                                    field={@form[String.to_atom(field.field_name)]}
                                    type="checkbox"
                                    phx-debounce="blur"
                                    name={"#{field.field_name}[]"}
                                    value={option}
                                    label={option}
                                    checked={
                                      option in (Phoenix.HTML.Form.input_value(
                                                   @form,
                                                   String.to_atom(field.field_name)
                                                 ) || [])
                                    }
                                  />
                                </div>
                              <% end %>
                            </div>
                          </div>
                        <% "upload" -> %>
                          <AppWeb.DocumentUploadComponent.document_upload_field uploads={
                            @uploads[String.to_atom(field.field_name)]
                          } />
                        <% _ -> %>
                          <div class="text-red-500 text-sm">
                            Unknown field type: <%= field.field_type %>
                          </div>
                      <% end %>

                      <%= if Map.get(field, :field_description) do %>
                        <p class="text-xs text-gray-500 mt-2">
                          <%= field.field_description %>
                        </p>
                      <% end %>
                    </div>
                  <% end %>
                <% end %>
              <% end %>
            </div>
            <div class="flex justify-between mt-8 pt-6 border-t">
              <.button
                type="button"
                onclick="history.back()"
                class="inline-flex items-center px-6 py-2 text-sm font-medium text-white bg-brand-10 border border-transparent rounded-md shadow-sm hover:bg-brand-11 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-2"
              >
                <i class="hero-chevron-double-left"></i> Back
              </.button>

              <.button
                type="submit"
                phx-disable-with="Submitting..."
                class="inline-flex items-center px-6 py-2 text-sm font-medium text-white bg-brand-10 border border-transparent rounded-md shadow-sm hover:bg-brand-11 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-2"
              >
                <.icon name="hero-check-circle" class="h-5 w-5 mr-2" /> Submit
              </.button>
            </div>
          </.simple_form>
        </div>
      </div>
    </div>
  <% end %>
<% else %>
  <!-- Loading Spinner -->
  <div
    id="main-loader"
    class="fixed inset-0 flex flex-col items-center justify-center bg-gray-100 bg-opacity-75 z-50"
  >
    <div class="w-10 h-10 border-4 border-t-transparent border-brand-10 rounded-full animate-spin">
    </div>

    <p class="mt-4 text-lg font-semibold text-gray-700">Loading, please wait...</p>
  </div>
<% end %>

defmodule AppWeb.Auth.Registration.PaymentsLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias App.{Companies, Licenses}

  alias AppWeb.Auth.RegistrationLive.Entry

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("client_applications-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Payments", "Accessed Payments Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(params: params)
        |> assign(is_status: Licenses.checking_current_status(socket.assigns.current_user.id))
        |> assign(:search_form, to_form(%{"nrc" => ""}))
        |> assign(:search_results, [])
        |> assign(:searching, false)
        |> assign(:searched, false)
        |> assign(:search_query, "")
        |> assign(confirmation_model: false)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(session_id: session["live_socket_id"])
        |> assign(live_socket_id: session["live_socket_id"])

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Payments", "Accessed Payments Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_data, params})

    {
      :noreply,
      socket
      |> assign(:params, params)
      |> apply_action(socket.assigns.live_action, params)
    }
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "")
  end

  @impl true
  def handle_info({:get_data, params}, socket), do: get_update(socket, params)

  def handle_info({:search, nrc}, socket) do
    socket =
      socket
      |> assign(:search_results, Companies.search_associate!(nrc))
      |> assign(:searching, false)
      |> assign(:searched, true)

    {:noreply, socket}
  end

  def handle_info({AppWeb.LiveFunctions, message}, socket) do
    {
      :noreply,
      socket
      |> put_flash(:info, message)
      |> assign(:live_action, :index)
      |> assign(:page_title, "")
    }
  end

  @impl true
  def handle_event("close_model", _value, socket), do: handle_close_model(socket)

  def handle_event("close_confirmation_model", _value, socket),
    do: handle_close_confirmation_model(socket)

  def handle_event("attach", value, socket), do: handle_attach(socket, value)
  def handle_event("process_attach", _value, socket), do: attach_application(socket)
  def handle_event("remove_file", value, socket), do: remove_file(socket, value)
  def handle_event("search", value, socket), do: search(value, socket)

  def handle_event("skip", _value, %{assigns: assigns} = socket),
    do: handle_skip(socket, assigns.record)

  def handle_event("validate_doc", value, socket), do: Entry.validate_doc(value, socket)
  def handle_event("doc_save", _value, socket), do: process_auto_uploads(socket)
  def handle_event("submit_correction", value, socket), do: submit_correction(value, socket)
  def handle_event("back", _value, socket), do: handle_back(socket)
  def handle_event(_target, _value, socket), do: {:noreply, socket}

  defp handle_close_model(socket) do
    {:noreply, assign(socket, :live_action, :index)}
  end

  defp handle_close_confirmation_model(socket) do
    {:noreply, assign(socket, :live_action, :index)}
  end

  defp handle_attach(socket, value) do
    {
      :noreply,
      assign(socket, :live_action, :confirm)
      |> assign(
        :confirmation_model_text,
        "Are you sure you want to attach this representative?"
      )
      |> assign(:confirmation_model_params, Map.merge(value, %{"action" => "cancel"}))
      |> assign(:confirmation_model_agree, "process_attach")
    }
  end

  defp handle_skip(socket, record) do
    {:noreply, Entry.next_position(socket, record)}
  end

  defp process_auto_uploads(socket) do
    # Process any completed uploads
    uploaded_files =
      socket.assigns.upload_fields
      |> Enum.reduce(%{}, fn upload_field, acc ->
        if length(socket.assigns.uploads[upload_field].entries) > 0 do
          files = consume_upload(socket, upload_field)
          Map.put(acc, upload_field, List.first(files))
        else
          acc
        end
      end)

    # Update the record with uploaded files
    if map_size(uploaded_files) > 0 do
      case Licenses.update_data_key(socket, socket.assigns.record.data) do
        {:ok, record} ->
          {:noreply,
           socket
           |> assign(:record, record)
           |> Entry.next_position(record)}

        {:error, error} ->
          {:noreply,
           LiveFunctions.sweet_alert(
             socket,
             "Error saving uploaded files: #{inspect(error)}",
             "error"
           )}
      end
    else
      # No new uploads, just proceed
      case socket.assigns.record do
        nil ->
          {:noreply,
           LiveFunctions.sweet_alert(
             socket,
             "No record found",
             "error"
           )}

        record ->
          {:noreply,
           socket
           |> Entry.next_position(record)}
      end
    end
  end

  # defp handle_doc_save(socket, value) do
  #   result = Entry.document_uploads(value["license_mapping"] || %{}, socket)

  #   case Licenses.create_registration(
  #       socket,
  #       socket.assigns.record,
  #       Map.merge(
  #         updated_attrs,
  #         socket.assigns.all_form_data
  #         |> Jason.encode!()
  #         |> Jason.decode!()
  #       )
  #     ) do
  #     {:ok, %{"license_registration" => record}} ->
  #       {:noreply, Entry.next_position(socket, record)}

  #     {:error, error} ->
  #       {:noreply,
  #        LiveFunctions.sweet_alert(
  #          socket,
  #          "Error processing registration: #{inspect(error)}",
  #          "error"
  #        )}
  #   end
  # end

  defp handle_back(socket) do
    {:noreply, Entry.previous_position(socket)}
  end

  def submit_correction(attrs, socket) do
    case Licenses.update_license_correction_pop(socket, socket.assigns.record.id, attrs) do
      {:ok, _record} ->
        {
          :noreply,
          push_navigate(
            LiveFunctions.sweet_alert(socket, "Application updated Successfully", "success"),
            to: ~p"/dashboard"
          )
          |> assign(:doc_details, %{})
        }

      {:error, message} ->
        LiveFunctions.sweet_alert(socket, message, "error")

        {
          :noreply,
          assign(socket, data_loader: false)
        }
    end
  end

  def consume_upload(socket, upload_type) do
    new_path = LiveFunctions.static_path("/uploads/licence_registration")

    consume_uploaded_entries(socket, upload_type, fn %{path: path}, entry ->
      file_name = "/#{Path.rootname(entry.client_name)}.#{LiveFunctions.ext(entry)}"
      dest = Path.join(new_path, file_name)

      if File.exists?(new_path <> "/") do
        File.cp(path, dest)
        {:ok, dest}
      else
        File.mkdir_p(new_path <> "/")
        File.cp_r(path, dest)
        {:ok, dest}
      end
    end)
  end

  defp search(%{"nrc" => nrc}, socket) do
    if String.trim(nrc) != "" do
      send(self(), {:search, String.trim(nrc)})

      socket =
        socket
        |> assign(:searching, true)
        |> assign(:searched, false)
        |> assign(:search_query, String.trim(nrc))
        |> assign(:search_results, [])

      {:noreply, socket}
    else
      {:noreply, assign(socket, :search_results, [])}
    end
  end

  def attach_application(socket) do
    new_params = %{
      "main_id" => Companies.get_main_application_id!(socket.assigns.current_user.id),
      "company_id" => Companies.get_company_id_by_user!(socket.assigns.current_user.id)
    }

    Companies.attach_application(
      socket,
      new_params,
      List.first(socket.assigns.search_results)
    )
    |> case do
      {:ok, _message} ->
        success_message(socket, "Successfully Attached.")

      {:error, %Ecto.Changeset{} = _changeset} ->
        error_message(
          socket,
          "Failed to attach application. Please check the details and try again."
        )

      {:error, message} ->
        error_message(socket, message)
    end
  end

  defp remove_file(socket, attrs) do
    Licenses.update_data_key(
      socket,
      Map.drop(socket.assigns.record.data || %{}, [attrs["field"]])
    )
    |> case do
      {:ok, record} ->
        send(self(), {:get_data, socket.assigns.params})

        {
          :noreply,
          socket
          |> assign(:record, record)
        }

      {:error, error} ->
        LiveFunctions.sweet_alert(
          socket,
          "Error removing file: #{inspect(error)}",
          "error"
        )

        {:noreply, socket}
    end
  end

  defp get_update(socket, params) do
    Entry.init(socket, params, "payments")
  end

  defp success_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_data, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(:search_form, to_form(%{"nrc" => nil}))
      |> assign(:search_results, [])
      |> assign(:searching, false)
      |> assign(:searched, false)
      |> assign(:search_query, "")
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "success"
      )
    }
  end

  defp error_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_data, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "error"
      )
    }
  end

  def error_to_string(:too_large), do: "Too large"
  def error_to_string(:not_accepted), do: "You have selected an unacceptable file type"
  def error_to_string(:too_many_files), do: "You have selected too many files"
end

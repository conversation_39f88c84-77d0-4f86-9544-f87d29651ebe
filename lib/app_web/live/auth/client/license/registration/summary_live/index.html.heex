<%= if @data_loader == false do %>
  <.live_component
    module={AppWeb.Registration.NavigationComponent}
    id="navigation"
    steps={@steps}
    current_position={@current_position}
  />
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 bg-white rounded-lg shadow-md mt-8">
    <div class="mb-8 border-b pb-5">
      <h1 class="text-2xl font-bold text-gray-900"><%= @chosen_licence.name %> Registration</h1>
      
      <p class="mt-2 text-sm text-gray-600">
        Please review your information and documents before submission.
      </p>
    </div>
     <%!-- Summary of information --%>
    <div class="bg-gray-50 p-6 rounded-lg border border-gray-200 mb-8">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Summary of Information</h2>
      
      <div class="flex flex-col space-y-4 max-h-96 overflow-y-auto pr-2">
        <% applicant_name_value =
          Enum.find_value(@license_fields, fn field ->
            if String.starts_with?(field.field_label || "", "Applicant Name") do
              Phoenix.HTML.Form.input_value(@form, String.to_atom(field.field_name))
            else
              nil
            end
          end) %>
        <%= for field <- Enum.sort_by(Enum.filter(@license_fields, &(&1.field_type != "upload")), fn field ->
          label = field.field_label || ""
          if String.starts_with?(label, "Applicant Name") do
            0
          else
            1
          end
        end) do %>
          <%= if App.Licenses.DynamicSchema.should_show_field?(field, @form.source.params || %{}) do %>
            <div class="w-full bg-white rounded-lg shadow-sm border border-gray-200 px-3 py-2 hover:shadow-md transition">
              <div class="text-xs font-semibold text-gray-500 mb-1 tracking-wide uppercase">
                <%= field.field_label %>
              </div>
              
              <div class="text-base font-normal text-gray-900">
                <%= case field.field_type  do %>
                  <% "checkbox" -> %>
                    <%= if Phoenix.HTML.Form.input_value(
                             @form,
                             String.to_atom(field.field_name)
                           ),
                           do: "Yes",
                           else: "No" %>
                  <% "checkbox_group" -> %>
                    <%= values =
                      Phoenix.HTML.Form.input_value(@form, String.to_atom(field.field_name)) ||
                        []

                    Enum.join(values, ", ") %>
                  <% _ -> %>
                    <%= Phoenix.HTML.Form.input_value(@form, String.to_atom(field.field_name)) ||
                        "Not provided" %>
                    <%= if String.starts_with?(field.field_label || "", "National Registration Card ID") && applicant_name_value do %>
                      (<%= applicant_name_value %>)
                    <% end %>
                <% end %>
              </div>
            </div>
          <% end %>
        <% end %>
      </div>
      
      <h3 class="text-md font-medium text-gray-900 mt-6 mb-2">Uploaded Documents</h3>
      
      <div class="flex flex-col space-y-4 max-h-96 overflow-y-auto pr-2">
        <%= for field <- Enum.filter(@license_fields, &(&1.field_type == "upload")) do %>
          <%= if is_nil(field.field_dependents) ||
          field.dependent_selection ==
            (if !Enum.empty?(field.field_dependents),
              do: @form.source.changes[String.to_atom(List.first(field.field_dependents))]) do %>
            <div class="w-full bg-white rounded-lg shadow-sm border border-gray-200 px-3 py-2 hover:shadow-md transition">
              <div class="text-xs font-semibold text-gray-500 mb-1 tracking-wide uppercase">
                <%= field.field_label %>
              </div>
              
              <div class="text-base font-normal text-gray-900">
                <%= if @record.data &&
                  Map.has_key?(@record.data, field.field_name) &&
                  Map.get(@record.data, field.field_name) != nil do %>
                  <div class="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-green-500 mr-2"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    
                    <span class="truncate">
                      <%= String.slice(Path.basename(@record.data[field.field_name]), 0, 40) <>
                        if String.length(Path.basename(@record.data[field.field_name])) > 20,
                          do: "...",
                          else: "" %>
                    </span>
                  </div>
                <% else %>
                  <span class="text-orange-500">Not uploaded</span>
                <% end %>
              </div>
            </div>
          <% end %>
        <% end %>
      </div>
      
      <%= if @associates != [] do %>
        <h3 class="text-md font-medium text-gray-900 mt-6 mb-2">
          Representatives: (<%= NumberF.comma_separated(length(@associates), 0) %>)
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <%= for representative <- @associates  do %>
            <div class="py-2 border-b border-gray-100">
           
              
              <div class="text-md text-gray-900">
                <%= representative.data["national_id"] %> (<%= representative.user.first_name %> <%= representative.user.last_name %>)
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
      
      <div class="mt-6 text-md text-gray-500">
        Please review the information above before submitting.
      </div>
      
      <div class="flex justify-end pt-6 border-t">
        <.button type="button" phx-click="back">
          <b><i class="hero-chevron-double-left position-left"></i></b> back
        </.button>
        
        <.button
          type="button"
          phx-disable-with="Submitting..."
          phx-click="submit"
          class="ml-3 inline-flex items-center px-6 py-2 text-sm font-medium text-white bg-brand-10 border border-transparent rounded-md shadow-sm hover:bg-brand-11 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-2"
        >
          <.icon name="hero-check-circle" class="h-5 w-5 mr-2" /> Submit
        </.button>
      </div>
    </div>
  </div>
<% end %>

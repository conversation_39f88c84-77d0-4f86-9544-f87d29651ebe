defmodule AppWeb.Dashboard.ClientLive.Index do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})
  alias App.Licenses

  @impl true
  def mount(params, _session, socket) do
    if connected?(socket) do
      {
        :ok,
        not_registered(
          socket,
          "dashboard-license_registration" in socket.assigns.permissions,
          params["name"]
        )
        |> assign(
          page_title: params["name"],
          category: params["name"],
          animate: true,
          animate_cards: true
        )
      }
    else
      {
        :ok,
        not_registered(socket, false, "")
        |> assign(
          page_title: params["name"],
          category: params["name"],
          animate: false,
          animate_cards: false,
          registration_status: false
        )
      }
    end
  end

  defp not_registered(socket, true, name) do
    registration_status = Licenses.check_licenses(socket.assigns.current_user.id, name)
    assign(socket, registration_status: registration_status)
  end

  defp not_registered(socket, false, _name), do: assign(socket, registration_status: false)
end

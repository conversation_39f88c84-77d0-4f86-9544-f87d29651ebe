defmodule AppWeb.Auth.NotificationLive.Index do
  use AppWeb, :live_view

  alias App.Service.Table.Notifications, as: TableQuery

  alias App.Utilities

  @impl true
  def mount(params, _session, socket) do
    {
      :ok,
      assign(socket, :user_notifications, [])
      |> assign(params: params)
    }
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply,
     socket
     |> assign(:params, params)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "Notifications")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, %{assigns: assigns} = socket) do
    case target do
      "read_notification" ->
        read_notification(value, socket)

      "read_all" ->
        Utilities.read_all_notifications(
          assigns.user_notifications.entries,
          Map.put(value, "status", true),
          socket
        )
        |> case do
          {:ok, _message} ->
            success(socket, "Notifications Read.")

          {:error, message} ->
            error(socket, value, message)
        end

      _ ->
        {:noreply, socket}
    end
  end

  def read_notification(params, socket) do
    Utilities.get_notification!(params["id"])
    |> Utilities.update_notification(Map.put(params, "status", true), socket)

    {:noreply, push_navigate(socket, to: params["url"])}
  end

  defp list(%{assigns: assigns} = socket, params) do
    {:noreply,
     assign(socket, :user_notifications, TableQuery.index(params, assigns))
     |> assign(params: params)}
  end

  defp success(socket, message) do
    send(self(), {:get_list, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> LiveFunctions.sweet_alert(message, "success")
    }
  end

  defp error(socket, params, message) do
    send(self(), {:get_list, params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> LiveFunctions.sweet_alert(
        message,
        "error"
      )
    }
  end
end

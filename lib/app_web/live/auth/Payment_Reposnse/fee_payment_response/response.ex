defmodule AppWeb.Auth.Authentication.Payment.BundlePurchaseResponseLive.Response do
  use AppWeb, :live_view

  # alias App.{Transactions}

  alias App.Jobs.CheckTransactionStatus

  @impl true
  def mount(params, session, socket) do
    CheckTransactionStatus.subscribe()

    {:ok,
     socket
     |> assign(transaction: [])
     #  |> assign(data_loader: true)
     |> assign(:live_action, :confirm)
     |> assign(:params, params)
     |> assign(confirmation_model_title: "")
     |> assign(:confirmation_model_icon, "loading")
     |> assign(
       :confirmation_model_text,
       "Your transaction is currently being processed."
     )
     |> assign(confirmation_model: true)
     |> assign(confirmation_model_agree: "")
     |> assign(confirmation_model_reject: "close_confirmation_model")
     |> assign(confirmation_model_params: "")
     |> assign(live_socket_id: session["live_socket_id"])}
  end

  @impl true
  def handle_info({CheckTransactionStatus, :complete_transaction, data}, socket) do
    socket =
      socket
      |> assign(:live_action, :index)
      |> assign(transaction: data.transaction)
      # |> assign(data_loader: false)
      |> assign(confirmation_model_title: "Are you sure?")
      |> assign(confirmation_model_text: "")
      |> assign(confirmation_model_agree: "")
      |> assign(confirmation_model_params: "")
      |> assign(confirmation_model_icon: "exclamation_circle")

    {:noreply, socket}
  end
end

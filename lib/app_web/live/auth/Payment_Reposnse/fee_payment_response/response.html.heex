<div class="mx-auto max-w-7xl  sm:px-9 lg:px-8 mt-5">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto"></div>
  </div>
</div>

<%= if @transaction != [] do %>
  <%= if @transaction.status not in ["P", "PENDING"] do %>
    <%= if @transaction.status in ["COMPLETE", "S", "SUCCESS"] do %>
      <section class="flex justify-center items-center py-20 overflow-hidden relative print:py-0 ">
        <div class="container max-w-A4 max-h-A4 flex justify-center">
          <div class="shadow-2xl print:shadow-none">
            <iframe
              src={~p"/invoice/bundle/#{@transaction.reference}"}
              id="receipt_iframe"
              title="bundle"
              width="1200"
              height="700"
            >
            </iframe>
          </div>
        </div>
      </section>
    <% else %>
      <div class="lg:px-24 lg:py-24 md:py-20 md:px-44 px-4 py-24 items-center flex justify-center lg:flex-row md:gap-28 gap-16">
        <div class="xl:pt-24 w-full xl:w-1/2 relative pb-12 lg:pb-0">
          <div class="relative">
            <div class="absolute">
              <div class="">
                <div class="flex items-center">
                  <div class="mr-20">
                    <h1 class="my-2 text-gray-800 font-bold text-2xl">
                      There has been an error with the transaction!
                    </h1>

                    <p class="my-2 text-gray-800">
                      Sorry about that! Kindly navigate back to the Homepage.
                    </p>

                    <div class="flex flex-wrap justify-between gap-6 mt-14">
                      <div>
                        <%!-- <p class="text-base font-normal">
                      Narration: <span class="font-medium"><%= @transaction.narration %></span>
                    </p> --%>
                        <p class="text-base font-normal">
                          Transaction reference:
                          <span class="font-medium"><%= @transaction.reference %></span>
                        </p>

                        <p class="text-base font-normal">
                          Transaction status:
                          <span class="font-medium"><%= @transaction.status %></span>
                        </p>
                      </div>
                    </div>

                    <.link navigate="/">
                      <button
                        class="sm:w-full rounded-xl lg:w-auto my-2 border md py-4 px-8 text-center bg-rose-800 text-white hover:bg-rose-600 focus:outline-none focus:ring-2 focus:ring-rose-600 focus:ring-opacity-50"
                        navigate={~p"/dashboard"}
                      >
                        Home
                      </button>
                    </.link>
                  </div>
                  <img class="block h-24 w-auto" src="/images/payment_failed.png" alt="Failed" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  <% end %>
<% end %>

<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>

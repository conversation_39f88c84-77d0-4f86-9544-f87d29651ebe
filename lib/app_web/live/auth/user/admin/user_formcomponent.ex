defmodule AppWeb.UserLive.FormComponent do
  use AppWeb, :live_component
  alias App.{Accounts, Roles}

  alias App.Service.ServiceAdminMaintenance.Functions
  # Render form
  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.simple_form
        for={@form}
        id="city-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-2 grid-cols-2 gap-4">
          <.input
            field={@form[:email]}
            type="text"
            placeholder="Enter Email"
            required
            label={raw(~c"Email <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:first_name]}
            type="text"
            placeholder="Enter First Name"
            required
            label={raw(~c"First Name <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:last_name]}
            type="text"
            placeholder="Enter Last Name"
            required
            label={raw(~c"Last Name <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:sex]}
            type="select"
            prompt="--Select Sex--"
            options={["MALE", "FEMALE"]}
            required
            label={raw(~c"Sex <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:mobile]}
            type="mobile"
            phx-hook="validateNumberHook"
            label={raw(~c"Mobile Number <span class='text-rose-500'>*</span>")}
            placeholder="e.g. 971234567"
            required
          />
          <.input
            field={@form[:department_id]}
            type="select"
            prompt="--Select Department--"
            options={@role_id_data}
            placeholder="Select User Department"
            required
            label={raw(~c"User Department <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:role_id]}
            type="select"
            prompt="--Select Role--"
            options={@select_role}
            placeholder="Select User Role"
            required
            label={raw(~c"User Role <span class='text-rose-500'>*</span>")}
          />
        </div>

        <:actions>
          <div class="align-left">
            <.button type="button" phx-click="close_model">Close</.button>

            <%= if @form.source.valid? do %>
              <.button type="submit" phx-disable-with="submitting...">Submit</.button>
            <% end %>
          </div>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{action: action} = assigns, socket) do
    c_mount(action, assigns, socket)
  end

  defp c_mount(:edit, %{user: user} = assigns, socket) do
    {select_role, department_id} = Roles.select_department_role_access_role(user.role_id)

    changeset =
      Accounts.change_admin_user_profile(user, %{"department_id" => to_string(department_id)})

    socket
    |> assign(assigns)
    |> assign(:department_id, department_id)
    |> assign(:select_role, select_role)
    |> assign(:role_id_data, Roles.select_department_role())
    |> assign_form(changeset)
    |> ok()
  end

  defp c_mount(:new, %{user: user} = assigns, socket) do
    changeset = Accounts.change_admin_user_profile(user)

    socket
    |> assign(assigns)
    |> assign(:select_role, [])
    |> assign(:role_id_data, Roles.select_department_role())
    |> assign_form(changeset)
    |> ok()
  end

  @impl true
  def handle_event("validate", %{"user" => params} = attrs, socket) do
    changeset =
      socket.assigns.user
      |> Accounts.change_admin_user_profile(params)
      |> Map.put(:action, :validate)

    role =
      if attrs["_target"] == ["user", "department_id"] do
        Roles.select_access_role_from_department_by(params["department_id"])
      else
        socket.assigns.select_role
      end

    socket
    |> assign_form(changeset)
    |> assign(:select_role, role)
    |> noreply()
  end

  def handle_event("save", %{"user" => user_params}, socket) do
    save_user(socket, socket.assigns.action, user_params)
    |> noreply()
  end

  defp save_user(socket, :edit, user_params) do
    case Functions.update(socket, socket.assigns.user, user_params) do
      {:ok, user} ->
        notify_parent({:saved, user, "Admin User Updated Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}
    end
  end

  defp save_user(socket, :new, user_params) do
    case Functions.create(socket, user_params) do
      {:ok, user} ->
        notify_parent({:saved, user, "Admin User Created Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}
    end
  end

  defp notify_parent(msg) do
    send(self(), {__MODULE__, msg})
  end
end

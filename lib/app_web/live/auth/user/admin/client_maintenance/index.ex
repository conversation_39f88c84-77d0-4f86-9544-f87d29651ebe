defmodule AppWeb.Auth.ClientManagementLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias App.Service.Table.ClientMaintenance, as: TableUserQuery

  alias App.{
    Accounts.User
  }

  alias Logs.Audit

  alias AppWeb.LiveFunctions
  alias App.Service.ServiceAdminMaintenance.Functions

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("client_user_view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Client User Maintenance", "Accessed Client User Maintenance Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      filter_data = %{
        "email" => nil,
        "mobile" => nil,
        "status" => nil,
        "start_date" => nil,
        "end_date" => nil
      }

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()
        |> assign(params: params)
        |> assign(reset_user_form: "hide")
        |> assign(enable_user_form: "hide")
        |> assign(disable_user_form: "hide")
        |> assign(confirmation_model: false)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(live_socket_id: session["live_socket_id"])
        |> assign(showFilter: false)
        |> assign(form: useform(filter_data))

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Client User Maintenance", "Accessed Client User Maintenance Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  defp useform(data) do
    to_form(
      data,
      as: "filter"
    )
  end

  @impl true
  def handle_info({AppWeb.UserLive.FormComponent, {:saved, _user_index, msg}}, socket) do
    send(self(), {:get_list, %{}})

    {:noreply,
     socket
     |> assign(:live_action, :index)
     |> AppWeb.LiveFunctions.sweet_alert(msg, "success")
     |> assign(:page_title, "")}
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {:change_record_status, params} ->
        record_status(socket, params)

      {:change_password, params} ->
        password_reset(socket, params)

      {:search_records, params} ->
        send(self(), {:get_list, %{"filter" => params}})
        noreply(socket)

      {AppWeb.Component.DateFilter, {:filtered, data}} ->
        send(self(), {:get_list, %{"filter" => data}})

        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }

      {AppWeb.LiveFunctions, message} ->
        send(self(), {:get_list, %{}})

        {
          :noreply,
          socket
          |> put_flash(:info, message)
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, %{assigns: assigns} = socket) do
    case target do
      "update_status" ->
        new_status = if value["status"] == "D", do: "disable", else: "enable"

        model_call = if value["status"] == "A", do: :confirm, else: :confirm_with_des

        {
          :noreply,
          assign(socket, :live_action, model_call)
          |> assign(
            :confirmation_model_text,
            "Are you sure you want to #{new_status} this User?"
          )
          |> assign(:confirmation_model_params, Map.merge(value, %{"action" => new_status}))
          |> assign(:confirmation_model_agree, "change_record_status")
        }

      "export" ->
        LiveFunctions.export_records(socket, value, "client_service", assigns.form.params())

      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "add_user" ->
        socket =
          socket
          |> assign(:page_title, "New User")
          |> assign(:user, %User{})
          |> assign(:live_action, :new)

        {:noreply, socket}

      "edit" ->
        socket =
          socket
          |> assign(:live_action, :edit)
          |> apply_action(:edit, value)

        {:noreply, socket}

      "filter_change" ->
        assign(socket, form: useform(value["filter"]))
        |> noreply()

      "filter" ->
        value = if socket.assigns.showFilter == true, do: false, else: true

        assign(socket, :showFilter, value)
        |> noreply()

      "reset_filter" ->
        socket
        |> assign(form: useform(%{}))
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        noreply(
          socket
          |> assign(checked_ids: [])
          |> assign(param_list: [])
        )

      "close_model" ->
        socket =
          socket
          |> assign(:new_edit_modal, false)
          |> assign(:view_modal, false)
          |> assign(:live_action, :index)
          |> apply_action(:index, value)

        {:noreply, socket}

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :index)
        }

      "change_record_status" ->
        LiveFunctions.change_record(value, socket, :change_record_status)

      "reset_password" ->
        {:noreply,
         assign(socket, :live_action, :confirm)
         |> assign(
           :confirmation_model_text,
           "Are you sure you want to reset the User password?"
         )
         |> assign(:confirmation_model_params, Map.merge(value, %{"action" => "reset"}))
         |> assign(:confirmation_model_agree, "confirm_password")}

      "confirm_password" ->
        LiveFunctions.change_record(value, socket, :change_password)

      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

      _ ->
        {:noreply, socket}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "User Management")
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "Create Admin User")
    |> assign(:user, %User{})
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Update Admin User")
    |> assign(:user, App.Accounts.get_admin!(id))
  end

  defp record_status(socket, params) do
    App.Service.ServiceClientMaintenance.Functions.index(socket, params, "change_status_client")
    |> case do
      {:ok, _message} ->
        success(socket, "Client User status changed successfully.")

      {:error, message} ->
        error(socket, params, message)
    end
  end

  defp password_reset(socket, params) do
    Functions.index(socket, params, "reset_user_password")
    |> case do
      {:ok, _message} ->
        success(socket, "User password successfully reset.")

      {:error, message} ->
        error(socket, params, message)
    end
  end

  defp success(socket, message) do
    send(self(), {:get_list, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(message, "success")
    }
  end

  defp error(socket, params, message) do
    send(self(), {:get_list, params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "error"
      )
    }
  end

  defp list(socket, params) do
    {
      :noreply,
      assign(
        socket,
        :data,
        TableUserQuery.index(LivePageControl.create_table_params(socket, params))
      )
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end
end

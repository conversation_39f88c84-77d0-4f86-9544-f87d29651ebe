defmodule AppWeb.Authenticated.SettingsLive.ConfigLive.Index do
  use AppWeb, :live_view

  on_mount({AppWeb.UserAuth, :mount_current_user})

  alias App.Settings

  @impl true
  def render(assigns) do
    ~H"""
    <h2 class="sm:text-2xl font-bold text-xl pt-4 md:pt-1"><%= @page_title %></h2>

    <div class="grid max-w-2xls mx-auto">
      <div class="items-center text-[#202142]">
        <div class="flow-root">
          <.simple_form
            for={@form}
            phx-submit="settings_configuration_submit"
            id="settings-form"
            class="-mt-4"
          >
            <div>
              <%= if @config != [] do %>
                <div class="inline-block w-full border-b-2">
                  <div class="grid sm:grid-cols-2 md:grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-4 py-4">
                    <%= for setting <-@config do %>
                      <div class="rounded-lg bg-white px-4 py-4 w-full border space-y-4">
                        <h2 class="block text-md font-medium text-gray-800">
                          <%= Enum.map(String.split(setting.name, "_"), &String.capitalize/1)
                          |> Enum.join(" ") %>
                        </h2>

                        <div class="flex justify-between items-stretch">
                          <input
                            id={"basic-addon#{setting.id}"}
                            value={setting.value}
                            type={setting.field_type}
                            class="block w-full rounded-none rounded-l-lg border-0 py-1.5  text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-brand-10 sm:text-sm sm:leading-6"
                            name={"value[#{setting.id}]"}
                            placeholder={
                              Enum.map(String.split(setting.name, "_"), &String.capitalize/1)
                              |> Enum.join(" ")
                            }
                            aria-label={
                              Enum.map(String.split(setting.name, "_"), &String.capitalize/1)
                              |> Enum.join(" ")
                            }
                            aria-describedby={"basic-addon#{setting.id}"}
                          />
                          <h3 class="bg-gray-50 rounded-r-lg border-y border-r border-gray-300 font-medium text-brand-1 py-2 px-2">
                            <%= setting.value_type %>
                          </h3>
                        </div>

                        <div class="bg-opacity-30 py-2 rounded-sm border-l-5">
                          <h2 class=" text-sm text-gray-500">
                            <%= setting.description %>.
                          </h2>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% else %>
                <div class="text-center text-rose-500">No Configuration Settings</div>
              <% end %>
            </div>

            <:actions>
              <%= if @config != [] do %>
                <.button
                  phx-disable-with="Saving..."
                  class="!bg-brand-10 hover:!bg-brand-1 hover:text-white"
                >
                  Save
                </.button>
              <% end %>
            </:actions>
          </.simple_form>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def mount(_params, session, %{assigns: _assigns} = socket) do
    form =
      to_form(
        %{
          "value" => nil
        },
        as: "settings"
      )

    socket =
      assign(socket, data: [])
      |> assign(page_title: "Configuration Settings")
      |> assign(browser_info: session["browser_info"])
      |> assign(maker_checker: false)
      |> assign(form: form)
      |> assign(config: Settings.list_settings_config())
      |> assign(live_socket_id: session["live_socket_id"])

    {:ok, socket}
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, socket) do
    case target do
      "settings_configuration_submit" ->
        settings_configuration_submit(socket, value)

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :index)
        }
    end
  end

  defp settings_configuration_submit(socket, value) do
    Enum.map(value["value"], fn {k, v} ->
      %{id: k, value: v}
    end)
    |> Settings.update_config_settings_value(socket)
    |> case do
      {:ok, _} ->
        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> LiveFunctions.sweet_alert(
            "Successfully updated configuration settings.",
            "success"
          )
        }

      {:error, _error} ->
        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> LiveFunctions.sweet_alert(
            "Failed to update configuration settings.",
            "error"
          )
        }
    end

    {:noreply, socket}
  end
end

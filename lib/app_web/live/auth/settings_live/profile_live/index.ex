defmodule AppWeb.Authenticated.SettingsLive.ProfileLive.Index do
  use AppWeb, :live_view

  alias App.Accounts

  on_mount({AppWeb.UserAuth, :mount_current_user})

  @impl true
  def render(assigns) do
    ~H"""
    <h2 class="sm:text-2xl font-bold text-xl pt-4 md:pt-1"><%= @page_title %></h2>

    <div class="grid max-w-2xl slide-in-from-top">
      <div class="items-center text-[#202142]">
        <.simple_form
          for={@form}
          id="profile-form"
          phx-change="validate"
          phx-submit="save_details"
          class="-mt-4"
        >
          <div class="mb-2 sm:mb-6 space-y-6">
            <.input
              field={@form[:current_password]}
              type="password"
              placeholder="Enter current password"
              label="Current Password"
              required
            />
            <.input
              field={@form[:password]}
              type="password"
              placeholder="Enter new password"
              label="New Password"
              required
            />
            <.input
              field={@form[:password_confirmation]}
              type="password"
              placeholder="Confirm new password"
              label="Confirm Password"
              required
            />
          </div>

          <:actions>
            <.button
              phx-disable-with="Saving..."
              class="!bg-brand-10 hover:!bg-brand-1 hover:text-white"
            >
              Update Password
            </.button>
          </:actions>
        </.simple_form>
      </div>
    </div>

    <Model.confirmation_model
      :if={@live_action == :confirm}
      show
      id="confirmation_model-modal"
      model_title={@confirmation_model_title}
      body_text={@confirmation_model_text}
      agree_function={@confirmation_model_agree}
      reject_function={@confirmation_model_reject}
      icon={@confirmation_model_icon}
    />
    """
  end

  @impl true
  def mount(_params, session, %{assigns: _assigns} = socket) do
    form =
      to_form(
        %{
          "current_password" => nil,
          "password" => nil,
          "password_confirmation" => nil
        },
        as: "user"
      )

    socket =
      assign(socket, data: [])
      |> assign(page_title: "Profile Settings")
      |> assign(form: form)
      |> assign(live_socket_id: session["live_socket_id"])
      |> assign(confirmation_model_title: "Are you sure?")
      |> assign(confirmation_model_text: "")
      |> assign(confirmation_model_agree: "")
      |> assign(confirmation_model_reject: "close_confirmation_model")
      |> assign(confirmation_model_params: "")
      |> assign(confirmation_model_icon: "exclamation_circle")

    {:ok, socket}
  end

  @impl true
  def handle_event("validate", %{"user" => params}, socket) do
    changeset =
      socket.assigns.current_user
      |> Accounts.change_user_password(params)
      |> Map.put(:action, :validate)

    {:noreply, assign_form(socket, changeset)}
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, socket) do
    case target do
      "save_details" ->
        cond do
          value["user"]["current_password"] == value["user"]["password"] ->
            {:noreply,
             LiveFunctions.sweet_alert(
               socket,
               "Current password and new password are the same!",
               "error"
             )}

          true ->
            {:noreply,
             assign(socket, :live_action, :confirm)
             |> assign(
               :confirmation_model_text,
               "Are you sure you want to update your password?"
             )
             |> assign(:confirmation_model_params, Map.merge(value, %{"action" => "update"}))
             |> assign(:confirmation_model_agree, "save")}
        end

      "save" ->
        Accounts.update_user_password(
          socket.assigns.current_user,
          socket.assigns.confirmation_model_params["user"]["current_password"],
          socket.assigns.confirmation_model_params["user"]
        )
        |> case do
          {:ok, _user} ->
            {
              :noreply,
              socket
              |> assign(:live_action, :index)
              |> LiveFunctions.sweet_alert(
                "Password successfully updated.",
                "success"
              )
            }

          {:error, changeset} ->
            errors = Enum.join(Enum.map(changeset.errors, &format_error/1), ", ")

            {
              :noreply,
              socket
              |> assign(:live_action, :index)
              |> LiveFunctions.sweet_alert(
                "Failed to change password. #{errors}",
                "error"
              )
            }
        end

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :index)
        }
    end
  end

  defp format_error({field, {message, _}}) do
    field_str = Atom.to_string(field)

    "#{String.capitalize(String.replace_suffix(String.replace(field_str, "_", " "), "_id", ""))} #{message}"
  end
end

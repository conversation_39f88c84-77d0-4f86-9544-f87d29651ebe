<div class="px-4 sm:px-6 lg:px-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h2 class="my-4 text-2xl font-semibold w-full">Settings</h2>
    </div>
  </div>

  <div class="bg-white slide-in-from-top sticky top-24 mt-4 w-full rounded-3xl flex flex-col max-h-screen overflow-y-scroll overflow-x-hidden hidden_scrollbar_container md:flex-row text-[#161931]">
    <aside class="md:py-8 md:w-1/3 lg:w-1/4 sticky top-0 border-b md:border-b-0 bg-white">
      <div class="flex justify-between md:justify-start w-full md:flex-col divide-solid flex-no-wrap md:gap-2 md:h-full text-sm md:border-r border-gray-300 md:px-6 ">
        <a
          href="#"
          class={"flex flex-1 md:flex-initial items-center text-center border-0 border-l md:border  justify-center px-3 py-6 md:py-2.5 font-bold transition-all ease-in-out duration-300 #{if(@current_tab == "user_profile", do: 'text-white hover:bg-brand-1 bg-brand-10 md:border-brand-10', else: 'hover:text-black text-gray-500 ')} md:rounded-full"}
          phx-click="change_tab"
          phx-value-tab="user_profile"
          phx-class:active="{ 'font-bold': @current_tab == 'user_profile' }"
        >
          User Profile
        </a>

        <%= if Enum.member?(@permissions, "configuration-view") do %>
          <a
            href="#"
            class={"flex flex-1 md:flex-initial items-center text-center md:border border-0 border-l justify-center px-3 py-6 md:py-2.5 font-bold transition-all ease-in-out duration-300 #{if(@current_tab == "config_settings", do: 'text-white hover:bg-brand-1 bg-brand-10 md:border-brand-10', else: 'hover:text-black text-gray-500 ')} md:rounded-full"}
            phx-click="change_tab"
            phx-value-tab="config_settings"
            phx-class:active="{ 'font-bold': @current_tab == 'config_settings' }"
          >
            Configuration Settings
          </a>
        <% end %>

        <%= if Enum.member?(@permissions, "function-view") do %>
          <a
            href="#"
            class={"flex flex-1 md:flex-initial items-center text-center md:border border-0 border-l justify-center px-3 py-6 md:py-2.5 font-bold transition-all ease-in-out duration-300 #{if(@current_tab == "settings", do: 'text-white hover:bg-brand-1 bg-brand-10 md:border-brand-10', else: 'hover:text-black text-gray-500 ')} md:rounded-full"}
            phx-click="change_tab"
            phx-value-tab="settings"
            phx-class:active="{ 'font-bold': @current_tab == 'settings' }"
          >
            Function Settings
          </a>
        <% end %>
      </div>
    </aside>

    <main class="w-full md:w-2/3 lg:w-3/4">
      <div class="w-full px-6 pt-4 md:pt-8 py-8">
        <%= if @current_tab == "user_profile" do %>
          <%= live_render(@socket, AppWeb.Authenticated.SettingsLive.ProfileLive.Index,
            sticky: true,
            id: "ProfileLive"
          ) %>
        <% end %>

        <%= if @current_tab == "config_settings" do %>
          <%= live_render(@socket, AppWeb.Authenticated.SettingsLive.ConfigLive.Index,
            sticky: true,
            id: "ConfigLive"
          ) %>
        <% end %>

        <%= if @current_tab == "settings" do %>
          <%= live_render(@socket, AppWeb.Authenticated.SettingsLive.FunctionLive.Index,
            sticky: true,
            id: "FunctionLive"
          ) %>
        <% end %>
      </div>
    </main>
  </div>
</div>

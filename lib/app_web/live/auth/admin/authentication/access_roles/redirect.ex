defmodule AppWeb.Authenticated.Auth.AccessRoleLive.Redirect do
  @moduledoc false
  use AppWeb, :live_view

  alias App.Roles

  @impl true
  def mount(params, _session, socket) do
    data = Roles.get_access_roles_by_id_preload(params["role"])

    {
      :ok,
      # push_redirect(socSxket, to: Routes.access_role_index_path(socket, :index, data.department.id))
      push_navigate(socket, to: "/admin/settings/roles/access/#{data.department.id}")
    }
  end
end

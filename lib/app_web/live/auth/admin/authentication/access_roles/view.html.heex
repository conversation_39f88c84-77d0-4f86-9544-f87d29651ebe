<div class="px-4 sm:px-6 lg:px-8">
  <div class="sm:flex sm:items-center mt-8">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">
        <%= @role_data.name %> access role management
      </h1>
    </div>

    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <.header>
        <div class="flex items-center my-4">
          <%= if Enum.member?(@permissions, "access_roles-view") do %>
            <.link
              class="text-base font-semibold cursor-pointer py-2 px-2 border-b-8 text-brand-1 hover:border-brand-1"
              navigate={~p"/roles/access_roles/#{@role_data.id}/view"}
            >
              Role Users
            </.link>
          <% end %>

          <%= if Enum.member?(@permissions, "access_roles-add") do %>
            <.link
              class="text-base font-semibold cursor-pointer py-2 px-2 text-gray-900 border-b-8 hover:text-brand-1"
              navigate={~p"/roles/access_roles/#{@role_data.id}/add"}
            >
              Non-role Users
            </.link>
          <% end %>
        </div>
      </.header>
    </div>
  </div>

  <div class="mt-4 flow-root">
    <div class="-mx-4 -my-2 sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <Table.main_table id="adverts" rows={@data} params={@params} data_loader={@data_loader}>
          <:col :let={james} label={table_link(@params, "Email", :email)}>
            <%= james.email %>
          </:col>

          <:col :let={james} label={table_link(@params, "First Name", :first_name)}>
            <%= james.first_name %>
          </:col>

          <:col :let={james} label={table_link(@params, "Last Name", :first_name)}>
            <%= james.last_name %>
          </:col>

          <:col :let={james} label={table_link(@params, "Status", :status)}>
            <Table.table_string_status status={james.status} />
          </:col>

          <:col :let={james} label={table_link(@params, "Last Login Date", :last_login_date)}>
            <%= james.last_login_date %>
          </:col>
        </Table.main_table>
      </div>
    </div>
  </div>
</div>

defmodule AppWeb.Authenticated.Authentication.AccessRoleLive.Add do
  @moduledoc false
  use AppWeb, :live_view

  alias App.Service.Table.AddRoleUsers, as: TableQuery

  alias App.{Accounts, Roles}

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("access_roles-add_users", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {
            "Add User to Role Settings",
            "Accessed Add User to Role Settings Page",
            "/"
          },
          {true, assigns.current_user.id}
        )
      end)

      socket =
        assign(socket, data: [])
        |> assign(:data_loader, true)
        |> assign(:role, params["role"])
        |> assign(:role_data, Roles.get_access_roles!(params["role"]))
        |> assign(error_message: "")
        |> assign(confirmation_model: false)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(info_modal_param: "")
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {
            "Add User to Role Maintenance",
            "Access Add User to Role Maintenance Page Denied due to permissions access",
            "/"
          },
          {false, assigns.current_user.id}
        )
      end)

      {:ok, push_navigate(socket, to: ~p"/dashboard", replace: true)}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    send(self(), {:get_list, params})
    socket = assign(socket, :params, params)

    {
      :noreply,
      LivePageControl.order_by_composer(socket, params)
    }
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {:add_user_to_role, params} ->
        add_user_to_role(socket, params)
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, %{assigns: assigns} = socket) do
    case target do
      "add_user_to_role" ->
        params = Jason.decode!(value["params"])
        send(self(), {:add_user_to_role, params})
        {:noreply, assign(socket, :confirmation_model_icon, "loading")}

      "add_user" ->
        user =
          Enum.find(socket.assigns.data.entries, fn %{:id => id} ->
            id == String.to_integer(value["id"])
          end)

        {
          :noreply,
          assign(socket, :live_action, :confirm)
          |> assign(
            :confirmation_model_text,
            "Are you sure you want to add #{user.username} to #{assigns.role_data.name} Role?"
          )
          |> assign(
            :confirmation_model_params,
            Map.merge(value, %{"role_id" => assigns.role_data.id})
          )
          |> assign(:confirmation_model_agree, "add_user_to_role")
        }

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :index)
        }

      "export" ->
        LiveFunctions.export_records(socket, value, "non_role_users", socket.assigns.params)

      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      _ ->
        {:noreply, socket}
    end
  end

  defp list(socket, params) do
    params = Map.put(params, "role_id", socket.assigns.role)

    {
      :noreply,
      assign(socket, :data, TableQuery.index(LivePageControl.create_table_params(socket, params)))
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end

  defp add_user_to_role(socket, params) do
    Accounts.add_user_to_role(socket, params, socket.assigns.current_user.id)
    |> case do
      {:ok, %{update: user}} ->
        if connected?(socket), do: send(self(), {:get_list, params})

        {
          :noreply,
          assign(socket, :live_action, :index)
          |> assign(confirmation_model_icon: "exclamation_circle")
          |> LiveFunctions.sweet_alert(
            "#{user.username} added to role successfully.",
            "success"
          )
        }

      {:error, message} ->
        {
          :noreply,
          LiveFunctions.sweet_alert(socket, message, "error")
          |> assign(:confirmation_model, false)
          |> assign(confirmation_model_icon: "exclamation_circle")
        }
    end
  end
end

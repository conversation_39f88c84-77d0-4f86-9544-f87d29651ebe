defmodule AppWeb.Settings.AccessRoleLive.FormComponent do
  @moduledoc false
  use AppWeb, :live_component

  alias App.Roles
  alias App.{Service.Functions.AccessRoleLive}

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
      </.header>

      <.simple_form for={@form} id="document-form" phx-target={@myself} phx-submit="save">
        <.input field={@form[:name]} type="text" placeholder="Role name" label="Name" />
        <.input
          field={@form[:description]}
          type="textarea"
          placeholder="Role description"
          label="Description"
        /> <.input field={@form[:remarks]} type="textarea" placeholder="Remarks" label="Remarks" />
        <:actions>
          <.button phx-disable-with="Saving...">Save</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{struct: struct} = assigns, socket) do
    changeset = Roles.change_access_roles(struct)

    {
      :ok,
      socket
      |> assign(assigns)
      # |> assign(error: nil)
      |> assign_form(changeset, :form)
    }
  end

  @impl true
  def handle_event(target, params, socket), do: handle_event_switch(target, params, socket)

  def handle_event_switch(target, params, socket) do
    case target do
      "save" -> save_user(socket, socket.assigns.action, params)
      "validate" -> validate(params, socket)
    end
  end

  def validate(params, socket) do
    changeset =
      socket.assigns.struct
      |> Roles.change_access_roles(
        Map.merge(
          params["access_roles"],
          %{"system_role_id" => socket.assigns.role}
        )
      )

    {
      :noreply,
      assign_form(socket, changeset, :form)
      #  |> assign(error: nil)
    }
  end

  defp save_user(%{assigns: assigns} = socket, :edit, params) do
    AccessRoleLive.update_access_roles(
      socket,
      socket.assigns.struct,
      Map.put(params["access_roles"], "updated_by", assigns.current_user.id)
    )
    |> case do
      {:ok, message, _maker_checker} ->
        # LiveFunctions.sweet_alert(socket, message, "success")

        LiveFunctions.notify_parent(message)
        {:noreply, socket}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset, :form)}

      {:error, error} ->
        LiveFunctions.notify_parent(error)

        {:noreply, socket}
    end
  end

  defp save_user(%{assigns: assigns} = socket, :new, params) do
    new_params =
      Map.merge(
        params["access_roles"],
        %{
          "department_id" => assigns.role,
          "created_by" => assigns.current_user.id,
          "user_interface" => true,
          "editable" => true,
          "deleted" => false
        }
      )

    display =
      Map.merge(params["access_roles"], %{
        "department" => Roles.get_department_roles!(assigns.role).name
      })

    AccessRoleLive.create_access_roles(socket, Map.put(new_params, "display", display))
    |> case do
      {:ok, _message, _maker_checker} ->
        LiveFunctions.notify_parent("Access Role created successfully")
        {:noreply, socket}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset, :form)}

      {:error, error} ->
        socket.endpoint.broadcast(
          "sweet_alert:" <> to_string(assigns.current_user.id),
          "sweet_alert:" <> to_string(assigns.current_user.id),
          %{message: error, icon: "error"}
        )

        {:noreply, socket}
    end
  end
end

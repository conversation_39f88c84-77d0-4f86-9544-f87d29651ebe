<div class="px-4 sm:px-6 lg:px-8">
  <div class="sm:flex sm:items-center mt-8">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">
        <%= @role_data.name %> Access role management
      </h1>
    </div>

    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <.header>
        <div class="flex items-center my-4">
          <%= if Enum.member?(@permissions, "access_roles-view") do %>
            <.link
              class="text-base font-semibold cursor-pointer py-2 px-4 text-gray-900 border-b-8 hover:text-brand-1"
              patch={~p"/roles/access_roles/#{@role_data.id}/view"}
            >
              Role users
            </.link>
          <% end %>

          <%= if Enum.member?(@permissions, "access_roles-add") do %>
            <.link
              class="text-base font-semibold cursor-pointer py-2 px-4 border-b-8 text-brand-1 border-brand-1"
              patch={~p"/roles/access_roles/#{@role_data.id}/add"}
            >
              Non-role users
            </.link>
          <% end %>
        </div>
      </.header>
    </div>
  </div>

  <div class="mt-4 flow-root">
    <div class="-mx-4 -my-2 sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <Table.main_table id="adverts" rows={@data} params={@params} data_loader={@data_loader}>
          <:col :let={james} label={table_link(@params, "Email", :email)}>
            <%= james.email %>
          </:col>

          <:col :let={james} label={table_link(@params, "First Name", :first_name)}>
            <%= james.first_name %>
          </:col>

          <:col :let={james} label={table_link(@params, "Last Name", :first_name)}>
            <%= james.last_name %>
          </:col>

          <:col :let={james} label={table_link(@params, "Status", :status)}>
            <Table.table_string_status status={james.status} />
          </:col>

          <:col :let={james} label={table_link(@params, "Department Role", :system_role)}>
            <%= james.system_role %>
          </:col>

          <:col :let={james} label={table_link(@params, "Access Role", :access_role)}>
            <%= james.access_role %>
          </:col>

          <:action :let={james}>
            <Option.bordered>
              <a
                href="#"
                phx-click="add_user"
                phx-value-id={james.id}
                class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-gray-100 hover:text-gray-900"
              >
                Add User
              </a>
            </Option.bordered>
          </:action>
        </Table.main_table>
      </div>
    </div>
  </div>
</div>

<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>

<div class="px-4 sm:px-6 lg:px-8">
  <div class="sm:flex sm:items-center mt-8">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">Access Roles Management</h1>
    </div>

    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <.header>
        <:actions>
          <.link navigate={~p"/roles/system_roles"}>
            <.button>
              ← Department Roles
            </.button>
          </.link>
          <.button phx-click="add_record">New Access Role</.button>
        </:actions>
      </.header>
    </div>
  </div>
  <!-- FILTERS -->
  <%= if @showFilter do %>
    <div class="border overflow-hidden animate-slide-in-from-top transition-all ease-in-out duration-500 rounded-lg bg-white my-4 flex flex-col gap-4 items-start">
      <h1 class="px-4 py-2 bg-gray-50 border-b font-medium w-full">Filter Access Roles</h1>

      <FormJ.filter_form
        for={@form}
        class="w-full px-4"
        id="filter"
        phx-change="filter_change"
        phx-submit="search"
      >
        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-2 mb-4">
          <FormJ.input_filter field={@form[:name]} type="text" label="Name" placeholder="Name" />
          <FormJ.input_filter
            field={@form[:status]}
            type="select"
            label="Status"
            prompt="--Select Status"
            options={[{"All", ""}, {"Active", "1"}, {"Inactive", "0"}]}
          />
        </div>

        <p class="text-gray-500 font-medium">Date Filters</p>

        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 pt-2">
          <FormJ.input_filter field={@form[:start_date]} type="date" label="From" />
          <FormJ.input_filter field={@form[:end_date]} type="date" label="To" />
        </div>

        <div class="flex gap-4 items-center justify-start mt-4 p-4 w-full border-t font-medium">
          <.button
            type="button"
            phx-click="reset_filter"
            class="cursor-pointer hover:text-brand-1 py-2"
          >
            Reset
          </.button>

          <.button type="submit" class="cursor-pointer hover:text-brand-1 py-2">Search</.button>
        </div>
      </FormJ.filter_form>
    </div>
  <% end %>
  <!-- END OF FILTERS -->
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <Table.main_table id="adverts" rows={@data} params={@params} data_loader={@data_loader}>
          <:col :let={james} label={table_link(@params, "Date", :inserted_at)}>
            <%= Calendar.strftime(
              NaiveDateTime.add(james.inserted_at, 7200, :second),
              "%d %B %Y %H:%M:%S"
            ) %>
          </:col>

          <:col :let={james} label={table_link(@params, "Name", :name)}><%= james.name %></:col>

          <:col :let={james} label={table_link(@params, "Description", :description)}>
            <%= james.description %>
          </:col>

          <:col :let={james} label="Permissions">
            <span class="text-sm text-gray-600">
              <%= james.permissions %> permissions(s)
            </span>
          </:col>
          <:col :let={james} label={table_link(@params, "User Count", :user)}>
            <%= to_string(james.user) <> " User(s)" %>
          </:col>

          <:col :let={james} label={table_link(@params, "Status", :status)}>
            <Table.table_numeric_status status={james.status} />
          </:col>

          <:action :let={james}>
            <%= if james.status != 4 do %>
              <Option.bordered>
                <%= if james.status == 1 do %>
                  <%= if Enum.member?(@permissions, "access_roles-view_users") or Enum.member?(@permissions, "access_roles-add_users") do %>
                    <.link
                      class="text-gray-700 block px-4 py-2 text-sm hover:bg-gray-100 hover:text-gray-900 w-full text-left"
                      role="menuitem"
                      navigate={~p"/roles/access_roles/#{james.id}/view"}
                    >
                      Manage role users
                    </.link>
                  <% end %>

                  <%= if Enum.member?(@permissions, "access_roles-view_permissions") do %>
                    <.link
                      class="text-gray-700 block px-4 py-2 text-sm hover:bg-gray-100 hover:text-gray-900 w-full text-left"
                      role="menuitem"
                      navigate={~p"/roles/permissions/#{james.id}/access"}
                    >
                      Manage permissions
                    </.link>
                  <% end %>

                  <%= if james.editable do %>
                    <button
                      type="button"
                      class="text-gray-700 block px-4 py-2 text-sm hover:bg-gray-100 hover:text-gray-900 w-full text-left"
                      role="menuitem"
                      phx-click="edit_record"
                      phx-value-id={james.id}
                      x-on:click="open = ! open"
                    >
                      Edit
                    </button>
                  <% end %>

                  <button
                    type="button"
                    class="text-rose-700 block px-4 py-2 text-sm hover:bg-gray-100 hover:text-rose-500 w-full text-left"
                    role="menuitem"
                    phx-click="status"
                    phx-value-status="0"
                    phx-value-id={james.id}
                    x-on:click="open = ! open"
                  >
                    Deactivate
                  </button>
                <% end %>

                <%= if james.status == 0 do %>
                  <button
                    type="button"
                    class="text-green-700 block px-4 py-2 text-sm hover:bg-gray-100 hover:text-green-500 w-full text-left"
                    role="menuitem"
                    phx-click="status"
                    phx-value-status="1"
                    phx-value-id={james.id}
                    x-on:click="open = ! open"
                  >
                    Activate
                  </button>
                <% end %>

                <%= if james.status == 3 or james.status == nil do %>
                  <span class="text-danger">No Actions</span>
                <% end %>
              </Option.bordered>
            <% else %>
              <div class="text-center text-danger">No Actions</div>
            <% end %>
          </:action>
        </Table.main_table>
      </div>
    </div>
  </div>
</div>

<Model.small :if={@live_action in [:new, :edit]} id="user-modal" show return_to="close_model">
  <.live_component
    module={AppWeb.Settings.AccessRoleLive.FormComponent}
    id={@record.id || :new}
    title={@page_title}
    action={@live_action}
    struct={@record}
    role={@role}
    current_user={@current_user}
    remote_ip={@remote_ip}
    user_agent={@user_agent}
    permissions={@permissions}
    live_socket_identifier={@live_socket_identifier}
    browser_info={@browser_info}
  />
</Model.small>

<Model.confirmation_model
  :if={@confirmation_model}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>

defmodule AppWeb.Authenticated.Authentication.DepartmentRoleLive.Index do
  use AppWeb, :live_view

  alias App.Service.Table.DepartmentRoles, as: ListData

  alias App.{
    Roles,
    Roles.DepartmentRoles,
    Service.SystemRoleLive.Function
  }

  @impl true
  def mount(_params, session, %{assigns: assigns} = socket) do
    Task.start(fn ->
      Audit.create_access(
        session,
        {"Department Role Settings", "Accessed Department Role Settings Page", "/"},
        {true, assigns.current_user.id}
      )
    end)

    filter_data = %{
      "name" => nil,
      "status" => nil,
      "start_date" => nil,
      "end_date" => nil
    }

    socket =
      assign(socket, data: [])
      |> assign(info_wording: "Yes")
      |> assign(data_loader: true)
      |> assign(maker_checker: false)
      |> assign(live_socket_id: session["live_socket_id"])
      |> assign(confirmation_model: false)
      |> assign(confirmation_model_title: "Are you sure?")
      |> assign(confirmation_model_text: "")
      |> assign(confirmation_model_agree: "")
      |> assign(confirmation_model_reject: "close_confirmation_model")
      |> assign(confirmation_model_params: "")
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> assign(showFilter: false)
      |> assign(form: useform(filter_data))
      |> LivePageControl.order_by_composer()
      |> LivePageControl.i_search_composer()
      |> LivePageControl.maker_checker_status()

    {:ok, socket}
  end

  @impl true
  def handle_params(params, url, socket) do
    socket.endpoint.broadcast_from!(
      self(),
      "nav:" <> socket.assigns.live_socket_id,
      "change_nav",
      %{uri_path: URI.parse(url).path}
    )

    send(self(), {:get_list, params})
    socket = assign(socket, :params, params)

    {
      :noreply,
      LivePageControl.order_by_composer(socket, params)
      |> apply_action(socket.assigns.live_action, params)
    }
  end

  defp useform(data) do
    to_form(
      data,
      as: "filter"
    )
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Departments")
    |> assign(:record, %DepartmentRoles{})
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{})

      {:get_list, params} ->
        list(socket, params)

      {FormLive, _, %{action: action, param: params}} ->
        if action == "dismiss" do
          {:noreply, assign(socket, :maker_checker, false)}
        else
          send(self(), {:department_status, Map.merge(params, action)})
          {:noreply, assign(socket, :info_wording, "Processing")}
        end

      {:change_record_status, params} ->
        department_status(socket, params)

      {AppWeb.LiveFunctions, message} ->
        send(self(), {:get_list, %{}})

        {
          :noreply,
          LiveFunctions.sweet_alert(socket, message, "success")
          |> assign(:page_title, "Add Department Role")
          |> assign(:live_action, :index)
          |> assign(:record, %DepartmentRoles{})
        }
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  defp handle_event_switch(target, value, %{assigns: assigns} = socket) do
    case target do
      "status" ->
        status = if value["status"] == "0", do: "disable", else: "enable"

        if assigns.maker_checker_status do
          form = [%{type: "textarea", label: "Remarks", name: "remarks", value: ""}]

          {:noreply,
           assign(socket, :maker_checker, true)
           |> assign(:form, form)
           |> assign(:no_click, false)
           |> assign(:form_title, "User Description")
           |> assign(:info_message, "Are you sure you want to #{status} Payment Provider?")
           |> assign(:info_modal_param, Map.merge(value, %{"action" => status}))
           |> assign(:info_wording, String.capitalize(status))}
        else
          {
            :noreply,
            assign(socket, :confirmation_model, true)
            |> assign(:confirmation_model_params, Map.merge(value, %{"action" => status}))
            |> assign(:confirmation_model_text, "You want to #{String.capitalize(status)} role")
            |> assign(:confirmation_model_agree, "change_record_status")
          }
        end

      "change_record_status" ->
        LiveFunctions.change_record_status(value, socket)

      "add_record" ->
        socket =
          socket
          |> assign(:page_title, "Add Department Role")
          |> assign(:live_action, :new)
          |> assign(:record, %DepartmentRoles{})

        {:noreply, socket}

      "export" ->
        LiveFunctions.export_records(socket, value, "department_service", socket.assigns.params)

      "filter_change" ->
        assign(socket, form: useform(value["filter"]))
        |> noreply()

      "filter" ->
        value = if socket.assigns.showFilter == true, do: false, else: true

        assign(socket, :showFilter, value)
        |> noreply()

      "reset_filter" ->
        socket
        |> assign(form: useform(%{}))
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        noreply(
          socket
          |> assign(checked_ids: [])
          |> assign(param_list: [])
        )

      "close_model" ->
        socket =
          socket
          |> assign(:page_title, "Listing Admin Users")
          |> assign(:live_action, :index)
          |> assign(:record, %DepartmentRoles{})

        {:noreply, socket}

      "edit_record" ->
        socket =
          socket
          |> assign(:page_title, "Edit Department Role")
          |> assign(:live_action, :edit)
          |> assign(:record, Roles.get_department_roles!(value["id"]))

        {:noreply, socket}

      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :confirmation_model, false)
        }

      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

      _ ->
        {:noreply, socket}
    end
  end

  defp list(socket, params) do
    {
      :noreply,
      assign(socket, :data, ListData.index(LivePageControl.create_table_params(socket, params)))
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end

  defp department_status(%{assigns: assigns} = socket, params) do
    Function.status_changer(
      socket,
      Roles.get_department_roles!(params["id"]),
      Map.put(
        params,
        "updated_by",
        assigns.current_user.id
      )
    )
    |> case do
      {:ok, message, _maker_checker} ->
        Process.send_after(self(), :get_list, 10)

        {
          :noreply,
          assign(socket, :confirmation_model, false)
          |> assign(confirmation_model_icon: "exclamation_circle")
          |> assign(:maker_checker, false)
          |> assign(info_wording: "Yes")
          |> LiveFunctions.sweet_alert(message, "success")
        }

      {:error, error} ->
        {
          :noreply,
          assign(socket, :confirmation_model, false)
          |> assign(confirmation_model_icon: "exclamation_circle")
          |> assign(:maker_checker, false)
          |> assign(:info_wording, "Yes")
          |> LiveFunctions.sweet_alert(error, "error")
        }
    end
  end

  # defp get_results(socket, status) do
  #   case status.status do
  #     0 ->
  #       if connected?(socket), do: Process.send_after(self(), :get_list, 1)
  #       success_message(socket, status)

  #     1 ->
  #       error_message(socket, status)
  #   end
  # end

  # defp success_message(%{assigns: assigns} = socket, status) do
  #   socket.endpoint.broadcast(
  #     "sweet_alert:" <> to_string(assigns.current_user.id),
  #     "sweet_alert:" <> to_string(assigns.current_user.id),
  #     %{
  #       message: status.message
  #     }
  #   )

  #   {:noreply,
  #    assign(socket, :confirmation_model, false)
  #    |> assign(confirmation_model_icon: "exclamation_circle")
  #    |> assign(:maker_checker, false)
  #    |> assign(info_wording: "Yes")}
  # end

  # defp error_message(%{assigns: assigns} = socket, status) do
  #   socket.endpoint.broadcast(
  #     "sweet_alert:" <> to_string(assigns.current_user.id),
  #     "sweet_alert:" <> to_string(assigns.current_user.id),
  #     %{
  #       message: status.message,
  #       icon: "info",
  #       timer: 5000,
  #       title: "Error"
  #     }
  #   )

  #   {
  #     :noreply,
  #     assign(socket, :confirmation_model, false)
  #     |> assign(confirmation_model_icon: "exclamation_circle")
  #     |> assign(:maker_checker, false)
  #   }
  # end
end

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  <.header>
    <%= @page_title %>
    <:subtitle>
      Role: <%= (@user.role && @user.role.name) || "No Role Assigned" %>
    </:subtitle>
    <:actions>
      <.link navigate={~p"/users/system_admin"} class="text-sm text-gray-600 hover:text-gray-900">
        ← Back to Users
      </.link>
    </:actions>
  </.header>

  <div class="mt-8 space-y-8">
    <!-- Role Permissions (Read Only) -->
    <div>
      <h3 class="text-lg font-medium text-gray-900 mb-4">Role Permissions</h3>
      <div class="bg-gray-50 rounded-lg p-4">
        <%= if Enum.empty?(@grouped_permissions.role) do %>
          <p class="text-gray-500 text-sm">No permissions from role</p>
        <% else %>
          <div class="grid lg:grid-cols-4 md:grid-cols-3 sm:grid-cols-2 xs:grid-cols-1 gap-4">
            <%= for {service, permissions} <- @grouped_permissions.role do %>
              <div class="mb-4">
                <h4 class="font-medium text-gray-700 mb-2"><%= service %></h4>
                <div class="space-y-1">
                  <%= for permission <- permissions do %>
                    <div class="flex items-center text-sm">
                      <svg
                        class="h-4 w-4 text-green-500 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clip-rule="evenodd"
                        />
                      </svg>
                      <span class="text-gray-600"><%= permission.description %></span>
                      <span class="ml-2 text-xs text-gray-400">(from role)</span>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
    <!-- Current Additional Permissions -->
    <%= if not Enum.empty?(@grouped_permissions.user_specific) do %>
      <div>
        <h3 class="text-lg font-medium text-gray-900 mb-4">Current Additional Permissions</h3>
        <div class="bg-blue-50 rounded-lg p-4">
          <%= for {service, permissions} <- @grouped_permissions.user_specific do %>
            <div class="mb-4">
              <h4 class="font-medium text-gray-700 mb-2"><%= service %></h4>
              <div class="space-y-1">
                <%= for permission <- permissions do %>
                  <div class="flex items-center text-sm">
                    <svg
                      class="h-4 w-4 text-blue-500 mr-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    <span class="text-gray-600"><%= permission.description %></span>
                    <span class="ml-2 text-xs text-blue-600">(user-specific)</span>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
    <!-- Available Additional Permissions -->
    <div>
      <h3 class="text-lg font-medium text-gray-900 mb-4">Grant Additional Permissions</h3>
      <div class="text-sm text-gray-600 mb-4">
        These are permissions from the user's department that are not included in their role.
      </div>
      <div class="mb-6">
        <div class="relative">
          <input
            type="text"
            phx-keyup="search"
            phx-debounce="300"
            value={@search_query}
            placeholder="Search permissions..."
            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <svg
            class="absolute right-3 top-3 h-5 w-5 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>
      <%= if Enum.empty?(@grouped_permissions.available) and Enum.empty?(@grouped_permissions.user_specific) do %>
        <div class="bg-gray-50 rounded-lg p-8 text-center">
          <svg
            class="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <p class="mt-2 text-gray-500">
            All available permissions are already assigned through the role.
          </p>
        </div>
      <% else %>
        <div class="space-y-4">
          <div class="grid lg:grid-cols-4 md:grid-cols-3 sm:grid-cols-2 xs:grid-cols-1 gap-4">
            <!-- Currently selected user-specific permissions -->
            <%= for {service, permissions} <- @grouped_permissions.user_specific do %>
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
                  <h4 class="text-sm font-medium text-gray-900"><%= service %></h4>
                </div>
                <div class="divide-y divide-gray-200">
                  <%= for permission <- permissions do %>
                    <div class="px-6 py-3 hover:bg-gray-50">
                      <label class="flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          phx-click="toggle_permission"
                          phx-value-permission_id={permission.id}
                          checked={MapSet.member?(@selected_additional, permission.id)}
                          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <div class="ml-3">
                          <div class="text-sm font-medium text-gray-900">
                            <%= permission.description %>
                          </div>
                          <%= if permission.tab do %>
                            <div class="text-xs text-gray-500">Tab: <%= permission.tab %></div>
                          <% end %>
                        </div>
                      </label>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
            <!-- Available permissions to add -->
            <%= for {service, permissions} <- filter_permissions(@grouped_permissions.available, @search_query) do %>
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
                  <h4 class="text-sm font-medium text-gray-900"><%= service %></h4>
                </div>
                <div class="divide-y divide-gray-200">
                  <%= for permission <- permissions do %>
                    <div class="px-6 py-3 hover:bg-gray-50">
                      <label class="flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          phx-click="toggle_permission"
                          phx-value-permission_id={permission.id}
                          checked={MapSet.member?(@selected_additional, permission.id)}
                          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <div class="ml-3">
                          <div class="text-sm font-medium text-gray-900">
                            <%= permission.description %>
                          </div>
                          <%= if permission.tab do %>
                            <div class="text-xs text-gray-500">Tab: <%= permission.tab %></div>
                          <% end %>
                        </div>
                      </label>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    <!-- Save Button -->
    <div class="flex justify-end space-x-4 pt-4">
      <.link navigate={~p"/users/system_admin"}>
        <.button type="button" variant="secondary">Cancel</.button>
      </.link>
      <.button phx-click="save">
        Save Additional Permissions
      </.button>
    </div>
  </div>
</div>

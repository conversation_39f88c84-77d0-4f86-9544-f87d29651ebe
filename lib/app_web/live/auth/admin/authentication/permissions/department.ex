defmodule AppWeb.Authenticated.Authentication.PermissionsLive.Department do
  @moduledoc false
  use AppWeb, :live_view

  alias App.Roles

  @impl true
  def mount(%{"role" => id}, _session, socket) do
    department_role = Roles.get_department_role!(id)

    all_permissions =
      Roles.list_permissions()
      |> group_permissions_by_service()

    current_permission_ids = Enum.map(department_role.permissions, & &1.id)

    socket
    |> assign(:department_role, department_role)
    |> assign(:all_permissions, all_permissions)
    |> assign(:selected_permissions, MapSet.new(current_permission_ids))
    |> assign(:search_query, "")
    |> assign(:page_title, "Manage Permissions - #{department_role.name}")
    |> ok()
  end

  @impl true
  def handle_event("toggle_permission", %{"permission_id" => permission_id}, socket) do
    permission_id = String.to_integer(permission_id)
    selected = socket.assigns.selected_permissions

    new_selected =
      if MapSet.member?(selected, permission_id) do
        MapSet.delete(selected, permission_id)
      else
        MapSet.put(selected, permission_id)
      end

    assign(socket, :selected_permissions, new_selected)
    |> noreply()
  end

  @impl true
  def handle_event("toggle_service", %{"service" => service}, socket) do
    service_permissions =
      socket.assigns.all_permissions
      |> Enum.find(fn {s, _} -> s == service end)
      |> elem(1)
      |> Enum.map(& &1.id)

    selected = socket.assigns.selected_permissions

    all_selected? = Enum.all?(service_permissions, &MapSet.member?(selected, &1))

    new_selected =
      if all_selected? do
        Enum.reduce(service_permissions, selected, &MapSet.delete(&2, &1))
      else
        Enum.reduce(service_permissions, selected, &MapSet.put(&2, &1))
      end

    assign(socket, :selected_permissions, new_selected)
    |> noreply()
  end

  @impl true
  def handle_event("search", %{"value" => query}, socket) do
    assign(socket, :search_query, query)
    |> noreply()
  end

  @impl true
  def handle_event("save", _params, socket) do
    permission_ids = MapSet.to_list(socket.assigns.selected_permissions)
    department_role = socket.assigns.department_role

    case Roles.update_department_role_permissions(department_role, permission_ids) do
      %{} ->
        socket
        |> put_flash(:info, "Permissions updated successfully")
        |> push_navigate(to: ~p"/roles/system_roles")
        |> noreply()

      {:error, _changeset} ->
        put_flash(socket, :error, "Error updating permissions")
        |> noreply()
    end
  end

  defp group_permissions_by_service(permissions) do
    permissions
    |> Enum.group_by(& &1.service)
    |> Enum.sort_by(fn {service, _} -> service end)
  end

  defp filter_permissions(permissions, search_query) do
    query = String.downcase(search_query)

    permissions
    |> Enum.map(fn {service, perms} ->
      filtered_perms =
        Enum.filter(perms, fn perm ->
          String.contains?(String.downcase(perm.description), query) ||
            String.contains?(String.downcase(perm.service), query) ||
            String.contains?(String.downcase(perm.tab || ""), query)
        end)

      {service, filtered_perms}
    end)
    |> Enum.reject(fn {_, perms} -> Enum.empty?(perms) end)
  end

  defp all_selected?(permissions, selected_permissions) do
    permission_ids = Enum.map(permissions, & &1.id)
    Enum.all?(permission_ids, &MapSet.member?(selected_permissions, &1))
  end
end

<div class="min-h-screen bg-white p-6">
  <div class="max-w-7xl mx-auto">
    <!-- Dashboard Header -->
    <div class="sm:flex sm:items-center mb-10">
      <div class="sm:flex-auto rounded-2xl p-6 border border-gray-200">
        <h1 class="text-4xl font-bold mb-2 text-gray-900">Dashboard</h1>

        <p class="text-gray-600">Welcome back! Here's what's happening today.</p>
      </div>

      <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none" phx-update="ignore" id="time2">
        <div class="px-6 py-3 rounded-xl shadow-lg border border-gray-200">
          <span phx-hook="LocalTime" id="time" class="text-lg text-gray-900 font-semibold"></span>
        </div>
      </div>
    </div>
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <%= if "dashboard-total_licenses" in @permissions do %>
        <%= live_render(@socket, AppWeb.Dashboard.Admin.TotalLicenseApplications,
          id: "dashboard-total_licenses"
        ) %>
      <% end %>
      <%= if "applications-new_license-view" in @permissions do %>
        <%= live_render(@socket, AppWeb.Dashboard.Admin.NewApplications,
          id: "applications-new_license-view"
        ) %>
      <% end %>

      <%= if "applications-returned_applicant-view" in @permissions do %>
        <%= live_render(@socket, AppWeb.Dashboard.Admin.ReturnedToApplicant,
          id: "applications-returned_applicant-view"
        ) %>
      <% end %>

      <%= if "applications-submitted_to_supervisor-view" in @permissions do %>
        <%= live_render(@socket, AppWeb.Dashboard.Admin.SubmittedSupervisor,
          id: "applications-submitted_to_supervisor-view"
        ) %>
      <% end %>
      
     
    

      <%= if "applications-submit_market_operations-view" in @permissions do %>
        <%= live_render(@socket, AppWeb.Dashboard.Admin.SubmittedMarketOperations,
          id: "applications-submit_market_operations-view"
        ) %>
      <% end %>
    </div>
    <!-- Recent Licenses Table -->
  
  </div>
</div>

defmodule AppWeb.Dashboard.AdminLive.Index do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # When LiveView is connected, we'll use these values for the animation

      {:ok,
       assign(socket,
         page_title: "Dashboard",
         animate: true,
         animate_cards: true
       )}
    else
      # Initial load without animations
      {:ok,
       assign(socket,
         page_title: "Dashboard",
         animate: false,
         animate_cards: false
       )}
    end
  end
end

defmodule AppWeb.CommitteeLive.FormComponent do
  use AppWeb, :live_component
  alias App.Utilities

  alias App.Service.ServiceCommittee.Functions
  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.simple_form
        for={@form}
        id="city-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-2 gap-4">
          <.input
            field={@form[:name]}
            type="text"
            placeholder="Enter a name"
            required
            label={raw(~c"Name <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:description]}
            type="text"
            placeholder="Enter a description"
            required
            label={raw(~c"Description <span class='text-rose-500'>*</span>")}
          />
        </div>

        <:actions>
          <div class="align-left">
            <.button type="button" phx-click="close_model">Close</.button>

            <%= if @form.source.valid? do %>
              <.button type="submit" phx-disable-with="submitting...">Submit</.button>
            <% end %>
          </div>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{record: record} = assigns, socket) do
    changeset = Utilities.change_commitee(record)

    socket
    |> assign(assigns)
    |> assign_form(changeset)
    |> ok()
  end

  @impl true
  def handle_event("validate", %{"committees" => params} = _attrs, socket) do
    changeset =
      socket.assigns.record
      |> Utilities.change_commitee(params)
      |> Map.put(:action, :validate)

    socket
    |> assign_form(changeset)
    |> noreply()
  end

  def handle_event("save", %{"committees" => params}, socket) do
    save_record(socket, socket.assigns.action, params)
    |> noreply()
  end

  defp save_record(socket, :edit, params) do
    case Functions.update(socket, socket.assigns.record, params) do
      {:ok, record} ->
        notify_parent({:saved, record, "Committee Updated Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}
    end
  end

  defp save_record(socket, :new, params) do
    case Functions.create(socket, params) do
      {:ok, record} ->
        notify_parent({:saved, record, "Committee Created Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}
    end
  end

  defp notify_parent(msg) do
    send(self(), {__MODULE__, msg})
  end
end

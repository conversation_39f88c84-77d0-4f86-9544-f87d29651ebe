defmodule AppWeb.ApprovalLevelsLive.FormComponent do
  use AppWeb, :live_component
  alias App.{Utilities, Roles}

  alias App.Service.ServiceApprovalLevels.Functions
  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.simple_form
        for={@form}
        id="city-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-2 gap-4">
          <.input
            field={@form[:approval_status]}
            phx-hook="validateIntegerHook"
            type="number"
            min="1"
            placeholder="Enter Approval Level"
            required
            label={raw(~c"Approval Level <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:department_id]}
            type="select"
            prompt="--Select Department--"
            options={@role_id_data}
            placeholder="Select Department"
            required
            label={raw(~c"Department <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:role_id]}
            type="select"
            prompt="--Select Role--"
            options={@select_role}
            placeholder="Select Role"
            required
            label={raw(~c"Role <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:count_down]}
            type="select"
            prompt="-- No --"
            options={[{"Yes", 1}]}
            label={raw(~c"Start Count Down")}
          />
          <.input
            field={@form[:condition_tracking]}
            type="select"
            prompt="-- No --"
            options={[{"Yes", 1}]}
            label={raw(~c"Start Conditional Tracking")}
          />
          <.input
            field={@form[:genarate_summary]}
            type="select"
            prompt="-- No --"
            options={[{"Yes", 1}]}
            label={raw(~c"Generate Summary")}
          />
          <.input
            field={@form[:evaluation]}
            type="select"
            prompt="-- No --"
            options={[{"Yes", 1}]}
            label={raw(~c"Allow Evaluation")}
          />
        </div>

        <:actions>
          <div class="align-left">
            <.button type="button" phx-click="close_model">Close</.button>

            <%= if @form.source.valid? do %>
              <.button type="submit" phx-disable-with="submitting...">Submit</.button>
            <% end %>
          </div>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{action: action} = assigns, socket) do
    c_mount(action, assigns, socket)
  end

  def c_mount(:edit, %{record: record, category_id: _category_id} = assigns, socket) do
    {select_role, department_id} = Roles.select_department_role_access_role(record.role_id)

    changeset =
      Utilities.change_approval_levels(record, %{"department_id" => to_string(department_id)})

    socket
    |> assign(assigns)
    |> assign(:select_role, select_role)
    |> assign(:role_id_data, Roles.select_department_role())
    |> assign_form(changeset)
    |> ok()
  end

  def c_mount(:new, %{record: record, category_id: _category_id} = assigns, socket) do
    changeset = Utilities.change_approval_levels(record)

    socket
    |> assign(assigns)
    |> assign(:select_role, [])
    |> assign(:role_id_data, Roles.select_department_role())
    |> assign_form(changeset)
    |> ok()
  end

  @impl true
  def handle_event("validate", %{"approval_levels" => params} = attrs, socket) do
    changeset =
      socket.assigns.record
      |> Utilities.change_approval_levels(
        Map.put(params, "categories_id", socket.assigns.category_id)
      )
      |> Map.put(:action, :validate)

    role =
      if attrs["_target"] == ["approval_levels", "department_id"] do
        Roles.select_access_role_from_department_by(params["department_id"])
      else
        socket.assigns.select_role
      end

    socket
    |> assign_form(changeset)
    |> assign(:select_role, role)
    |> noreply()
  end

  def handle_event("save", %{"approval_levels" => params}, socket) do
    save_record(socket, socket.assigns.action, params)
    |> noreply()
  end

  defp save_record(socket, :edit, params) do
    case Functions.update(socket, socket.assigns.record, params) do
      {:ok, record} ->
        notify_parent({:saved, record, "Approval Level Updated Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        assign_form(socket, changeset)
    end
  end

  defp save_record(socket, :new, params) do
    case Functions.create(socket, Map.put(params, "categories_id", socket.assigns.category_id)) do
      {:ok, record} ->
        notify_parent({:saved, record, "Approval Level Created Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        assign_form(socket, changeset)
    end
  end

  defp notify_parent(msg) do
    send(self(), {__MODULE__, msg})
  end
end

<%= if !@data_loader do %>
  <div class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Application Details View -->
      <div class="mb-6 flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">Application Details</h1>
      </div>
      
      <div class="flex flex-col lg:flex-row gap-6">
        <!-- Main application details column -->
        <div class="lg:flex-1">
          <Card.section_with_header
            title="Submitted Details"
            subtitle="Review application information"
          >
            <%= if Enum.member?(@permissions, "review-application") do %>
              <div class="px-6 py-3 bg-gray-50 border-b border-gray-100">
                <p class="text-sm text-gray-600 flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-2 text-brand-10"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Tick the boxes that require review
                </p>
              </div>
            <% end %>
            
            <.simple_form for={@form} id="preview-form" class="p-6">
              <div class="bg-white rounded-xl mb-8">
                <div class="flex items-center mb-6 pb-3 border-b border-gray-100">
                  Status
                  <span class="text-xs px-3 py-1 rounded-full font-medium">
                    <Table.table_approval_status status={@record.status} name={@current_level} />
                  </span>
                </div>
                
                <div class="mb-8">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                    <%= for field <- @license_data do %>
                      <%= if field.field_type != "upload" do %>
                        <div class="group relative p-4 bg-gray-50 rounded-lg border border-gray-100 hover:border-brand-10 hover:shadow-sm transition-all">
                          <div class="flex items-start gap-3">
                            <%= if Enum.member?(@permissions, "review-application") do %>
                              <input
                                type="checkbox"
                                phx-click="submit_for_review_form"
                                phx-value-id={field.id}
                                phx-value-license_id={@record.license_id}
                                phx-value-user_license_mapping_id={@record.id}
                                phx-value-associate_id={
                                  Enum.map_join(@associates, ",", fn assoc -> assoc.id end)
                                }
                                phx-value-field={field.field_name}
                                phx-value-field_label={field.field_label}
                                class={[
                                  "h-5 w-5 rounded border-gray-300 text-brand-10 focus:ring-brand-2"
                                ]}
                                checked={
                                  Enum.member?(@submit_for_review_list, to_string(field.id))
                                }
                              />
                            <% end %>
                            
                            <div class="flex-1">
                              <div class="text-sm font-medium text-gray-700 mb-2">
                                <%= String.capitalize(to_string(field.field_label)) %>
                              </div>
                              
                              <div class="text-base text-gray-900 p-3 bg-white rounded-md border border-gray-100">
                                <%= @field_data |> Map.get(field.field_name, "Not provided") %>
                              </div>
                            </div>
                          </div>
                        </div>
                      <% end %>
                    <% end %>
                  </div>
                </div>
                
                <%= if Enum.any?(@license_data, &(&1.field_type == "upload")) do %>
                  <div class="mt-10 pt-6 border-t border-gray-100">
                    <div class="flex items-center mb-6">
                      <svg
                        class="h-5 w-5 text-brand-10 mr-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      
                      <h3 class="text-lg font-medium text-gray-900">Uploaded Documents</h3>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <%= for field <- @license_data do %>
                        <%= if field.field_type == "upload" do %>
                          <%= if is_nil(field.field_dependents) ||
                        field.dependent_selection == (if !Enum.empty?(field.field_dependents),
                          do: @record.data[List.first(field.field_dependents)]) do %>
                            <div class="flex items-center justify-between bg-gray-50 p-4 rounded-lg border border-gray-200 hover:border-indigo-300 hover:shadow-sm transition-all">
                              <%= if Enum.member?(@permissions, "review-application") do %>
                                <input
                                  type="checkbox"
                                  phx-click="submit_for_review_form"
                                  phx-value-id={field.id}
                                  phx-value-user_license_mapping_id={@record.id}
                                  phx-value-license_id={@record.license_id}
                                  phx-value-associate_id={
                                    Enum.map_join(@associates, ",", fn assoc -> assoc.id end)
                                  }
                                  phx-value-field={field.field_name}
                                  phx-value-field_label={field.field_label}
                                  class={[
                                    "h-5 w-5 rounded border-gray-300 text-brand-10 focus:ring-brand-2"
                                  ]}
                                  checked={
                                    Enum.member?(@submit_for_review_list, to_string(field.id))
                                  }
                                />
                              <% end %>
                              
                              <div class="flex items-center flex-1 ml-3">
                                <svg
                                  class="h-8 w-8 text-brand-10 mr-3"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                                  />
                                </svg>
                                
                                <div>
                                  <div class="text-sm font-medium text-gray-700">
                                    <%= field.field_label %>
                                  </div>
                                  
                                  <div class="text-xs text-gray-500 mt-1">
                                    Document
                                  </div>
                                </div>
                              </div>
                              
                              <.button
                                type="button"
                                phx-click="view_document"
                                phx-value-doc_name={field.field_label}
                                phx-value-path={@field_data |> Map.get(field.field_name)}
                                class="px-4 py-2 rounded-md text-sm flex items-center gap-1 bg-brand-10 text-indigo-700 hover:bg-brand-10"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  class="h-4 w-4"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                  />
                                  <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                  />
                                </svg>
                                View
                              </.button>
                            </div>
                          <% end %>
                        <% end %>
                      <% end %>
                    </div>
                  </div>
                <% end %>
              </div>
              
              <div class="flex items-center justify-between pt-6 border-t border-gray-100">
                <.button
                  :if={is_nil(@record.associated_license_id)}
                  type="button"
                  onclick="history.back()"
                  class="flex items-center gap-2 px-4 py-2 rounded-md bg-brand-10 text-gray-700 hover:bg-gray-200 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    />
                  </svg>
                  Back to Applications
                </.button>
                
                <%= if @review == [] do %>
                  <%= if @approval_level && is_nil(@record.associated_license_id) and !Enum.member?(@permissions, "sandbox-evaluate") do %>
                    <div class="flex gap-3">
                      <.button
                        type="button"
                        phx-click="approve_application"
                        phx-value-activity="decline"
                        class="flex items-center gap-2 px-5 py-2 rounded-md bg-red-500 text-white hover:bg-red-600 transition-colors"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                        Decline
                      </.button>
                      
                      <.button
                        type="button"
                        phx-click="approve_application"
                        phx-value-activity="approve"
                        class="flex items-center gap-2 px-5 py-2 rounded-md bg-green-500 text-white hover:bg-green-600 transition-colors"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        Approve
                      </.button>
                    </div>
                  <% end %>
                  
                  <%= if Enum.member?(@permissions, "sandbox-evaluate") do %>
                    <div class="flex gap-3">
                      <.link
                        navigate={~p"/license/applications/evaluation/#{@record.id}"}
                        class="phx-submit-loading:opacity-75 ease-in-out delay-150 duration-300 rounded-md hover:border-brand-10 bg-brand-10 hover:bg-transparent border-2 hover:text-brand-10 py-2 px-3 text-sm font-semibold leading-6 text-white active:text-white/80 flex items-center gap-2  bg-brand-10 transition-colors"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 4v16m8-8H4"
                          />
                        </svg>
                        Evaluate
                      </.link>
                    </div>
                  <% end %>
                <% else %>
                  <div class="flex gap-3">
                    <.button
                      type="button"
                      phx-click="approve_application"
                      phx-value-activity="decline"
                      class="flex items-center gap-2 px-5 py-2 rounded-md bg-red-500 text-white hover:bg-red-600 transition-colors"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                      Decline
                    </.button>
                    
                    <button
                      disabled
                      type="button"
                      class="flex items-center gap-2 px-5 py-2 rounded-md bg-gray-300 text-gray-500 cursor-not-allowed"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      Approve
                    </button>
                  </div>
                <% end %>
              </div>
            </.simple_form>
          </Card.section_with_header>
        </div>
        <!-- Side column - all sidebars combined -->
        <div class="lg:w-1/3 space-y-6">
          <!-- Associated Licenses section -->
          <%= if @associates != [] do %>
            <Card.section_with_header
              title="Associated Licenses"
              subtitle={"Licenses associated with #{@record.record_name}"}
            >
              <div class="p-4">
                <ul class="divide-y divide-gray-100">
                  <%= for associate <- @associates do %>
                    <.link phx-click="view_associate" phx-value-id={associate.id} class="block">
                      <li class="hover:bg-gray-50 rounded-lg transition-colors">
                        <div class="flex items-center justify-between p-3">
                          <div>
                            <p class="font-medium text-gray-800">
                              <%= associate.record_name %>
                            </p>
                            
                            <div class="flex items-center mt-1">
                              <span class="text-sm text-gray-600">
                                <%= associate.license.name %>
                              </span>
                            </div>
                          </div>
                          
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-5 w-5 text-brand-2"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </div>
                      </li>
                    </.link>
                  <% end %>
                </ul>
              </div>
            </Card.section_with_header>
          <% end %>
          <!-- Submitted for Review section -->
          <%= if @review != [] && @role_department not in [4] do %>
            <Card.section_with_header
              title="Submitted for Review"
              subtitle="Fields submitted for review"
            >
              <div class="p-4">
                <ul class="divide-y divide-gray-100">
                  <%= for jay <- @review do %>
                    <li class="py-3">
                      <div class="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                        <div class="flex-1">
                          <div class="flex items-center justify-between">
                            <p class="font-medium text-gray-800">
                              <%= jay.attention_field %>
                              <span class="text-sm text-gray-500">
                                <%= if jay.associated_id == nil,
                                  do: "[License Summary]",
                                  else: "[Associated Licenses]" %>
                              </span>
                            </p>
                            
                            <button
                              phx-click="remove_field"
                              phx-value-id={jay.id}
                              phx-value-license_id={jay.license_id}
                              phx-value-user_license_mapping_id={jay.user_license_mapping_id}
                              class="ml-2 p-1 rounded-full hover:bg-gray-200 text-gray-500 hover:text-gray-700 transition-colors"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-5 w-5"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path
                                  fill-rule="evenodd"
                                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                  clip-rule="evenodd"
                                />
                              </svg>
                            </button>
                          </div>
                          
                          <%= if jay.attention_status == "submitted" do %>
                            <span class="inline-flex items-center mt-2 px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              Pending Correction
                            </span>
                          <% end %>
                          
                          <%= if jay.attention_status == "corrected" do %>
                            <span class="inline-flex items-center mt-2 px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Corrected
                            </span>
                          <% end %>
                        </div>
                      </div>
                    </li>
                  <% end %>
                </ul>
              </div>
            </Card.section_with_header>
          <% end %>
          <!-- Uploaded Files section -->
          <%= if Enum.member?(@permissions, "review-application") do %>
            <Card.section_with_header
              title="Uploaded Files"
              subtitle="Files uploaded by the Commission"
            >
              <.simple_form
                for={@upload_form}
                id="card-form"
                phx-change="validate_file"
                phx-submit="upload"
                class="p-4"
              >
                <AppWeb.DocumentComponent.document_upload uploads={@uploads} db_files={@db_files} />
                <%= if @uploads.file.entries != [] do %>
                  <div class="flex items-center justify-center mt-4">
                    <.button
                      type="submit"
                      class="px-4 py-2 bg-brand-10 hover:bg-indigo-700 text-white rounded-lg transition"
                    >
                      Upload Files
                    </.button>
                  </div>
                <% end %>
              </.simple_form>
            </Card.section_with_header>
          <% end %>
        </div>
      </div>
    </div>
  </div>
<% end %>
<!-- Modals -->
<Model.fullscreen
  :if={@live_action in [:view_associate]}
  id="view_associate-modal"
  title="Associated License"
  show
  return_to="close_model"
>
  <iframe
    src={@associate}
    id="associate_iframe"
    title="associate"
    style="width: 100%;"
    height="700"
    name="ASSOCIATE"
  >
  </iframe>
</Model.fullscreen>

<Model.fullscreen
  :if={@live_action in [:view_document]}
  id="view_document-modal"
  title={@doc_name}
  show
  return_to="close_model"
>
  <iframe
    src={@document}
    id="document_iframe"
    title="document"
    style="width: 100%;"
    height="700"
    name="DOCUMENT"
  >
  </iframe>
</Model.fullscreen>

<Model.fullscreen
  :if={@live_action in [:view_licence_form]}
  id="view_licence-modal"
  title="License Form"
  show
  return_to="close_model"
>
  <iframe
    src={@licence_form}
    id="licence_iframe"
    title="License"
    style="width: 100%;"
    height="700"
    name="License"
  >
  </iframe>
</Model.fullscreen>

<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
<Model.confirmation_with_des
  :if={@live_action == :confirm_with_des}
  show
  id="confirmation_with_des-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
<Model.confirmation_with_comments
  :if={@live_action == :confirmation_with_comments}
  show
  id="confirmation_with_comments-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
<Model.small
  :if={@live_action == :view_conditions}
  id="user-modal"
  show
  return_to="close_model"
  title={@page_title}
>
  <.live_component
    module={AppWeb.LicenceDetailsLive.ConditionComponent}
    id={@current_user.id}
    title={@page_title}
    action={@live_action}
    record={@record}
    browser_info={@browser_info}
    current_user={@current_user}
  />
</Model.small>

<Model.fullscreen
  :if={@live_action == :assoc}
  id="user-modal"
  show
  return_to="close_model"
  title={@page_title}
>
  <.live_component
    module={AppWeb.Auth.ViewApplicationsLive.Representative}
    id="assoc{@assoc_id}"
    title={@page_title}
    assoc_id={@assoc_id}
    action={@live_action}
    current_user={@current_user}
    browser_info={@browser_info}
    live_socket_identifier={@live_socket_identifier}
  />
</Model.fullscreen>

<%= if @reject_document_form=="show" do %>
  <div class="relative z-10" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity backdrop-blur-sm">
    </div>
    
    <div class="fixed inset-0 z-10 overflow-y-auto">
      <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <svg
                class="h-6 w-6 text-red-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            
            <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
              <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">
                Reject (<%= @field_label %>)
              </h3>
              
              <div class="mt-2"></div>
            </div>
          </div>
          
          <form phx-submit="submit_for_review" class="mt-5">
            <div class="space-y-4">
              <!-- Textarea with label -->
              <div>
                <label for="rejection-reason" class="block text-sm font-medium text-gray-700 mb-2">
                  Rejection Reason
                </label>
                 <textarea
                  id="rejection-reason"
                  name="reason"
                  rows="4"
                  placeholder={"Please provide detailed reason for rejecting #{@field_label}"}
                  class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500 transition duration-150 ease-in-out text-sm resize-none"
                ></textarea> <input type="hidden" name="associate_id" value={@associate_id} />
                <input type="hidden" name="attention_field" />
                <input type="hidden" name="field_label" value={@field_label} />
                <input type="hidden" name="license_id" value={@license_id} />
                <input
                  type="hidden"
                  name="user_license_mapping_id"
                  value={@user_license_mapping_id}
                /> <input type="hidden" name="field_id" value={@field_id} />
                <p class="mt-1 text-sm text-gray-500">
                  This reason will be communicated to the applicant.
                </p>
              </div>
              <!-- Buttons -->
              <div class="flex items-center justify-end space-x-3">
                <button
                  type="button"
                  phx-click="close_modal"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200"
                >
                  Cancel
                </button>
                
                <button
                  type="submit"
                  class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                  Reject
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
<% end %>

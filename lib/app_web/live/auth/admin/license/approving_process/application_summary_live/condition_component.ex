defmodule AppWeb.LicenceDetailsLive.ConditionComponent do
  use AppWeb, :live_component
  alias App.LicenseConditions
  alias App.Licenses.{LicenseConditionsMapping}

  @impl true
  def render(assigns) do
    ~H"""
    <div class="bg-white rounded-lg shadow-sm p-6">
      <h3 class="text-xl font-semibold text-gray-800 mb-5">Recommended Conditions</h3>

      <div class="mb-6">
        <%= if Enum.empty?(@existing_conditions) do %>
          <div class="bg-gray-50 rounded-md p-4 text-center">
            <p class="text-gray-500">No conditions have been added yet</p>
          </div>
        <% else %>
          <div class="overflow-hidden bg-white border border-gray-200 rounded-md">
            <ul class="divide-y divide-gray-200">
              <%= for {condition, _index} <- Enum.with_index(@existing_conditions) do %>
                <li class="flex items-center justify-between p-4 hover:bg-gray-50 transition duration-150">
                  <div class="flex-1 min-w-0 mr-4">
                    <p class="font-medium text-gray-800 truncate">
                      <%= condition.condition.name %>
                      <span class="inline-flex items-center rounded-full text-xs font-medium ">
                        (<%= condition.responsibility %>)
                      </span>
                    </p>

                    <div class="mt-1 flex items-center">
                      <span class="inline-flex items-center rounded-full text-xs font-medium ">
                        Description:&nbsp; <%= condition.condition.description %>
                      </span>
                    </div>

                    <div class="mt-1 flex items-center">
                      <span class="inline-flex items-center rounded-full text-xs font-medium ">
                        Expiring Date:&nbsp;
                        <%= if condition.expiring_date do %>
                          <%= Calendar.strftime(
                            Date.from_iso8601!(condition.expiring_date),
                            "%d %B %Y"
                          ) %>
                        <% else %>
                          <span class="text-gray-400 italic">No date Set</span>
                        <% end %>
                        &nbsp; &nbsp;
                        Remaining Day(s):&nbsp;
                        <%= if condition.expiring_date do %>
                          <%= case Date.diff(Date.from_iso8601!(condition.expiring_date), Date.utc_today()) do %>
                            <% days when days < 0 -> %>
                              <span class="text-red-500 italic">Expired</span>
                            <% 0 -> %>
                              <span class="text-yellow-500 italic">Expires Today</span>
                            <% days -> %>
                              <%= days %>
                          <% end %>
                        <% end %>
                      </span>
                    </div>
                  </div>

                  <button
                    type="button"
                    phx-click="remove_condition"
                    phx-value-id={condition.id}
                    phx-value-name={condition.condition.name}
                    class="ml-4 flex-shrink-0 p-1 rounded-md text-gray-400 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-red-500"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </button>
                </li>
              <% end %>
            </ul>
          </div>
        <% end %>
      </div>

      <div class="bg-gray-50 rounded-lg p-5">
        <h4 class="text-lg font-medium text-gray-700 mb-4">Add New Condition</h4>

        <.simple_form
          for={@form}
          id="condition-form"
          phx-target={@myself}
          phx-change="validate"
          phx-submit="save"
        >
          <div class="grid grid-cols-2 gap-4">
            <.input
              field={@form[:condition_id]}
              type="select"
              prompt="--Select Condition--"
              options={@conditions}
              placeholder="Select a Condition"
              required
              label={raw(~c"License Condition <span class='text-rose-500'>*</span>")}
            />
            <.input
              field={@form[:responsibility]}
              type="select"
              prompt="--Responsibility--"
              options={@responsibility}
              required
              label={raw(~c"Responsibility <span class='text-rose-500'>*</span>")}
            />
            <%= if @condition_type && @condition_type.type != "Law Enforcement" do %>
              <.input
                field={@form[:expiring_date]}
                min={@min_date}
                type="date"
                required
                label={raw(~c"Condition Expiring Date <span class='text-rose-500'>*</span>")}
              />
            <% end %>
          </div>

          <:actions>
            <div class="align-left">
              <.button type="button" phx-click="close_model">Close</.button>

              <%= if @form.source.valid? do %>
                <.button type="submit" phx-disable-with="submitting...">Add Condition</.button>
              <% end %>

              <%= if !Enum.empty?(@existing_conditions) do %>
                <.button type="button" phx-disable-with="submitting...">Submit</.button>
              <% end %>
            </div>
          </:actions>
        </.simple_form>
      </div>
    </div>
    """
  end

  @impl true
  def update(%{record: record} = assigns, socket) do
    changeset = LicenseConditions.change_license_condition_mapping_attrs()

    existing = LicenseConditions.get_license_condition!(record.id)

    condition = LicenseConditions.get_license_expiring_condition!(record.id)
    get_application_id = App.Jobs.JobConditionTracking.get_application_id(record.id)

    expiring_days =
      case condition do
        nil ->
          nil

        _ ->
          if is_nil(condition.expiring_date) do
            nil
          else
            Date.diff(Date.from_iso8601!(condition.expiring_date), Date.utc_today())
          end
      end

    update_condition =
      case condition do
        nil -> nil
        _ -> App.Jobs.JobConditionTracking.init()
      end

    # Date.diff(Date.from_iso8601!("2025-06-19"), Date.utc_today())
    {:ok,
     socket
     |> assign(assigns)
     |> assign(expiring_date: expiring_days)
     |> assign(get_application_id: get_application_id)
     |> assign(update_condition: update_condition)
     |> assign_form(changeset)
     |> assign(min_date: Date.utc_today() |> Date.to_iso8601())
     |> assign(existing_conditions: existing)
     |> assign(
       conditions: LicenseConditions.get_conditions_tuple!(Enum.map(existing, & &1.condition_id))
     )
     |> assign(condition_type: nil)
     |> assign(responsibility: [])
     |> assign(record: record)}
  end

  @impl true
  def handle_event("validate", %{"license_conditions_mapping" => params} = _attrs, socket) do
    changeset =
      %LicenseConditionsMapping{}
      |> LicenseConditions.change_license_condition_mapping(params)
      |> Map.put(:action, :validate)

    type = LicenseConditions.get_license_condition_type_by_id!(params["condition_id"])

    socket
    |> assign_form(changeset)
    |> assign(condition_type: type)
    |> assign(responsibility: responsibility_options(type))
    |> noreply()
  end

  def handle_event("save", %{"license_conditions_mapping" => params}, socket) do
    save_record(socket, socket.assigns.action, params)
    |> noreply()
  end

  defp save_record(socket, :view_conditions, params) do
    case LicenseConditions.create_condition_mapping(
           socket,
           Map.put(params, "application_id", socket.assigns.record.id)
         ) do
      {:ok, %{"record" => record}} ->
        notify_parent({:saved, record.summary_user_draft, "Condition Successfully Added"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}

      {:error, msg} ->
        notify_parent({:error, msg})
        socket
    end
  end

  defp notify_parent(msg) do
    send(self(), {__MODULE__, msg})
  end

  defp responsibility_options(record) do
    if record && record.type == "Law Enforcement" do
      ["Commission"]
    else
      ["Applicant"]
    end
  end
end

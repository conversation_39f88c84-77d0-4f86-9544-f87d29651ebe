defmodule AppWeb.Auth.CategoryLandingLive.Index do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})

  alias App.Licenses

  @impl true
  def render(assigns) do
    ~H"""
    <div
      class="p-6"
      x-data="{ show: false }"
      x-init="setTimeout(() => show = true, 100)"
      x-show="show"
      x-transition:enter="transition ease-out duration-500"
      x-transition:enter-start="opacity-0 transform -translate-y-4"
      x-transition:enter-end="opacity-100 transform translate-y-0"
    >
      <div
        class="sm:flex sm:items-center mb-10"
        x-data="{ show: false }"
        x-init="setTimeout(() => show = true, 200)"
        x-show="show"
        x-transition:enter="transition ease-out duration-500"
        x-transition:enter-start="opacity-0 transform -translate-x-4"
        x-transition:enter-end="opacity-100 transform translate-x-0"
      >
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-bold text-gray-800 mb-6">Application Categories</h1>
        </div>
      </div>
      
      <%= if Enum.empty?(@categories) do %>
        <.empty_state />
      <% else %>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
          <%= for {category, idx} <- Enum.with_index(@categories) do %>
            <div
              x-data="{ show: false, hover: false }"
              x-init={"setTimeout(() => show = true, #{150 * idx})"}
              x-show="show"
              x-transition:enter="transition ease-out duration-500"
              x-transition:enter-start="opacity-0 transform translate-y-8"
              x-transition:enter-end="opacity-100 transform translate-y-0"
              @mouseenter="hover = true"
              @mouseleave="hover = false"
              class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform"
              style={"border-top: 4px solid #{category["color"]}"}
            >
              <div class="relative p-6">
                <!-- Decorative Element -->
                <div
                  class="absolute top-0 right-0 w-24 h-24 opacity-10"
                  style={"background: radial-gradient(circle at top right, #{category["color"]}, transparent 70%)"}
                >
                </div>
                
                <div class="flex items-start justify-between">
                  <div class="mr-4 flex-1">
                    <!-- Title with Count Badge -->
                    <div class="flex items-center justify-between mb-2">
                      <h3 class="text-xl font-bold text-gray-900"><%= category["name"] %></h3>
                      
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white shadow-sm"
                        style={"background-color: #{category["color"]}"}
                      >
                        <%= Map.get(category, "application_count", 0) %>
                      </span>
                    </div>
                    <!-- Description  -->
                    <%= if Map.has_key?(category, "description") do %>
                      <p class="text-gray-600 text-sm line-clamp-2"><%= category["description"] %></p>
                    <% else %>
                      <p class="text-gray-500 text-sm">Explore this category for more information.</p>
                    <% end %>
                  </div>
                </div>
                <!-- Action Buttons -->
                <div class="mt-5 flex justify-between items-center">
                  <button
                    phx-value-id={category["id"]}
                    phx-click="view"
                    class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-300 flex items-center gap-2 shadow-sm"
                  >
                    View
                  </button>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
    """
  end

  @impl true
  def mount(params, _session, socket) do
    IO.inspect(params, label: "Mount Params")

    # Get all active license categories
    all_categories = Licenses.list_active_license_categories()

    # Filter categories based on user type
    categories =
      case socket.assigns.current_user.user_type do
        "EVALUATOR" ->
          # For EVALUATOR users, only show the sandbox category
          Enum.filter(all_categories, fn category ->
            String.downcase(category["name"]) == "sandbox"
          end)

        _ ->
          # For all other users, show all categories
          all_categories
      end

    # Add application counts to each category
    categories_with_counts =
      Enum.map(categories, fn category ->
        count = get_application_count_for_category(category["id"], params["page"], socket.assigns)
        Map.put(category, "application_count", count)
      end)

    {:ok, assign(socket, categories: categories_with_counts, params: params)}
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, socket) do
    case target do
      "view" ->
        view(value, socket)

      _ ->
        {:noreply, socket}
    end
  end

  defp view(%{"id" => id}, socket) do
    link = "/maintenance/#{socket.assigns.params["page"]}/#{id}"

    case socket.assigns.params["page"] do
      "conditional_levels" ->
        {:noreply, push_navigate(socket, to: link)}

      _ ->
        {:noreply,
         push_navigate(socket,
           to: "/license/main_applications/#{socket.assigns.params["page"]}/#{id}"
         )}
    end
  end

  # function to get application count for a category
  defp get_application_count_for_category(category_id, page_type, assigns) do
    case page_type do
      "New" ->
        Licenses.count_new_applications_by_category(category_id, assigns)

      "Returned_to_Applicant" ->
        Licenses.count_returned_to_applicant_by_category(category_id, assigns.current_user.id)

      "Returned_from_Applicant" ->
        Licenses.count_returned_to_applicant_by_category(category_id, assigns.current_user.id)

      "Submitted_to_Supervisor" ->
        status_list = Licenses.get_status_list(category_id, assigns)

        Licenses.count_submitted_to_supervisor_by_category(
          category_id,
          status_list,
          assigns.current_user.id
        )

      "Submitted_to_Board" ->
        Licenses.count_submitted_to_board_by_category(category_id, assigns)

      "Returned_from_Supervisor" ->
        Licenses.count_returned_from_supervisor_by_category(category_id, assigns)

      "Returned_from_Board" ->
        Licenses.count_returned_from_board_by_category(category_id, assigns)

      "Board" ->
        Licenses.count_board_applications_by_category(category_id, assigns)

      "Market_Operations" ->
        status =
          Licenses.get_license_approval_status(
            assigns.role_department,
            assigns.current_user.role_id
          )

        Licenses.count_market_operations_by_category(category_id, status, assigns.current_user.id)

      "summary" ->
        status_list = Licenses.get_status_list(category_id, assigns)

        Licenses.count_application_summary_by_category(
          category_id,
          status_list,
          assigns.current_user.id
        )

      "all" ->
        Licenses.count_applications_by_category_and_type(category_id, assigns)

      "conditional" ->
        Licenses.count_conditional_applications_by_category(category_id)

      "Processed" ->
        Licenses.count_processed_applications_by_category(category_id)

      _ ->
        Licenses.count_applications_by_category_and_type(category_id, assigns)
    end
  rescue
    # Return 0 if there's any error fetching the count
    _ -> 0
  end
end

defmodule AppWeb.LicenceDetailsLive.ViewComponent do
  use AppWeb, :live_component

  @impl true
  def render(assigns) do
    ~H"""
    <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
      <!-- Simple header -->
      <div class="px-6 py-4 border-b border-gray-100">
        <h1 class="text-xl font-medium text-gray-800">Application Summary</h1>

        <p class="text-sm text-gray-500 mt-1">Review submitted information</p>
      </div>
      <!-- Main content area -->
      <div class="p-6">
        <!-- Application fields -->
        <div class="mb-8">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-5">
            <%= for field <- @license_data do %>
              <%= if field.field_type != "upload" do %>
                <div>
                  <div class="text-lg font-medium text-gray-500 mb-1">
                    <%= String.capitalize(to_string(field.field_label)) %>
                  </div>

                  <div class="text-sm text-gray-800">
                    <%= @field_data |> Map.get(field.field_name, "Not provided") %>
                  </div>
                </div>
              <% end %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def update(
        %{license_data: license_data, record: record, field_data: field_data} = assigns,
        socket
      ) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign(license_data: license_data)
     |> assign(record: record)
     |> assign(field_data: field_data)}
  end
end

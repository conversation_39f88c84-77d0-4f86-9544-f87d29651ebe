defmodule AppWeb.EvaluatorUploadedList.ViewComponent do
  use AppWeb, :live_component

  @impl true
  def mount(socket) do
    {:ok, assign(socket, page: 1, per_page: 10)}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
      <br />
      <Table.extract_paginated
        id="review"
        rows={@records}
        record_title={@record_title}
        page={@page}
        per_page={@per_page}
        myself={@myself}
      >
        <:col :let={james} label="Row"><%= james["key"] %></:col>
        
        <:col :let={james} label="First Name"><%= james["col1"] %></:col>
        
        <:col :let={james} label="Last Name"><%= james["col2"] %></:col>
        
        <:col :let={james} label="Email"><%= james["col3"] %></:col>
        
        <:col :let={james} label="Mobile"><%= james["col4"] %></:col>
        
        <:description :let={james}><%= james["message"] %></:description>
      </Table.extract_paginated>
    </div>
    """
  end

  @impl true
  def update(%{records: records, title: record_title}, socket) do
    new_records =
      Jason.decode!(records)
      |> Enum.sort(&(&1["key"] < &2["key"]))

    {
      :ok,
      socket
      |> assign(:records, new_records)
      |> assign(:record_title, record_title)
    }
  end

  @impl true
  def handle_event("paginate", %{"page" => page}, socket) do
    {page, _} = Integer.parse(page)
    {:noreply, assign(socket, :page, page)}
  end
end

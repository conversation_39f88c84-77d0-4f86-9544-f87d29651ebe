defmodule AppWeb.Auth.ApplicationEvaluatorsLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias App.{Licenses, Evaluations, Repo}
  alias App.Service.Table.SandboxEvaluators, as: TableUserQuery

  alias App.Service.ServiceCompanyAttach.Functions

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("evaluators-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Evaluators Management", "Accessed Evaluators Management Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      filter_data = %{
        "record_name" => nil,
        "status" => nil,
        "start_date" => nil,
        "end_date" => nil
      }
      evaluations =
        Evaluations.get_evaluations_by_evaluator!(assigns.current_user.id, params["id"])
        evaluations_by_section = Enum.group_by(evaluations, & &1.criteria.section)

      sections = evaluations_by_section |> Map.keys() |> Enum.sort()

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()
        |> assign(params: params)
        |> assign(:evaluations_by_section, evaluations_by_section)
          |> assign(:evaluations, evaluations)
        |> assign_form(Evaluations.change_evaluations())
         |> assign(:sections, sections)
        |> assign(:current_section, if(sections != [], do: Enum.at(sections, 0), else: 1))
        |> assign(:evaluation_scores, build_scores_map(evaluations))
        |> assign(:evaluation_comments, build_comments_map(evaluations))
        |> assign(application: Licenses.get_application!(params["id"]))
        |> assign(confirmation_model: false)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(session_id: session["live_socket_id"])
        |> assign(live_socket_id: session["live_socket_id"])
        |> assign(showFilter: false)
        |> assign(:show_evaluated_summary, false)
        |> assign(form: useform(filter_data))

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Evaluators Management", "Accessed Evaluators Management Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  defp useform(data) do
    to_form(
      data,
      as: "filter"
    )
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "Evaluators Management")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {:process_attach, params} ->
        attach_associate(params, socket)

      {:search_records, params} ->
        send(self(), {:get_list, %{"filter" => params}})
        noreply(socket)

      {AppWeb.LiveFunctions, message} ->
        send(self(), {:get_list, %{}})

        {
          :noreply,
          socket
          |> put_flash(:info, message)
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, %{assigns: assigns} = socket) do
    case target do
      "close_model" ->
        {:noreply, assign(socket, :live_action, :index)}

        "show_evaluated_summary" ->
        application_evaluator_id = value["evaluator-id"]
        if application_evaluator_id do
          # Get the ApplicationEvaluator record to find the evaluator_id
          application_evaluator = Repo.get!(App.Evaluation.ApplicationEvaluators, application_evaluator_id)
          evaluations = Evaluations.get_evaluations_by_evaluator!(application_evaluator.evaluator_id, assigns.params["id"])
          evaluations_by_section = Enum.group_by(evaluations, & &1.criteria.section)

          # Get evaluator info from the table data
          evaluator_info = Enum.find(assigns.data, fn item -> item.id == String.to_integer(application_evaluator_id) end)

          socket =
            assign(socket, :show_evaluated_summary, true)
            |> assign(:evaluations_by_section, evaluations_by_section)
            |> assign(:evaluations, evaluations)
            |> assign(:evaluation_scores, build_scores_map(evaluations))
            |> assign(:evaluation_comments, build_comments_map(evaluations))
            |> assign(:current_evaluator, evaluator_info)

          {:noreply, socket}
        else
          {:noreply, assign(socket, :show_evaluated_summary, true)}
        end

      "close_evaluated_summary" ->
        {:noreply, assign(socket, :show_evaluated_summary, false)}

      "export" ->
        LiveFunctions.export_records(socket, value, "users_service", assigns.form.params())

      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "filter_change" ->
        assign(socket, form: useform(value["filter"]))
        |> noreply()

      "filter" ->
        value = if socket.assigns.showFilter == true, do: false, else: true

        assign(socket, :showFilter, value)
        |> noreply()

      "reset_filter" ->
        socket
        |> assign(form: useform(%{}))
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        noreply(socket)

      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :index)
        }

      "process" ->
        status =
          case value["status"] do
            "1" -> "approve"
            "-1" -> "decline"
          end

        model_call =
          cond do
            value["activity"] == "decline" -> :confirm_with_des
            true -> :confirm
          end

        {
          :noreply,
          assign(socket, :live_action, model_call)
          |> assign(
            :confirmation_model_text,
            "Are you sure you want to #{status} this request?"
          )
          |> assign(:confirmation_model_params, Map.merge(value, %{"action" => status}))
          |> assign(:confirmation_model_agree, "process_attach")
        }

      "process_attach" ->
        send(self(), {:process_attach, value})

        socket
        |> assign(confirmation_model_title: "")
        |> assign(:confirmation_model_icon, "loading")
        |> assign(
          :confirmation_model_text,
          "Processing."
        )
        |> noreply()

      _ ->
        {:noreply, socket}
    end
  end

  defp attach_associate(params, socket) do
    attrs = Jason.decode!(params["params"])

    Functions.index(socket, Map.put(attrs, "reason", params["reason"]), attrs["action"])
    |> case do
      {:ok, _} ->
        success_message(socket, "Process Completed Successfully.")

      {:error, _error} ->
        error_message(socket, "Failed to process request.")
    end
  end

  defp list(socket, params) do
    {
      :noreply,
      assign(
        socket,
        :data,
        TableUserQuery.index(
          LivePageControl.create_table_params(socket, params),
          params
        )
      )
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end

  defp success_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_list, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "success"
      )
    }
  end

  defp error_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_list, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "error"
      )
    }
  end

   defp get_section_title(evaluations, section) do
    evaluations
    |> Enum.find_value(fn eval ->
      if eval.criteria.section == section, do: eval.criteria.type, else: nil
    end)
  end

  defp get_current_section_evaluations(evaluations_by_section, current_section) do
    Map.get(evaluations_by_section, current_section, [])
  end

  defp section_progress(sections, current_section) do
    current_index =
      Enum.find_index(sections, &(&1 == current_section))

    total_sections = length(sections)

    if total_sections > 0 do
      ((current_index + 1) / total_sections * 100) |> round()
    else
      0
    end
  end


  defp is_last_section?(sections, current_section) do
    List.last(sections) == current_section
  end

  defp is_first_section?(sections, current_section) do
    List.first(sections) == current_section
  end

   defp build_scores_map(evaluations) do
    evaluations
    |> Enum.reduce(%{}, fn eval, acc ->
      Map.put(acc, eval.criteria.id, eval.rating)
    end)
  end

  defp build_comments_map(evaluations) do
    evaluations
    |> Enum.reduce(%{}, fn eval, acc ->
      Map.put(acc, eval.criteria.id, eval.comments || "")
    end)
  end
end

<div class="min-h-screen bg-gray-50">
  <div class="max-w-4xl mx-auto py-8 px-4">
    <!--  Header -->
      
   <!-- Evaluation Form -->
    <div class="p-6">
      <div class="space-y-6">
        <div class="p-4 text-center rounded-md mb-6 sticky top-16 bg-white shadow-md z-10">
          <div class="flex items-center justify-between">
            <button
              onclick="window.history.back()"
              class="text-gray-600 hover:text-gray-800 focus:outline-none rounded-full p-2 border border-brand-10"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            
            <h1 class="text-2xl font-bold tracking-tight text-gray-900 flex-1 text-center">
              <%= @application.record_name %>'s Evaluation
            </h1>
            
            <div class="w-5"></div>
            <!-- Placeholder for alignment -->
          </div>
        </div>
        
        <%= if @step == :agreement and !@check_confirmation do %>
          <div class="bg-white shadow-lg rounded-lg p-8">
            <div class="bg-white-50 p-6 mb-8">
              <h2 class="text-xl font-semibold text-brand-900">Evaluation Agreement</h2>
              
              <div class="space-y-4 text-gray-900">
                <p>
                  Before proceeding with the evaluation, please read and agree to the following:
                </p>
                
                <ul class="list-disc ml-6 space-y-2">
                  <li>I will evaluate this application fairly and objectively</li>
                  
                  <li>I will provide constructive and detailed feedback</li>
                  
                  <li>I understand that my evaluation will impact the application's outcome</li>
                  
                  <li>I will maintain confidentiality regarding the application details</li>
                </ul>
                
                <div class="bg-white p-4 rounded-lg border border-blue-200 mt-6">
                  <h3 class="font-medium text-gray-900 mb-2">Application Details:</h3>
                  
                  <p><strong>Applicant:</strong> <%= @application.record_name || "N/A" %></p>
                  
                  <p>
                    <strong>Submission Date:</strong> <%= @application.inserted_at
                    |> Calendar.strftime("%B %d, %Y") %>
                  </p>
                </div>
              </div>
            </div>
            
            <div class="flex justify-between">
              <.button
                phx-click="disagree"
                class="bg-red-600 hover:bg-brand-10 text-white font-semibold py-3 px-8 rounded-lg transition duration-300"
              >
                Disagree
              </.button>
              
              <.button
                phx-click="agree"
                class="bg-brand-10 hover:bg-brand-10 text-white font-semibold py-3 px-8 rounded-lg transition duration-300"
              >
                I Agree
              </.button>
            </div>
          </div>
        <% end %>
        
        <%= if @step == :summary do %>
          <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <!-- Progress Bar (Complete) -->
            <div class="bg-gray-200 h-2">
              <div class="bg-green-600 h-2 w-full transition-all duration-300"></div>
            </div>
            <!-- Header -->
            <div class="p-6">
              <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-semibold text-gray-900">Evaluation Summary</h2>
                
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  Ready for Submission
                </span>
              </div>
              <!-- Summary by Section -->
              <%= for section <- @sections do %>
                <div class="mb-6 border border-gray-200 rounded-lg p-4">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    Section <%= section %>: <%= get_section_title(@evaluations, section) %>
                  </h3>
                  
                  <div class="space-y-3">
                    <%= for evaluation <- get_current_section_evaluations(@evaluations_by_section, section) do %>
                      <div class="bg-white p-4 rounded border">
                        <div class="flex justify-between items-start mb-2">
                          <h4 class="font-medium text-gray-900">
                            <%= evaluation.criteria.name %>
                          </h4>
                          
                          <span class="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                            <%= evaluation.rating %>/4
                          </span>
                        </div>
                        
                        <%= if evaluation.comments && String.trim(evaluation.comments) != "" do %>
                          <div class="text-sm text-gray-600 mt-2">
                            <strong>Comment:</strong> <%= evaluation.comments %>
                          </div>
                        <% end %>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
              <!-- General Comment Section -->
              <div class="bg-gray-50 p-6 rounded-lg mb-6">
                <label for="general_comment" class="block text-lg font-medium text-gray-900 mb-3">
                  General Comments
                </label>
                
                <p class="text-sm text-gray-600 mb-4">
                  Provide any additional feedback or overall comments about this application.
                </p>
                 <textarea
                  id="general_comment"
                  name="general_comment"
                  rows="4"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter your general comments about the overall application..."
                  phx-change="update_general_comment"
                  phx-debounce="1000"
                  disabled={@evaluation_complete and not @edit_mode}
                ><%= @general_comment %></textarea>
              </div>
              <!-- Navigation -->
              <div class="flex justify-between pt-6 border-t border-gray-200">
                <div>
                  <.button
                    phx-click="back_to_evaluation"
                    class="bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-6 rounded-lg transition duration-300"
                  >
                    ← Back to Evaluation
                  </.button>
                </div>
                
                <div>
                  <.button
                    phx-click="submit_evaluation"
                    disabled={@evaluation_complete and not @edit_mode}
                    class="bg-green-600 hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-semibold py-3 px-8 rounded-lg transition duration-300"
                  >
                    <%= cond do %>
                      <% @evaluation_complete and not @edit_mode -> %>
                        Submitted ✓
                      <% @evaluation_complete and @edit_mode -> %>
                        Re-Submit Evaluation
                      <% true -> %>
                        Submit Final Evaluation
                    <% end %>
                  </.button>
                </div>
              </div>
            </div>
          </div>
        <% end %>
        
        <%= if @step == :evaluation do %>
          <!-- Progress Bar -->
          <div class="bg-gray-200 h-2">
            <div
              class="bg-blue-600 h-2 transition-all duration-300"
              style={"width: #{section_progress(@sections, @current_section)}%"}
            >
            </div>
          </div>
          <!-- Header -->
      
        <!-- Evaluation Form -->
          <div class="p-6">
            <div class="space-y-6">
              <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900">
                  Section <%= @current_section %> : ASSESSMENT AGAINST <%= get_section_title(
                    @evaluations,
                    @current_section
                  ) %>
                </h2>
                <!-- Edit Mode Controls -->
                <%= if @evaluation_complete do %>
                  <div class="flex items-center space-x-2">
                    <%= if @edit_mode do %>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                        Editing
                      </span>
                      
                      <button
                        phx-click="disable_edit_mode"
                        class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                        title="Disable Edit Mode"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    <% else %>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        Submitted ✓
                      </span>
                      
                      <button
                        phx-click="enable_edit_mode"
                        class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                        title="Enable Edit Mode"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                          />
                        </svg>
                      </button>
                    <% end %>
                  </div>
                <% end %>
              </div>
              <!-- Criteria for current section -->
              <%= for evaluation <- get_current_section_evaluations(
              @evaluations_by_section,
              @current_section
            ) do %>
                <div class="border border-gray-200 rounded-lg p-6 space-y-4">
                  <h3 class="font-medium text-gray-900"><%= evaluation.criteria.name %></h3>
                  
                  <%= if evaluation.criteria.description do %>
                    <p class="text-sm text-gray-600"><%= evaluation.criteria.description %></p>
                  <% end %>
                  <!-- Score Selection -->
                  <.simple_form
                    for={@form}
                    id={"rating-form-#{evaluation.id}"}
                    phx-change="validate"
                  >
                    <input type="hidden" name="evaluation[id]" value={evaluation.id} />
                    <.input
                      type="rating"
                      field={@form[:rating]}
                      value={Map.get(@evaluation_scores, evaluation.criteria.id, "")}
                      label="Rating (0-4)"
                      class="bg-gray-50 p-4 rounded-lg"
                      disabled={@evaluation_complete and not @edit_mode}
                    />
                    <!-- Comment Section -->
                    <div>
                      <label
                        for={"comment_#{evaluation.id}"}
                        class="block text-sm font-medium text-gray-700 mb-2"
                      >
                        Comments (In support of rating assigned.)
                      </label>
                      
                      <.input
                        type="textarea"
                        field={@form[:comments]}
                        phx-debounce="3000"
                        value={Map.get(@evaluation_comments, evaluation.criteria.id, "")}
                        rows="4"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder={"Provide feedback for #{evaluation.criteria.name}..."}
                        disabled={@evaluation_complete and not @edit_mode}
                      />
                    </div>
                  </.simple_form>
                </div>
              <% end %>
              <!-- Navigation -->
              <div class="flex justify-between pt-6 border-t border-gray-200">
                <div>
                  <%= unless is_first_section?(@sections, @current_section) do %>
                    <.button
                      phx-click="previous_section"
                      class="bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-6 rounded-lg transition duration-300"
                    >
                      ← Previous Section
                    </.button>
                  <% end %>
                </div>
                
                <div>
                  <%= if is_last_section?(@sections, @current_section) do %>
                    <.button
                      phx-click="show_summary"
                      class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg transition duration-300"
                    >
                      Review Summary →
                    </.button>
                  <% else %>
                    <.button
                      phx-click="next_section"
                      class="bg-brand-10 hover:bg-brand-10 text-white font-semibold py-2 px-6 rounded-lg transition duration-300"
                    >
                      Next Section →
                    </.button>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

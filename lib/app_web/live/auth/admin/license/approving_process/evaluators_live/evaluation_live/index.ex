defmodule AppWeb.Auth.EvaluatorsLive.EvaluationLive.Index do
  @moduledoc false
  use AppWeb, :live_view

  alias App.{Licenses, Evaluations}

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("sandbox-evaluate", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Evaluation", "Accessed Evaluation Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      # Get existing evaluations for this evaluator and application
      evaluations =
        Evaluations.get_evaluations_by_evaluator!(assigns.current_user.id, params["id"])

      # Group evaluations by section
      evaluations_by_section = Enum.group_by(evaluations, & &1.criteria.section)

      sections = evaluations_by_section |> Map.keys() |> Enum.sort()

      # check if evaluator has completed all evaluations
      evaluation_complete =
        Evaluations.check_evaluator_completion(assigns.current_user.id, params["id"]) ==
          length(evaluations)

      socket =
        assign(socket, params: params)
        |> assign(application: Licenses.get_application!(params["id"]))
        |> assign(:loader, false)
        |> assign(:step, :agreement)
        |> assign(:agreed, false)
        |> assign(
          check_confirmation:
            Evaluations.check_confirmation!(assigns.current_user.id, params["id"])
        )
        |> assign(:evaluations, evaluations)
        |> assign_form(Evaluations.change_evaluations())
        |> assign(:evaluations_by_section, evaluations_by_section)
        |> assign(:sections, sections)
        |> assign(:current_section, if(sections != [], do: Enum.at(sections, 0), else: 1))
        |> assign(:evaluation_scores, build_scores_map(evaluations))
        |> assign(:evaluation_comments, build_comments_map(evaluations))
        |> assign(:evaluation_complete, evaluation_complete)
        |> assign(:edit_mode, !evaluation_complete)
        |> assign(:general_comment, "")
        |> assign(:step, :evaluation)

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Evaluation", "Accessed Evaluation Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  defp build_scores_map(evaluations) do
    evaluations
    |> Enum.reduce(%{}, fn eval, acc ->
      Map.put(acc, eval.criteria.id, eval.rating)
    end)
  end

  defp build_comments_map(evaluations) do
    evaluations
    |> Enum.reduce(%{}, fn eval, acc ->
      Map.put(acc, eval.criteria.id, eval.comments || "")
    end)
  end

  @impl true
  def handle_event("agree", _params, socket), do: agree_function(socket)
  def handle_event("validate", params, socket), do: validate_function(params, socket)
  def handle_event("submit_evaluation", params, socket), do: submit_evaluation(params, socket)
  def handle_event("enable_edit_mode", _params, socket), do: enable_edit_mode(socket)
  def handle_event("disable_edit_mode", _params, socket), do: disable_edit_mode(socket)
  def handle_event("show_summary", _params, socket), do: show_summary(socket)
  def handle_event("back_to_evaluation", _params, socket), do: back_to_evaluation(socket)

  def handle_event("update_general_comment", %{"general_comment" => comment}, socket),
    do: update_general_comment(comment, socket)

  @impl true
  def handle_event("next_section", _params, socket) do
    current_section = socket.assigns.current_section

    # Get fresh data from database to check completion status
    fresh_evaluations =
      Evaluations.get_evaluations_by_evaluator!(
        socket.assigns.current_user.id,
        socket.assigns.params["id"]
      )

    # Group fresh evaluations by section
    fresh_evaluations_by_section = Enum.group_by(fresh_evaluations, & &1.criteria.section)

    current_section_evaluations =
      Map.get(fresh_evaluations_by_section, current_section, [])

    # Check if all criteria in current section are completed using fresh data
    all_completed =
      Enum.all?(current_section_evaluations, fn eval ->
        score = eval.rating
        comment = eval.comments || ""

        score && String.trim(comment) != ""
      end)

    if all_completed do
      current_index = Enum.find_index(socket.assigns.sections, &(&1 == current_section))
      next_section = Enum.at(socket.assigns.sections, current_index + 1)

      if next_section do
        # Update socket with fresh data and move to next section
        socket =
          socket
          |> assign(:current_section, next_section)
          |> assign(:evaluations, fresh_evaluations)
          |> assign(:evaluations_by_section, fresh_evaluations_by_section)
          |> assign(:evaluation_scores, build_scores_map(fresh_evaluations))
          |> assign(:evaluation_comments, build_comments_map(fresh_evaluations))

        {:noreply, socket}
      else
        # This is the last section, show summary instead of direct submit
        socket =
          socket
          |> assign(:step, :summary)
          |> assign(:evaluations, fresh_evaluations)
          |> assign(:evaluations_by_section, fresh_evaluations_by_section)
          |> assign(:evaluation_scores, build_scores_map(fresh_evaluations))
          |> assign(:evaluation_comments, build_comments_map(fresh_evaluations))

        {:noreply, socket}
      end
    else
      socket =
        LiveFunctions.sweet_alert(
          socket,
          "Please complete all scores and comments in this section",
          "warning"
        )

      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("previous_section", _params, socket) do
    # allow navigation even when editing is disabled
    current_section = socket.assigns.current_section
    current_index = Enum.find_index(socket.assigns.sections, &(&1 == current_section))

    if current_index > 0 do
      previous_section = Enum.at(socket.assigns.sections, current_index - 1)
      socket = assign(socket, :current_section, previous_section)
      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  defp agree_function(socket) do
    case Evaluations.create_evaluation(socket) do
      {:ok, _evaluation} ->
        # Reload evaluations from database to get the newly created records
        evaluations =
          Evaluations.get_evaluations_by_evaluator!(
            socket.assigns.current_user.id,
            socket.assigns.params["id"]
          )

        # Group evaluations by section
        evaluations_by_section = Enum.group_by(evaluations, & &1.criteria.section)
        sections = evaluations_by_section |> Map.keys() |> Enum.sort()

        # Check if evaluator has completed all evaluations
        evaluation_complete =
          Evaluations.check_evaluator_completion(
            socket.assigns.current_user.id,
            socket.assigns.params["id"]
          ) == length(evaluations)

        socket =
          socket
          |> assign(:agreed, true)
          |> assign(:step, :evaluation)
          |> assign(:evaluations, evaluations)
          |> assign(:evaluations_by_section, evaluations_by_section)
          |> assign(:sections, sections)
          |> assign(:current_section, if(sections != [], do: Enum.at(sections, 0), else: 1))
          |> assign(:evaluation_scores, build_scores_map(evaluations))
          |> assign(:evaluation_comments, build_comments_map(evaluations))
          |> assign(:evaluation_complete, evaluation_complete)
          |> assign(:edit_mode, !evaluation_complete)
          |> assign(:general_comment, "")
          |> assign(
            check_confirmation:
              Evaluations.check_confirmation!(
                socket.assigns.current_user.id,
                socket.assigns.params["id"]
              )
          )
          |> LiveFunctions.sweet_alert("You have agreed to the evaluation", "success")

        {:noreply, socket}

      {:error, _changeset} ->
        socket =
          LiveFunctions.sweet_alert(
            socket,
            "Failed to agree to the evaluation. Please try again.",
            "error"
          )

        {:noreply, socket}
    end
  end

  defp validate_function(%{"evaluation" => params} = _attrs, socket) do
    # Check if editing is allowed
    if not socket.assigns.edit_mode and socket.assigns.evaluation_complete do
      socket =
        LiveFunctions.sweet_alert(
          socket,
          "Evaluation has been submitted. Click 'Enable Edit' to make changes.",
          "warning"
        )

      {:noreply, socket}
    else
      record =
        socket.assigns.evaluations
        |> Enum.find_value(fn eval ->
          if eval.id == String.to_integer(params["id"]), do: eval, else: nil
        end)

      if record do
        changeset =
          record
          |> Evaluations.change_record_evaluations(params)
          |> Map.put(:action, :validate)

        case Evaluations.update_ratings(record, params) do
          {:ok, %{"rating" => updated_record}} ->
            # Update the socket assigns with the new data
            updated_evaluations =
              Enum.map(socket.assigns.evaluations, fn eval ->
                if eval.id == updated_record.id, do: updated_record, else: eval
              end)

            updated_scores =
              Map.put(
                socket.assigns.evaluation_scores,
                record.criteria.id,
                updated_record.rating
              )

            updated_comments =
              Map.put(
                socket.assigns.evaluation_comments,
                record.criteria.id,
                updated_record.comments || ""
              )

            socket =
              socket
              |> assign_form(changeset)
              |> assign(:record, updated_record)
              |> assign(:evaluations, updated_evaluations)
              |> assign(:evaluation_scores, updated_scores)
              |> assign(:evaluation_comments, updated_comments)

            {:noreply, socket}

          {:error, error} ->
            socket =
              LiveFunctions.sweet_alert(
                socket,
                "Error processing rating: #{inspect(error)}",
                "error"
              )

            {:noreply, socket}
        end
      else
        IO.inspect("No record found", label: :error)
        {:noreply, socket}
      end
    end
  end

  def submit_evaluation(_params, socket) do
    # Check if editing is allowed
    if not socket.assigns.edit_mode and socket.assigns.evaluation_complete do
      socket =
        LiveFunctions.sweet_alert(
          socket,
          "Evaluation has been submitted. Click 'Enable Edit' to make changes.",
          "warning"
        )

      {:noreply, socket}
    else
      # Check if all evaluations are completed
      all_completed =
        Enum.all?(socket.assigns.evaluations, fn eval ->
          score = eval.rating
          comment = eval.comments || ""

          score && String.trim(comment) != ""
        end)

      if all_completed do
        # Check if any updates failed
        case Evaluations.submit_evaluation(
               socket.assigns.evaluations,
               socket,
               socket.assigns.general_comment
             ) do
          {:ok, _} ->
            socket =
              socket
              |> assign(:evaluation_complete, true)
              |> assign(:edit_mode, false)
              |> LiveFunctions.sweet_alert("Evaluation submitted successfully!", "success")

            {:noreply, socket}

          {:error, _changeset} ->
            socket =
              LiveFunctions.sweet_alert(
                socket,
                "Failed to submit evaluation. Please try again.",
                "error"
              )

            {:noreply, socket}
        end
      else
        socket =
          LiveFunctions.sweet_alert(
            socket,
            "Please complete all sections before submitting",
            "warning"
          )

        {:noreply, socket}
      end
    end
  end

  defp enable_edit_mode(socket) do
    # If currently in summary, go back to last section for editing
    last_section = List.last(socket.assigns.sections)

    socket =
      socket
      |> assign(:edit_mode, true)
      |> assign(:step, :evaluation)
      |> assign(:current_section, last_section)
      |> LiveFunctions.sweet_alert(
        "Edit mode enabled. You can now modify your evaluation.",
        "info"
      )

    {:noreply, socket}
  end

  defp disable_edit_mode(socket) do
    socket =
      socket
      |> assign(:edit_mode, false)
      |> LiveFunctions.sweet_alert("Edit mode disabled.", "info")

    {:noreply, socket}
  end

  defp show_summary(socket) do
    # Check if all evaluations are completed before showing summary
    all_completed =
      Enum.all?(socket.assigns.evaluations, fn eval ->
        score = eval.rating
        comment = eval.comments || ""

        score && String.trim(comment) != ""
      end)

    if all_completed do
      socket = assign(socket, :step, :summary)
      {:noreply, socket}
    else
      socket =
        LiveFunctions.sweet_alert(
          socket,
          "Please complete all sections before viewing summary",
          "warning"
        )

      {:noreply, socket}
    end
  end

  defp back_to_evaluation(socket) do
    last_section = List.last(socket.assigns.sections)

    socket =
      socket
      |> assign(:step, :evaluation)
      |> assign(:current_section, last_section)

    {:noreply, socket}
  end

  defp update_general_comment(comment, socket) do
    socket = assign(socket, :general_comment, comment)
    {:noreply, socket}
  end

  defp get_section_title(evaluations, section) do
    evaluations
    |> Enum.find_value(fn eval ->
      if eval.criteria.section == section, do: eval.criteria.type, else: nil
    end)
  end

  defp get_current_section_evaluations(evaluations_by_section, current_section) do
    Map.get(evaluations_by_section, current_section, [])
  end

  defp section_progress(sections, current_section) do
    current_index =
      Enum.find_index(sections, &(&1 == current_section))

    total_sections = length(sections)

    if total_sections > 0 do
      ((current_index + 1) / total_sections * 100) |> round()
    else
      0
    end
  end

  defp is_last_section?(sections, current_section) do
    List.last(sections) == current_section
  end

  defp is_first_section?(sections, current_section) do
    List.first(sections) == current_section
  end
end

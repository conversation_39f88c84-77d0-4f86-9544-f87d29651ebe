defmodule AppWeb.Auth.UploadEvaluatorsLive.Index do
  use AppWeb, :live_view
  alias App.Licenses

  alias App.{
    Service.Functions.EvaluatorsUpload
  }

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("evaluators-upload", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Evaluators Upload", "Accessed Evaluators Upload Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      menu_list = [
        %{
          "position" => 1,
          "name" => "Upload a File"
        },
        %{
          "position" => 2,
          "name" => "Verify Details"
        }
      ]

      socket =
        assign(socket, maker_checker: false)
        |> assign(process: 1)
        |> assign(:form, to_form(%{"first_name" => ""}))
        |> assign(menu_list: menu_list)
        |> assign(loader: false)
        |> assign(params: params)
        |> assign(application: Licenses.get_application!(params["id"]))
        |> allow_upload(:file, accept: ~w(.csv .xlsx), max_entries: 1)
        |> assign(:attachment, nil)

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Evaluators Upload", "Accessed Evaluators Upload Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_event(target, params, socket), do: handle_event_switch(target, params, socket)

  defp handle_event_switch(target, params, socket) do
    case target do
      "proceed_file" ->
        proceed_file(socket)

      "cancel_upload" ->
        cancel_upload(socket)

      "validate" ->
        {:noreply, validate(params, socket)}

      "save" ->
        upload(params, socket)

      "view_valid_records" ->
        view_valid_records(socket)

      "view_invalid_records" ->
        view_invalid_records(socket)

      "cancel-entry" ->
        cancel_upload(params, socket)

      "close_modal" ->
        {
          :noreply,
          socket
          |> assign(:page_title, "Representatives Upload")
          |> assign(:live_action, :index)
        }
    end
  end

  defp validate(_params, socket) do
    filename = %{
      "filename" =>
        try do
          Enum.at(socket.assigns.uploads.file.entries, 0).client_name
        rescue
          _ -> ""
        end
    }

    socket
    |> assign(:filename, filename)
  end

  def cancel_upload(%{"ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, :file, ref)}
  end

  def cancel_upload(socket) do
    {
      :noreply,
      assign(socket, process: 1)
      |> assign(data: nil)
    }
  end

  def proceed_file(socket) do
    send(self(), :process_file_entries)

    {
      :noreply,
      assign(socket, loader: true)
    }
  end

  def upload(params, socket) do
    path = LiveFunctions.priv_path("/uploads/evaluators")

    files =
      consume_uploaded_entries(socket, :file, fn meta, entry ->
        file_name = "/#{Path.rootname(entry.client_name)}.#{LiveFunctions.ext(entry)}"
        dest = Path.join(path, file_name)

        if File.exists?(path <> "/") do
          File.cp(meta.path, dest)
          {:ok, dest}
        else
          File.mkdir_p(path <> "/")
          File.cp_r(meta.path, dest)
          {:ok, dest}
        end
      end)

    send(self(), {:extract_file, Enum.at(files, 0), params})

    {
      :noreply,
      assign(socket, process: 0)
      |> assign(loading_status: "Extracting Data")
      |> assign(attachment: List.first(files))
    }
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {:extract_file, file, params} ->
        extract_file_file(file, params, socket)

      :process_file_entries ->
        process_file_entries(socket)
    end
  end

  defp extract_file_file(file, params, socket) do
    EvaluatorsUpload.execute(
      socket,
      file,
      Map.put(params, "application_id", socket.assigns.application.id)
    )
    |> case do
      {:error, :start, message} ->
        LiveFunctions.sweet_alert(socket, message, "error")

        {
          :noreply,
          socket
          |> assign(process: 1)
        }

      {:ok, target, data, message} ->
        LiveFunctions.sweet_alert(socket, message, target)

        {
          :noreply,
          socket
          |> assign(proceed: target)
          |> assign(data: data)
          |> assign(process: 2)
        }
    end
  end

  def process_file_entries(socket) do
    EvaluatorsUpload.process_file(
      socket,
      socket.assigns.application,
      %{"entries" => socket.assigns.data.success_list}
    )
    |> case do
      {:ok, message, _txn} ->
        {
          :noreply,
          socket
          |> LiveFunctions.sweet_alert(message, "success")
          |> push_navigate(
            to: ~p"/license/applications/evaluators/#{socket.assigns.application.id}"
          )
        }

      {:error, message} ->
        LiveFunctions.sweet_alert(socket, message, "error")

        {
          :noreply,
          socket
          |> assign(:process, 1)
          |> assign(processing_file: false)
          |> assign(:data, nil)
        }
    end
  end

  defp view_valid_records(%{assigns: assigns} = socket) do
    {
      :noreply,
      assign(socket, :record_title, "Valid Records")
      |> assign(live_action: :view)
      |> assign(:records, assigns.data.success_list)
    }
  end

  defp view_invalid_records(%{assigns: assigns} = socket) do
    {
      :noreply,
      assign(socket, :record_title, "Invalid Records")
      |> assign(live_action: :view)
      |> assign(:records, assigns.data.fail_list)
    }
  end
end

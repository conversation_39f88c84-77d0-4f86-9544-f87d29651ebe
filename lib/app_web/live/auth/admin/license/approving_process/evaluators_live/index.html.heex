<div class="px-4 sm:px-6 lg:px-8 mt-5">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">
        <%= @application.record_name %>'s Evaluator(s)
      </h1>
    </div>
    
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <.header>
        <:actions>
          <.link navigate={"/license/applications/evaluators/upload/#{@params["id"]}"}>
            <.button>Upload</.button>
          </.link>
          
          <.link navigate={"/report/sandbox_scores/#{@params["id"]}"}>
            <.button>View Report</.button>
          </.link>
        </:actions>
      </.header>
    </div>
  </div>
  <!-- FILTERS -->
  <%= if @showFilter do %>
    <div class="border overflow-hidden animate-slide-in-from-top transition-all ease-in-out duration-500 rounded-lg bg-white my-4 flex flex-col gap-4 items-start">
      <h1 class="px-4 py-2 bg-gray-50 border-b font-medium w-full">Filter Requests</h1>
      
      <FormJ.filter_form
        for={@form}
        class="w-full px-4"
        id="filter"
        phx-change="filter_change"
        phx-submit="search"
      >
        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 mb-4">
          <FormJ.input_filter
            field={@form[:record_name]}
            type="text"
            label="Record Name"
            placeholder="Enter Record Name"
          />
          <FormJ.input_filter
            field={@form[:status]}
            type="select"
            label="Status"
            prompt="--Select Status--"
            options={[
              {"All", ""},
              {"PENDING APPROVAL", 0},
              {"DECLINED", -1}
            ]}
          />
        </div>
        
        <p class="text-gray-500 font-medium">Date Filters</p>
        
        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 pt-2">
          <FormJ.input_filter field={@form[:start_date]} type="date" label="From" />
          <FormJ.input_filter field={@form[:end_date]} type="date" label="To" />
        </div>
        
        <div class="flex gap-4 items-center justify-start mt-4 p-4 w-full border-t font-medium">
          <.button type="reset" class="cursor-pointer hover:text-brand-1 py-2">Reset</.button>
          
          <.button type="submit" class="cursor-pointer hover:text-brand-1 py-2">Search</.button>
        </div>
      </FormJ.filter_form>
    </div>
  <% end %>
  <!-- END OF FILTERS -->
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <Table.main_table id="adverts" rows={@data} params={@params} data_loader={@data_loader}>
          <:col :let={rj} label={table_link(@params, "Registration Date", :inserted_at)}>
            <%= Calendar.strftime(
              NaiveDateTime.add(rj.inserted_at, 7200, :second),
              "%d %B %Y %H:%M:%S"
            ) %>
          </:col>
          
          <:col :let={rj} label={table_link(@params, "Full Name", :evaluator)}>
            <%= rj.evaluator %>
          </:col>
          
          <:col :let={rj} label={table_link(@params, "Email", :email)}>
            <%= rj.email %>
          </:col>
          
          <:col :let={rj} label={table_link(@params, "Application Name", :application_name)}>
            <%= rj.application_name %>
          </:col>
          
          <:col :let={rj} label={table_link(@params, "Status", :status)}>
            <Table.table_evaluator_status status={rj.status} />
          </:col>
          
          <%!-- <:col :let={rj} label={table_link(@params, "Evaluation", :status)}>
            <%= if rj.status == true do %>
              <button
                type="button"
                phx-click="show_evaluated_summary"
                phx-value-evaluator-id={rj.id}
                class="ml-2 sm:ml-4 inline-flex items-center px-4 sm:px-6 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-lg transition-all duration-200 transform hover:scale-105"
              >
                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                 <span class="hidden sm:inline font-semibold">Evaluation Summary</span>
                <span class="sm:hidden">Summary</span>
              </button>
            <% else %>
              <span class="text-gray-500">N/A</span>
            <% end %>
          </:col> --%>
          <:action :let={rj}>
            <Option.bordered>
              <.link
                phx-click="remove_evaluator"
                phx-value-id={rj.id}
                class="w-full text-left text-red-500 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-gray-100"
              >
                Remove Evaluator
              </.link>
              
              <%= if rj.status == true do %>
                <.link
                  phx-click="show_evaluated_summary"
                  phx-value-evaluator-id={rj.id}
                  class="w-full text-left text-brand-10 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-gray-100"
                >
                  View Evaluation
                </.link>
                
                <.link
                  phx-click="update_status"
                  phx-value-status="false"
                  phx-value-id={rj.id}
                  class="w-full text-left text-brand-10 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-gray-100"
                >
                  Allow Edit
                </.link>
              <% end %>
            </Option.bordered>
          </:action>
        </Table.main_table>
      </div>
    </div>
  </div>
</div>

<%= if @show_evaluated_summary do %>
  <!-- Modal Backdrop -->
  <div
    class="fixed inset-0 z-50 overflow-y-auto"
    aria-labelledby="modal-title"
    role="dialog"
    aria-modal="true"
  >
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div
        class="fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity"
        phx-click="close_evaluated_summary"
      >
      </div>
      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <!-- Modal header -->
        <div class="bg-gradient-to-r from-brand-900 to-brand-800 px-6 py-4 sticky top-0 z-50">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <svg
                  class="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              
              <h3 class="text-xl font-semibold text-black" id="modal-title">
                Evaluation Progress Summary
              </h3>
            </div>
            
            <button
              type="button"
              phx-click="close_evaluated_summary"
              class="rounded-md text-black hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-white transition-colors duration-200"
              aria-label="Close modal"
            >
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
        <!-- Modal body -->
        <div class="bg-white px-6 py-6 max-h-[70vh] overflow-y-auto">
          <div class="space-y-6">
            <%= for {section, evaluations} <- @evaluations_by_section do %>
              <div class="bg-gray-50 rounded-xl border border-gray-200 overflow-hidden">
                <!-- Section header -->
                <div class="bg-white border-b border-gray-200 px-6 py-4">
                  <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                    <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-brand-100 text-brand-800 text-sm font-medium mr-3">
                      <%= section %>
                    </span>
                     <%= get_section_title(@evaluations, section) %>
                  </h4>
                </div>
                <!-- Evaluation items -->
                <div class="p-6">
                  <div class="grid gap-4">
                    <%= for evaluation <- evaluations do %>
                      <div class="bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors duration-200">
                        <div class="p-4">
                          <!-- Criteria header -->
                          <div class="flex items-start justify-between mb-3">
                            <h5 class="font-medium text-gray-900 text-base leading-tight">
                              <%= evaluation.criteria.name %>
                            </h5>
                            
                            <%= if Map.get(@evaluation_scores, evaluation.criteria.id) do %>
                              <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                  Score: <%= Map.get(@evaluation_scores, evaluation.criteria.id) %>/4
                                </span>
                                
                                <div class="flex items-center">
                                  <% score =
                                    Map.get(@evaluation_scores, evaluation.criteria.id, 0)

                                  numeric_score =
                                    cond do
                                      is_binary(score) -> String.to_integer(score)
                                      is_integer(score) -> score
                                      true -> Decimal.to_integer(score)
                                    end %>
                                  <%= for i <- 1..4 do %>
                                    <svg
                                      class={"h-4 w-4 #{if i <=numeric_score, do: "text-yellow-400" ,
                                          else: "text-gray-300" }"}
                                      fill="currentColor"
                                      viewBox="0 0 20 20"
                                    >
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                  <% end %>
                                </div>
                              </div>
                            <% else %>
                              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-600">
                                Not Rated
                              </span>
                            <% end %>
                          </div>
                          <!-- Comments section -->
                          <%= if Map.get(@evaluation_comments, evaluation.criteria.id) && Map.get(@evaluation_comments,
                              evaluation.criteria.id) !="" do %>
                            <div class="mt-3 p-3 bg-gray-50 rounded-lg border-l-4 border-blue-500">
                              <p class="text-sm text-gray-700 leading-relaxed">
                                <span class="font-medium text-gray-900">Comments:</span> <%= Map.get(
                                  @evaluation_comments,
                                  evaluation.criteria.id
                                ) %>
                              </p>
                            </div>
                          <% else %>
                            <div class="mt-3 p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
                              <p class="text-sm text-yellow-700">
                                <span class="font-medium">No comments provided yet</span>
                              </p>
                            </div>
                          <% end %>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
        <!-- Modal footer -->
        <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-600">
              <span class="font-medium">Evaluator:</span>
              <%= if assigns[:current_evaluator] do %>
                <%= @current_evaluator.evaluator %>
              <% else %>
                <%= @application.record_name %>
              <% end %>
               <br /> <span class="font-medium">Email:</span>
              <%= if assigns[:current_evaluator] do %>
                <%= @current_evaluator.email %>
              <% else %>
                <%= @application.email %>
              <% end %>
            </div>
            
            <button
              type="button"
              phx-click="close_evaluated_summary"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-brand-900 hover:bg-brand-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500 transition-colors duration-200"
            >
              Close Summary
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>

<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
<Model.confirmation_with_des
  :if={@live_action == :confirm_with_des}
  show
  id="confirmation_with_des-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>

<div class="px-4 sm:px-6 lg:px-8 py-8 bg-gray-50 min-h-screen">
  <div class="max-w-7xl mx-auto">
    <!-- Header Section -->
    <div class="sm:flex sm:items-center justify-between mb-8 bg-white rounded-xl p-6 shadow-sm">
      <div class="sm:flex-auto">
        <h1 class="text-2xl font-bold leading-7 text-gray-900">
          <%= @name.name %>
        </h1>
        <p class="mt-2 text-sm text-gray-500">Manage licenses for this form</p>
      </div>

      <div class="mt-4 sm:mt-0">
        <form id="searching" phx-change="search" class="relative">
          <div class="flex items-center">
            <input
              type="search"
              name="filter"
              placeholder="Search form..."
              class="w-64 pl-4 pr-10 py-2 border border-gray-300 rounded-full text-sm focus:ring-brand-1 focus:border-brand-1 outline-none transition duration-150"
            />
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <svg
                class="h-5 w-5 text-gray-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
          </div>
        </form>
      </div>
    </div>
    <!-- Permissions Grid -->
    <form id="update_the_permissions" phx-change="validate" phx-submit="save">
      <div class="bg-white rounded-xl shadow-sm p-6 mb-8">
        <h2 class="text-lg font-medium mb-6 pb-4 border-b border-gray-200">Settings</h2>
        <%= if @licenses_forms==[] do %>
          <div colspan="7" class="text-center">
            <%= if @filter==[] do %>
              <div class="py-12 px-4">
                <div class="text-center">
                  <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-red-100 mb-4">
                    <svg
                      class="w-6 h-6 text-red-500"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>

                  <h3 class="text-gray-700 font-medium mb-1">No data available</h3>
                </div>
              </div>
            <% else %>
              <div class="py-12 px-4">
                <div class="text-center">
                  <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-red-100 mb-4">
                    <svg
                      class="w-6 h-6 text-red-500"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>

                  <h3 class="text-gray-700 font-medium mb-1">
                    No data available for: "<%= @filter %>"
                  </h3>

                  <p class="text-sm text-gray-500">Try adjusting your search criteria</p>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <%= for form <-@licenses_forms  |> IO.inspect(label: "INSPECTING licenses_formslicenses_forms") do %>
              <div class="bg-white rounded-lg p-4 border border-gray-100 hover:shadow-md transition duration-150 ease-in-out">
                <label class="flex items-center justify-between cursor-pointer w-full">
                  <%= form.field_label %>
                  <span class="text-sm font-medium text-gray-800"></span>

                  <div class="relative inline-flex items-center">
                    <input
                      type="checkbox"
                      phx-value-id={form.id}
                      phx-click="check_id"
                      class="sr-only peer"
                      name={form.id}
                      checked={form.id in @selected_ids}
                    />

                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-100 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-10">
                    </div>
                  </div>
                </label>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
      <!-- Action Buttons -->
      <div class="flex items-center justify-end sticky bottom-0 bg-white p-4 border-t border-gray-200 rounded-b-lg shadow-md">
        <.button
          type="button"
          onclick="history.back()"
          class="px-5 py-2.5 rounded-lg text-gray-700 border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition duration-150 ease-in-out"
        >
          Back
        </.button>
        <%= if @updated_list == [] do %>
        <% else %>
          <.button
            type="submit"
            class="px-5 py-2.5 rounded-lg bg-brand-600 hover:bg-blue-700 text-white font-medium shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
            phx-disable-with="Updating..."
          >
            Save form
          </.button>
        <% end %>

        <.link
          navigate={"/license/summary/#{@license_id}"}
          class="phx-submit-loading:opacity-75 transition ease-in-out delay-150 duration-300 rounded hover:border-brand-10 bg-brand-10 hover:bg-transparent border-2 hover:text-brand-10 py-2 px-3 text-sm font-semibold leading-6 text-white active:text-white/80 px-5 py-2.5 rounded-lg bg-brand-600 hover:bg-blue-700 text-white font-medium shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
        >
          Manage Summary
        </.link>
      </div>
    </form>
  </div>
</div>

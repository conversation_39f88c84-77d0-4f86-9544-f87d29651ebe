defmodule AppWeb.Auth.ConditionTrackingLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias Logs.Audit
  alias App.Service.ServicMappingConditions.Functions
  alias App.Files.UploadedFile

  alias App.{LicenseConditions, Utilities, Licenses}

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("conditional_tracking-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Licence Conditional Tracking", "Accessed Licence Conditional Tracking Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      socket =
        assign(socket, conditions: [])
        |> assign(data_loader: true)
        |> assign(loader: true)
        |> assign(give_condition_reasons: false)
        |> assign(params: params)
        |> assign(confirmation_model: false)
        |> assign(
          query_expired_condition:
            App.Licenses.query_user_with_expired_conditions(assigns.current_user.id)
        )
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(session_id: session["live_socket_id"])
        |> assign(checked_ids: [])
        |> assign(param_list: [])
        |> assign(:show_upload_section, %{})
        |> assign(selected_count: 0)
        |> assign(upload_condition_id: nil)
        |> assign(
          :upload_form,
          to_form(Licenses.change_upload_file(%UploadedFile{}), as: "upload")
        )
        |> allow_upload(:file,
          accept: ~w(.jpg .jpeg .png .pdf .docx .doc .xlsx),
          max_entries: 1,
          max_file_size: 10_000_000
        )

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Licence Conditional Tracking", "Accessed Licence Conditional Tracking Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply,
     socket
     |> assign(:params, params)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "Licence Conditional Tracking")
  end

  @impl true
  def handle_info(
        {AppWeb.ConditionsLive.ConditionUploadComponent, {:upload_file, file, params}},
        socket
      ) do
    upload_file(socket, file, params)
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {:get_list, params} ->
        list(socket, params)

      {:upload_file, file, params} ->
        upload_file(socket, file, params)

      {AppWeb.LiveFunctions, message} ->
        send(self(), {:get_list, %{}})

        {
          :noreply,
          socket
          |> put_flash(:info, message)
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }
    end
  end

  def handle_event("toggle_upload_section", %{"id" => condition_id}, socket) do
    current_state = Map.get(socket.assigns.show_upload_section, condition_id, false)

    # Reset any existing uploads when toggling
    socket =
      if current_state do
        assign(socket, :upload_condition_id, nil)
      else
        # Cancel entries when toggle off
        socket =
          if socket.assigns.upload_condition_id &&
               socket.assigns.upload_condition_id != String.to_integer(condition_id) do
            Enum.reduce(socket.assigns.uploads.file.entries, socket, fn entry, acc ->
              cancel_upload(acc, :file, entry.ref)
            end)
          else
            socket
          end

        assign(socket, :upload_condition_id, String.to_integer(condition_id))
      end

    # make visible or not
    updated_show_upload =
      Map.put(socket.assigns.show_upload_section, condition_id, !current_state)

    {:noreply, assign(socket, :show_upload_section, updated_show_upload)}
  end

  def handle_event("cancel-entry", params, socket) do
    cancel_upload(params, socket)
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, %{assigns: assigns} = socket) do
    case target do
      "add_upload" ->
        socket =
          socket
          |> assign(:page_title, "Upload Condition")
          |> assign(:record, %UploadedFile{})
          |> assign(:params, Map.put(assigns.params, "condition_id", value["id"]))
          |> assign(:live_action, :new)

        {:noreply, socket}

      "confirm_condition" ->
        action = if value["activity"] == "decline", do: "decline", else: "confirm"

        model_call =
          cond do
            value["activity"] == "decline" -> :confirm_with_des
            true -> :confirm
          end

        {
          :noreply,
          assign(socket, :live_action, model_call)
          |> assign(
            :confirmation_model_text,
            "Are you sure you want to #{action} selected Condition(s)?"
          )
          |> assign(
            :confirmation_model_params,
            Map.merge(value, %{"action" => action, "ids" => assigns.checked_ids})
          )
          |> assign(:confirmation_model_agree, "confirm")
        }

      "confirm" ->
        attrs = Jason.decode!(value["params"])

        case Functions.index(socket, Map.put(attrs, "reason", value["reason"]), attrs["action"]) do
          {:ok, _record} ->
            success_message(socket, "Condition Status Updated Successfully")

          {:error, _changeset} ->
            error_message(socket, "Unable to update Condition Status")
        end

      "remove_condition" ->
        {
          :noreply,
          assign(socket, :live_action, :confirm)
          |> assign(
            :confirmation_model_text,
            "Are you sure you want to remove this condition: #{value["name"]}?"
          )
          |> assign(
            :confirmation_model_params,
            Map.merge(value, %{
              "action" => "remove"
            })
          )
          |> assign(:confirmation_model_agree, "condition_remove")
        }

      "condition_remove" ->
        attrs = Jason.decode!(value["params"])

        LicenseConditions.remove_condition(socket, Map.put(attrs, "reason", value["reason"]))
        |> case do
          {:ok, _record} ->
            socket
            |> assign(loader: false)
            |> success_message("Condition Removed.")

          {:error, error} ->
            socket
            |> assign(loader: false)
            |> error_message(error)
        end

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :index)
          |> assign(reset_user_form: "hide")
        }

      "validate_file" ->
        {:noreply, socket}

      "upload_file" ->
        upload(value, socket)

      "close_model" ->
        {:noreply, assign(socket, :live_action, :index)}

      "close_modal" ->
        close_modal(socket)

      "give_condition_reasons" ->
        give_condition_reasons(value, socket)

      "submit_condition_reason" ->
        submit_condition_reason(value, socket)

      "view_document" ->
        socket =
          socket
          |> assign(:page_title, "View Document")
          |> assign(:live_action, :view_document)
          |> assign(:doc_name, value["doc_name"])
          |> assign(
            :document,
            String.replace(value["path"], Path.join(:code.priv_dir(:app), "static/"), "")
          )

        {:noreply, socket}

      "toggle_box" ->
        id = String.to_integer(value["id"])

        update_list =
          case assigns.checked_ids do
            [] ->
              [%{id: id}]

            _ ->
              if id in Enum.map(assigns.param_list, & &1.id) do
                assigns.param_list
                |> Enum.reject(fn %{id: checked_id} -> checked_id == id end)
              else
                [
                  %{id: id}
                  | assigns.param_list
                ]
              end
          end

        {
          :noreply,
          assign(socket, checked_ids: Enum.map(update_list, & &1.id))
          |> assign(param_list: update_list)
          |> assign(selected_count: Enum.count(update_list))
        }

      _ ->
        {:noreply, socket}
    end
  end

  defp list(%{assigns: _assigns} = socket, params) do
    conditions = LicenseConditions.get_license_condition!(params["license_id"])

    conditions_with_levels =
      Enum.map(conditions, fn condition ->
        approval_level =
          Utilities.condition_level_by_role!(
            condition.status,
            socket.assigns.current_user.role_id
          )

        uploaded_file = LicenseConditions.get_uploaded_by_condition_map!(condition.id)

        condition
        |> Map.put(:approval_level, approval_level)
        |> Map.put(:uploaded_file, uploaded_file)
      end)

    {
      :noreply,
      assign(socket, :conditions, conditions_with_levels)
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end

  def cancel_upload(%{"ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, :file, ref)}
  end

  def upload(params, socket) do
    new_path = LiveFunctions.static_path("/uploads/conditional_approval_docs")

    file =
      consume_uploaded_entries(
        socket,
        :file,
        fn %{path: path}, entry ->
          file_name = "/#{Path.rootname(entry.client_name)}.#{LiveFunctions.ext(entry)}"
          dest = Path.join(new_path, file_name)

          if File.exists?(new_path <> "/") do
            File.cp(path, dest)
            {:ok, dest}
          else
            File.mkdir_p(new_path <> "/")
            File.cp_r(path, dest)
            {:ok, dest}
          end
        end
      )

    send(self(), {:upload_file, Enum.at(file, 0), params})

    {
      :noreply,
      socket
      |> assign(loader: true)
    }
  end

  defp upload_file(socket, file, params) do
    Licenses.upload_conditional_file(socket, file, params)
    |> case do
      {:ok, _} ->
        socket
        |> assign(loader: false)
        |> assign(:upload_condition_id, nil)
        |> success_message("File Successfully Uploaded.")

      {:error, error} ->
        socket
        |> assign(loader: false)
        |> error_message(error)
    end
  end

  defp success_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_list, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(selected_count: 0)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "success"
      )
    }
  end

  defp error_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_list, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(selected_count: 0)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "error"
      )
    }
  end

  def give_condition_reasons(value, socket) do
    {:noreply,
     assign(socket,
       give_condition_reasons: true,
       license_name: value["licence_name"],
       expiring_date:
         value["expiring_date"] |> Date.from_iso8601!() |> Calendar.strftime("%A, %B %d, %Y"),
       license_id: value["id"]
     )}
  end

  def submit_condition_reason(value, socket) do
    App.Licenses.upate_license_expired_condition(value, socket)
    |> case do
      {:ok, _} ->
        success_message(socket, "Condition reason successfully submitted.")

        {:noreply,
         socket
         |> assign(give_condition_reasons: false)}

      {:error, reason} ->
        {:noreply,
         socket |> put_flash(:error, "Failed to submit condition reason: #{inspect(reason)}")}
    end
  end

  def close_modal(socket) do
    {:noreply, assign(socket, give_condition_reasons: false)}
  end

  defp format_datetime(datetime) do
    Calendar.strftime(datetime, "%d %b %Y, %H:%M")
  end

  # Convert error atoms to readable strings
  defp error_to_string(:too_large), do: "The file is too large (max 10MB)"
  defp error_to_string(:not_accepted), do: "Unacceptable file type (only PDF, JPG, JPEG, PNG)"
  defp error_to_string(:too_many_files), do: "You can only upload one file"
  defp error_to_string(error), do: "Error: #{inspect(error)}"
end

defmodule AppWeb.ConditionsLive.ReasonComponent do
  use AppWeb, :live_component

  @impl true
  def render(assigns) do
    ~H"""
    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-8">
      <div class="flex items-start space-x-4">
        <div class="flex-1">
          <p class="text-sm text-gray-600 mb-4">
            The following reason was provided for why this condition could not be met on time:
          </p>
          
          <div class="bg-gray-50 rounded-md p-4 border-l-4 border-brand-10">
            <p class="text-gray-800 leading-relaxed"><%= @condition.comments %></p>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def update(%{condition: condition} = _assigns, socket) do
    {:ok,
     socket
     |> assign(:condition, condition)}
  end
end

<div class="min-h-screen bg-gray-50 py-6">
  <div class="container mx-auto px-4 max-w-4xl">
    <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100">
      <!-- Header with Applicant Name -->
      <%= if !Enum.empty?(@conditions) do %>
        <div class="bg-gradient-to-r from-blue-600 to-brand-2 text-white p-5">
          <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
            <div>
              <h2 class="text-xl font-semibold tracking-tight">Recommended Conditions</h2>
              
              <p class="text-blue-100 mt-2 flex items-center text-sm">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 mr-1.5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
                
                <span>
                  Applicant:
                  <span class="font-medium ml-1">
                    <%= if @current_user.user_type == "STAFF" do %>
                      <.link
                        navigate={
                          ~p"/license/applications/view/#{List.first(@conditions).application.id}"
                        }
                        class=" hover:underline"
                      >
                        <%= List.first(@conditions).application.record_name %> (Click to view summary)
                      </.link>
                    <% else %>
                      <%= List.first(@conditions).application.record_name %>
                    <% end %>
                  </span>
                </span>
              </p>
            </div>
            
            <div class="bg-white text-blue-600 font-medium rounded-full px-4 py-1 text-xs shadow-sm flex items-center">
              <span class="bg-blue-100 text-blue-700 w-6 h-6 rounded-full flex items-center justify-center mr-1.5">
                <%= length(@conditions) %>
              </span>
               <%= if length(@conditions) == 1, do: "Condition", else: "Conditions" %>
            </div>
          </div>
        </div>
      <% else %>
        <div class="bg-gradient-to-r from-blue-600 to-brand-2 text-white p-5">
          <h2 class="text-xl font-semibold tracking-tight">Recommended Conditions</h2>
        </div>
      <% end %>
      <!-- Conditions List -->
      <div>
        <%= if Enum.empty?(@conditions) do %>
          <div class="py-12 px-4 text-center">
            <div class="text-gray-200 mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-16 w-16 mx-auto"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="1"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            
            <p class="text-gray-600 text-base font-medium">
              No conditions recommended for this license
            </p>
            
            <p class="text-gray-400 mt-1 text-sm">
              Conditions will appear here once they are added
            </p>
          </div>
        <% else %>
          <div class="p-4">
            <ul class="divide-y divide-gray-100">
              <%= for condition <- @conditions  do %>
                <li class="hover:bg-blue-50 transition-colors duration-200 group">
                  <div class="p-4">
                    <div class="flex items-start">
                      <div class="mr-3 flex items-center h-full pt-1">
                        <%= if show_condition_checkbox?(condition) do %>
                          <input
                            type="checkbox"
                            class="cursor-pointer tBLcheckbox appearance-none w-4 h-4 border-2 border-brand-10 rounded-md checked:bg-brand-10 checked:border-transparent transition-colors duration-300 focus:outline-none focus:border-brand-10 focus:ring focus:ring-brand-10"
                            name="ids[]"
                            value={condition.id}
                            phx-click="toggle_box"
                            phx-value-id={condition.id}
                            checked={condition.id in @checked_ids}
                          />
                        <% else %>
                          <div class="flex items-center justify-center h-4 w-4">
                            <%= if condition.condition_met do %>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-5 w-5 text-green-600"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path
                                  fill-rule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                  clip-rule="evenodd"
                                />
                              </svg>
                            <% end %>
                            
                            <%= if condition.status == -1 do %>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-5 w-5 text-red-600"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path
                                  fill-rule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                  clip-rule="evenodd"
                                />
                              </svg>
                            <% end %>
                          </div>
                        <% end %>
                      </div>
                      
                      <div class="flex flex-col md:flex-row md:items-start justify-between gap-4 flex-grow">
                        <div class="flex-grow">
                          <div class="flex items-start">
                            <div class="bg-blue-100 rounded-full p-2 mr-3 text-blue-600 hidden sm:block">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-4 w-4"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                                />
                              </svg>
                            </div>
                            
                            <div>
                              <h3 class="text-base font-semibold text-gray-800 mb-1 group-hover:text-blue-600 transition-colors duration-200">
                                <%= condition.condition.name %>
                                <span class="text-brand-10">
                                  (<%= condition.responsibility %>)
                                </span>
                              </h3>
                              
                              <p class="text-gray-600 mb-3 text-sm">
                                <%= condition.condition.description %>
                              </p>
                              
                              <div class="flex flex-col sm:flex-row sm:items-center gap-2 text-xs text-gray-500">
                                <div class="flex items-center">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-3 w-3 mr-1.5 text-blue-500"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      stroke-linecap="round"
                                      stroke-linejoin="round"
                                      stroke-width="2"
                                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                  </svg>
                                  Added <%= format_datetime(condition.inserted_at) %>
                                </div>
                                
                                <div class="flex items-center">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-3 w-3 mr-1.5 text-blue-500"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      stroke-linecap="round"
                                      stroke-linejoin="round"
                                      stroke-width="2"
                                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                    />
                                  </svg>
                                   <%= condition.added_by.email %>
                                </div>
                                
                                <%= if condition.expiring_date && condition.expiring_date != "" do %>
                                  <div class="flex items-center">
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      class="h-3 w-3 mr-1.5 text-orange-500"
                                      fill="none"
                                      viewBox="0 0 24 24"
                                      stroke="currentColor"
                                    >
                                      <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                      />
                                    </svg>
                                    Expiry Date: <%= Date.from_iso8601!(condition.expiring_date)
                                    |> Calendar.strftime("%d %B %Y") %>
                                  </div>
                                <% end %>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div class="flex flex-wrap gap-2 mt-2 md:mt-0">
                          <%= if  !is_nil(condition.uploaded_file) do %>
                            <!-- View Details button -->
                            <.button
                              type="button"
                              phx-click="view_document"
                              phx-value-doc_name={condition.uploaded_file.filename}
                              phx-value-path={condition.uploaded_file.path}
                              class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                              title="View Document"
                            >
                              View Document
                            </.button>
                          <% else %>
                            <div class="flex items-center px-2 py-1 bg-red-100 border border-red-500 text-red-700 rounded text-xs font-medium">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-3 w-3 mr-1"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                  d="M6 18L18 6M6 6l12 12"
                                />
                              </svg>
                              Not Uploaded
                            </div>
                          <% end %>
                          <!-- Upload Condition button -->
                          <%= if condition.responsibility == "Applicant" && is_nil(condition.uploaded_file) && @current_user.user_type == "CLIENT" && condition.comments =="" && condition.expiring_date == "" do %>
                            <.button
                              type="button"
                              phx-click="toggle_upload_section"
                              phx-value-id={condition.id}
                              class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                              title={
                                if Map.get(@show_upload_section, condition.id, false),
                                  do: "Hide Upload",
                                  else: "Upload Condition"
                              }
                            >
                              Upload
                            </.button>
                          <% end %>
                          
                          <%= if @current_user.user_type == "CLIENT" && condition.expiring_date && condition.comments == nil && is_condition_expired?(condition)  do %>
                            <.button
                              type="button"
                              phx-click="give_condition_reasons"
                              phx-value-id={condition.id}
                              phx-value-expiring_date={condition.expiring_date}
                              class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                            >
                              Provide Reason
                            </.button>
                          <% end %>
                          
                          <%= if condition.comments not in [nil, ""] do %>
                            <.button
                              type="button"
                              phx-click="view_reason"
                              phx-value-id={condition.id}
                              class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                            >
                              View Reason
                            </.button>
                          <% end %>
                          <!-- Upload Condition button -->
                          <%= if condition.responsibility == "Commission" && is_nil(condition.uploaded_file) && @current_user.user_type == "STAFF" && condition.approval_level do %>
                            <.button
                              type="button"
                              phx-click="toggle_upload_section"
                              phx-value-id={condition.id}
                              class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                              title={
                                if Map.get(@show_upload_section, condition.id, false),
                                  do: "Hide Upload",
                                  else: "Upload Condition"
                              }
                            >
                              Upload
                            </.button>
                          <% end %>
                          
                          <%= if condition.condition_met do %>
                            <div class="flex items-center px-2 py-1 bg-green-100 border border-green-500 text-green-700 rounded text-xs font-medium">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-3 w-3 mr-1"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                  d="M5 13l4 4L19 7"
                                />
                              </svg>
                              Condition Met
                            </div>
                          <% end %>
                          
                          <%= if condition.status == -1 do %>
                            <div class="flex items-center px-2 py-1 bg-red-100 border border-red-500 text-red-700 rounded text-xs font-medium">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-3 w-3 mr-1"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                  d="M6 18L18 6M6 6l12 12"
                                />
                              </svg>
                              Condition Declined
                            </div>
                          <% end %>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- Upload Section -->
                  <div class={
                    if Map.get(@show_upload_section, "#{condition.id}", false) &&
                         @upload_condition_id == condition.id,
                       do: "",
                       else: "hidden"
                  }>
                    <div class="px-4 pb-4 pt-1 animate-fade-in">
                      <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">
                          Upload Document for Condition
                        </h4>
                        
                        <.simple_form
                          for={@upload_form}
                          id={"upload-form-#{condition.id}"}
                          phx-change="validate_file"
                          phx-submit="upload_file"
                          class="p-4"
                        >
                          <div class="flex flex-col space-y-3">
                            <div class="flex items-center justify-center w-full">
                              <label
                                phx-drop-target={@uploads.file.ref}
                                class="w-full border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
                              >
                                <%= if @uploads.file.entries == [] do %>
                                  <div class="flex flex-col items-center justify-center pt-3 pb-4">
                                    <svg
                                      aria-hidden="true"
                                      class="w-8 h-8 mb-2 text-brand-10"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                      />
                                    </svg>
                                    
                                    <p class="mb-1 text-xs text-gray-500">
                                      <span class="font-semibold text-brand-10">
                                        Click to upload
                                      </span>
                                      or drag and drop
                                    </p>
                                    
                                    <p class="text-xs text-gray-500">
                                      PDF or Image (MAX. 10MB)
                                    </p>
                                  </div>
                                <% else %>
                                  <div class="p-3">
                                    <%= for entry <- @uploads.file.entries do %>
                                      <div class="mt-4 bg-white rounded-lg border border-gray-200 p-4">
                                        <div class="flex items-center justify-between">
                                          <div class="flex items-center space-x-2">
                                            <div class="flex-shrink-0">
                                              <%= if String.ends_with?(entry.client_name, [".jpg", ".jpeg", ".png"]) do %>
                                                <svg
                                                  class="w-8 h-8 text-brand-10"
                                                  fill="none"
                                                  stroke="currentColor"
                                                  viewBox="0 0 24 24"
                                                  xmlns="http://www.w3.org/2000/svg"
                                                >
                                                  <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                                                  />
                                                </svg>
                                              <% else %>
                                                <svg
                                                  class="w-8 h-8 text-red-600"
                                                  fill="none"
                                                  stroke="currentColor"
                                                  viewBox="0 0 24 24"
                                                  xmlns="http://www.w3.org/2000/svg"
                                                >
                                                  <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                                                  />
                                                </svg>
                                              <% end %>
                                            </div>
                                            
                                            <div class="min-w-0 flex-1">
                                              <p class="text-sm font-medium text-gray-900 truncate">
                                                <%= String.slice(entry.client_name, 0, 60) <>
                                                  if String.length(entry.client_name) > 60,
                                                    do: "...",
                                                    else: "" %>
                                              </p>
                                              
                                              <p class="text-xs text-gray-500">
                                                <%= NumberF.comma_separated(
                                                  div(entry.client_size, 1000),
                                                  0
                                                ) %> KB
                                              </p>
                                            </div>
                                          </div>
                                          
                                          <div class="flex items-center space-x-2">
                                            <%= if entry.progress < 100 do %>
                                              <div class="flex items-center">
                                                <div class="w-16 bg-gray-200 rounded-full h-2.5 mr-2">
                                                  <div
                                                    class="bg-brand-10 h-2.5 rounded-full"
                                                    style={"width: #{entry.progress}%"}
                                                  />
                                                </div>
                                                
                                                <span class="text-xs text-gray-500">
                                                  <%= entry.progress %>%
                                                </span>
                                              </div>
                                            <% end %>
                                            
                                            <button
                                              type="button"
                                              phx-click="cancel-entry"
                                              phx-value-ref={entry.ref}
                                              class="inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                            >
                                              <svg
                                                class="h-4 w-4"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                                aria-hidden="true"
                                              >
                                                <path
                                                  stroke-linecap="round"
                                                  stroke-linejoin="round"
                                                  stroke-width="2"
                                                  d="M6 18L18 6M6 6l12 12"
                                                />
                                              </svg>
                                            </button>
                                          </div>
                                        </div>
                                        
                                        <%= for err <- upload_errors(@uploads.file, entry) do %>
                                          <div class="mt-2 text-sm text-red-600">
                                            <%= error_to_string(err) %>
                                          </div>
                                        <% end %>
                                      </div>
                                    <% end %>
                                  </div>
                                <% end %>
                                 <.live_file_input upload={@uploads.file} class="hidden" />
                              </label>
                            </div>
                            
                            <div class="flex flex-col sm:flex-row gap-3">
                              <.input type="hidden" name="condition_id" value={condition.id} />
                              <div class="flex gap-2">
                                <.button
                                  type="button"
                                  phx-click="toggle_upload_section"
                                  phx-value-id={condition.id}
                                  class="px-4 py-2 bg-red-500 text-gray-700 rounded-md text-sm hover:bg-white transition-colors"
                                >
                                  Cancel
                                </.button>
                                
                                <%= if @uploads.file.entries != [] do %>
                                  <.button
                                    type="submit"
                                    class="px-4 py-2 bg-brand-10 text-white rounded-md text-sm hover:bg-blue-700 transition-colors"
                                  >
                                    Upload
                                  </.button>
                                <% end %>
                              </div>
                            </div>
                          </div>
                        </.simple_form>
                      </div>
                    </div>
                  </div>
                </li>
              <% end %>
            </ul>
            <!-- Action Buttons at the Bottom -->
            <%= if @current_user.user_type == "STAFF" do %>
              <div class="p-4 border-t border-gray-100 bg-gray-50 flex justify-between items-center">
                <div class="text-sm text-gray-500">
                  <span id="selected-count">
                    <%= if @selected_count != nil do %>
                      <%= NumberF.comma_separated(@selected_count, 0) %>
                    <% else %>
                      0
                    <% end %>
                  </span>
                  conditions selected
                </div>
                
                <%= if @selected_count > 0 do %>
                  <div class="flex space-x-3">
                    <.button
                      phx-click="confirm_condition"
                      phx-value-activity="decline"
                      class="flex items-center px-4 py-2 bg-red-500 text-white hover:bg-white rounded text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-200 cursor-pointer"
                      id="decline-button"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4 mr-1.5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                      Decline Selected
                    </.button>
                    
                    <.button
                      phx-click="confirm_condition"
                      phx-value-activity="confirm"
                      class="flex items-center px-4 py-2 bg-green-500 text-white hover:bg-white rounded text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-300 cursor-pointer"
                      id="approve-button"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4 mr-1.5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      Approve Selected
                    </.button>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<%= if @give_condition_reasons do %>
  <modal>
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true">
    </div>
    
    <div class="fixed inset-0 z-10 overflow-y-auto">
      <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
          <div class="absolute right-0 top-0 pr-4 pt-4"></div>
          
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-yellow-400 sm:mx-0 sm:h-10 sm:w-10">
              <svg
                class="h-6 w-6 text-white"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 9v2m0 4h.01M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"
                />
              </svg>
            </div>
            
            <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
              <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-title">
                This condition expired on <%= @expiring_date %>
              </h3>
              
              <div class="mt-2">
                <h3 class="text-sm text-gray-500">
                  Please explain why you did not meet the required conditions for your application.
                </h3>
              </div>
            </div>
          </div>
          
          <form phx-submit="submit_condition_reason" class="mt-6 space-y-4">
            <input type="hidden" name="license_id" value={@license_id} />
            <div>
              <label for="condition_comments" class="block text-sm font-medium text-gray-700">
                Provide Reasons
              </label>
               <textarea
                id="condition_comments"
                required
                name="condition_comments"
                rows="3"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-yellow-400 focus:ring-yellow-400 sm:text-sm"
                placeholder="Please provide reasons for not meeting this condition..."
              ></textarea>
            </div>
            
            <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                class="inline-flex w-full justify-center rounded-md bg-brand-10 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-brand-20 sm:ml-3 sm:w-auto"
              >
                Submit
              </button>
              
              <button
                type="button"
                phx-click="close_modal"
                class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </modal>
<% end %>

<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
<Model.confirmation_with_des
  :if={@live_action == :confirm_with_des}
  show
  id="confirmation_with_des-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
<Model.small
  :if={@live_action in [:new]}
  id="user-modal"
  show
  return_to="close_model"
  title={@page_title}
>
  <.live_component
    module={AppWeb.ConditionsLive.ConditionUploadComponent}
    id={@record.id || :new}
    title={@page_title}
    action={@live_action}
    record={@record}
    params={@params}
    license_id={@license_id}
    expiring_date={@expiring_date}
    browser_info={@browser_info}
    current_user={@current_user}
  />
</Model.small>

<Model.fullscreen
  :if={@live_action in [:view_document]}
  id="view_document-modal"
  title={@doc_name}
  show
  return_to="close_model"
>
  <iframe
    src={@document}
    id="document_iframe"
    title="document"
    style="width: 100%;"
    height="700"
    name="DOCUMENT"
  >
  </iframe>
</Model.fullscreen>

<Model.small
  :if={@live_action in [:view_reason]}
  id="user-modal"
  show
  return_to="close_model"
  title={@page_title}
>
  <.live_component
    module={AppWeb.ConditionsLive.ReasonComponent}
    id={@condition.id || :view_reason}
    title={@page_title}
    action={@live_action}
    condition={@condition}
    params={@params}
    browser_info={@browser_info}
    current_user={@current_user}
  />
</Model.small>

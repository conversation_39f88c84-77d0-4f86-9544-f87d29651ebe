<div class="px-4 sm:px-6 lg:px-8 mt-5">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">
        Application Conditions
      </h1>
    </div>

    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <.header>
        <:actions>
          <.button phx-click="add_record">New Condition</.button>
        </:actions>
      </.header>
    </div>
  </div>
  <!-- FILTERS -->
  <%= if @showFilter do %>
    <div class="border overflow-hidden animate-slide-in-from-top transition-all ease-in-out duration-500 rounded-lg bg-white my-4 flex flex-col gap-4 items-start">
      <h1 class="px-4 py-2 bg-gray-50 border-b font-medium w-full">Filter Conditions</h1>

      <FormJ.filter_form
        for={@form}
        class="w-full px-4"
        id="filter"
        phx-change="filter_change"
        phx-submit="search"
      >
        <p class="text-gray-500 font-medium">Date Filters</p>

        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 pt-2">
          <FormJ.input_filter field={@form[:start_date]} type="date" label="From" />
          <FormJ.input_filter field={@form[:end_date]} type="date" label="To" />
        </div>

        <div class="flex gap-4 items-center justify-start mt-4 p-4 w-full border-t font-medium">
          <.button type="reset" class="cursor-pointer hover:text-brand-1 py-2">Reset</.button>

          <.button type="submit" class="cursor-pointer hover:text-brand-1 py-2">Search</.button>
        </div>
      </FormJ.filter_form>
    </div>
  <% end %>
  <!-- END OF FILTERS -->
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <Table.main_table id="adverts" rows={@data} params={@params} data_loader={@data_loader}>
          <:col :let={rj} label={table_link(@params, "Created At", :inserted_at)}>
            <%= Calendar.strftime(
              NaiveDateTime.add(rj.inserted_at, 7200, :second),
              "%d %B %Y %H:%M:%S"
            ) %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Name", :name)}>
            <%= rj.name %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Type", :type)}>
            <%= rj.type %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Description", :description)}>
            <%= rj.description %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Status", :status)}>
            <Table.table_numeric_status status={rj.status} />
          </:col>

          <:action :let={rj}>
            <Option.bordered>
              <%= if rj.status == 1 do %>
                <.link
                  phx-click="edit"
                  phx-value-id={rj.id}
                  class="w-full text-left text-brand-10 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-gray-100"
                >
                  Edit
                </.link>

                <.link
                  phx-click="update_status"
                  phx-value-status="0"
                  phx-value-id={rj.id}
                  class="w-full text-left text-red-500 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-gray-100"
                >
                  Disable
                </.link>
              <% end %>

              <%= if rj.status == 0 do %>
                <.link
                  phx-click="update_status"
                  phx-value-status="1"
                  phx-value-id={rj.id}
                  class="w-full text-left text-green-500 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-gray-900"
                >
                  Enable
                </.link>
              <% end %>
            </Option.bordered>
          </:action>
        </Table.main_table>
      </div>
    </div>
  </div>
</div>

<Model.small :if={@live_action in [:filter]} id="transaction-modal" show return_to="close_model">
  <.live_component
    module={AppWeb.Component.DateFilter}
    id={:filter}
    title={@page_title}
    @click.outside="open = false"
    params={@params}
  />
</Model.small>

<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
<Model.small
  :if={@live_action in [:new, :edit]}
  id="user-modal"
  show
  return_to="close_model"
  title={@page_title}
>
  <.live_component
    module={AppWeb.ConditionsLive.FormComponent}
    id={@record.id || :new}
    title={@page_title}
    action={@live_action}
    record={@record}
    browser_info={@browser_info}
    current_user={@current_user}
  />
</Model.small>

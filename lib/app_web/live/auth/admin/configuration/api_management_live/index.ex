defmodule AppWeb.Maintenance.ApiManagementLive.Index do
  @moduledoc false
  use AppWeb, :live_view

  alias App.{
    Management,
    Management.ApiManagement
  }

  alias App.Service.Table.ApiManagement, as: TableQuery

  alias App.{
    Service.APIManagementLive.Functions
  }

  @impl true
  def mount(_params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("api_maintenance_view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"API Management", "Accessed API Management Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      changeset = ApiManagement.changeset(%ApiManagement{}, %{})

      {
        :ok,
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(confirmation_model: false)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(:maker_checker, false)
        |> assign(edit_form: true)
        |> assign(selected_item: nil)
        |> assign(selected_info: nil)
        |> assign(:changeset, changeset)
        |> assign(display_message: false)
        |> assign(display_message_text: nil)
        |> assign(live_socket_id: session["live_socket_id"])
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()
        |> LivePageControl.maker_checker_status()
      }
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"API Management", "Accessed API Management Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_params(params, url, socket) do
    socket.endpoint.broadcast_from!(
      self(),
      "nav:" <> socket.assigns.live_socket_id,
      "change_nav",
      %{uri_path: URI.parse(url).path}
    )

    send(self(), {:get_list, params})
    socket = assign(socket, :params, params)

    {
      :noreply,
      LivePageControl.order_by_composer(socket, params)
      |> apply_action(socket.assigns.live_action, params)
    }
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "API Management")
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, %{assigns: assigns} = socket) do
    case target do
      "view_entry" ->
        view_entry(value, socket)

      "export" ->
        LiveFunctions.export_records(socket, value, "api_logs", assigns.form.params)

      "continue" ->
        if assigns.maker_checker_status do
          form = [%{type: "textarea", label: "Remarks", name: "remarks", value: ""}]

          {:noreply,
           assign(socket, :maker_checker, true)
           |> assign(:form, form)
           |> assign(:no_click, false)
           |> assign(:form_title, "User Description")
           |> assign(:info_message, "Are you sure you?")
           |> assign(
             :info_modal_param,
             Map.merge(value["api_management"], %{"action" => "continue"})
           )
           |> assign(:info_wording, "Continue")}
        else
          {:noreply,
           assign(socket, :confirmation_model, true)
           |> assign(:confirmation_model_text, "You want to save you changes")
           |> assign(
             :confirmation_model_params,
             Map.merge(value["api_management"], %{"action" => "continue"})
           )
           |> assign(:confirmation_model_agree, "update_record")}
        end

      "edit" ->
        edit(socket)

      "close_confirmation_model" ->
        {:noreply, assign(socket, :confirmation_model, false)}

      "update_record" ->
        params = Jason.decode!(value["params"])
        if connected?(socket), do: send(self(), {:update_record, params})
        {:noreply, assign(socket, :confirmation_model_icon, "loading")}

      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      _ ->
        {:noreply, socket}
    end
  end

  defp view_entry(value, socket) do
    data =
      Jason.decode!(value["data"])
      |> Enum.with_index()
      |> Enum.map(fn {{k, v}, idx} ->
        %{head: k, data: v, id: "#{idx}"}
      end)

    Task.start(fn ->
      Audit.system_log_live(
        socket,
        "View agreement",
        "VIEW",
        value,
        "Agreements Maintenance",
        socket.assigns.current_user.id
      )
    end)

    socket =
      assign(socket, :selected_item, value["id"])
      |> assign(:selected_item_name, value["name"])
      |> assign(:selected_info, data)
      |> assign(:live_action, :show)
      |> assign(:status_checker, value["status"])
      |> assign(edit_form: true)

    {:noreply, socket}
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {:save, params} ->
        save(socket, params)

      {:update_record, params} ->
        save(socket, params)

      {Roles, _, %{permissions: permissions}} ->
        if Audit.page_access("api_maintenance_view", permissions) do
          {:noreply, socket}
        else
          {:noreply,
           push_navigate(put_flash(socket, :error, "You are not allowed to access this page"),
             to: ~p"/dashboard"
           )}
        end
    end
  end

  defp save(%{assigns: assigns} = socket, api_management) do
    user = assigns.current_user

    new_params = %{
      "base_url" => api_management["base_url"],
      "key" => api_management["key"],
      "remarks" => api_management["remarks"],
      "updated_by" => "#{user.email} - #{user.id}",
      "data" =>
        Map.delete(api_management, "base_url")
        |> Map.delete("key")
        |> Map.delete("updated_by")
        |> Map.delete("action")
    }

    Management.get_api_management!(assigns.selected_item)
    |> Functions.update_api_management(socket, new_params)
    |> case do
      {:ok, message, _maker_checker} ->
        Process.send_after(self(), :get_list, 1000)

        {:noreply,
         assign(socket, selected_info: nil)
         |> assign(edit_form: true)
         |> assign(:confirmation_model, false)
         |> assign(:confirmation_model_icon, "exclamation_circle")
         |> assign(:live_action, :done)
         |> assign(display_message: true)
         |> assign(selected_item: nil)
         |> assign(:maker_checker, false)
         |> assign(display_message_text: message)}

      {:error, message} ->
        {:noreply, put_flash(socket, :error, message)}
    end
  end

  defp list(socket, params) do
    {
      :noreply,
      assign(socket, :data, TableQuery.index(LivePageControl.create_table_params(socket, params)))
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end

  defp edit(%{assigns: assigns} = socket) do
    socket =
      if Audit.page_access("api_maintenance_modify", assigns.permissions) do
        agreements = Management.get_api_management!(assigns.selected_item)

        Task.start(fn ->
          Audit.system_log_live(
            socket,
            "Enabled edit #{agreements.name} field",
            "Access",
            %{id: assigns.selected_item},
            "API Maintenance",
            socket.assigns.current_user.id
          )
        end)

        assign(socket, edit_form: false)
        |> assign(agreements: agreements)
      else
        Task.start(fn ->
          Audit.system_log_live(
            socket,
            "Enable API edit field denied",
            "Access Denied",
            %{id: assigns.selected_item},
            "API Maintenance",
            socket.assigns.current_user.id
          )
        end)

        put_flash(socket, :error, "You do not have access to perform action")
      end

    {:noreply, socket}
  end
end

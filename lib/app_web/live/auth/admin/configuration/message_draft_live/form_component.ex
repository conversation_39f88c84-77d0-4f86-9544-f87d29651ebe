defmodule AppWeb.MessageDraftLive.FormComponent do
  use AppWeb, :live_component

  alias App.MessagesDrafts

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Use this form to manage message_draft records in your database.</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="message_draft-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <.input field={@form[:message_draft]} type="text" label="Message draft" />
        <.input field={@form[:service_type]} type="text" label="Service type" />
        <:actions>
          <.button phx-disable-with="Saving...">Save Message draft</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{message_draft: message_draft} = assigns, socket) do
    changeset = MessagesDrafts.change_message_draft(message_draft)

    {:ok,
     socket
     |> assign(assigns)
     |> assign_form(changeset)}
  end

  @impl true
  def handle_event("validate", %{"message_draft" => message_draft_params}, socket) do
    changeset =
      socket.assigns.message_draft
      |> MessagesDrafts.change_message_draft(message_draft_params)
      |> Map.put(:action, :validate)

    {:noreply, assign_form(socket, changeset)}
  end

  def handle_event("save", %{"message_draft" => message_draft_params}, socket) do
    save_message_draft(socket, socket.assigns.action, message_draft_params)
  end

  defp save_message_draft(socket, :edit, message_draft_params) do
    case MessagesDrafts.update_message_draft(socket.assigns.message_drafts, message_draft_params) do
      {:ok, message_draft} ->
        notify_parent({:saved, message_draft, "Message draft updated successfully"})

        {:noreply, socket}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}
    end
  end

  defp save_message_draft(socket, :new, message_draft_params) do
    case MessagesDrafts.create_message_draft(message_draft_params) do
      {:ok, message_draft} ->
        notify_parent({:saved, message_draft, "Message draft created successfully"})

        {:noreply, socket}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}
    end
  end

  defp notify_parent(msg), do: send(self(), {__MODULE__, msg})
end

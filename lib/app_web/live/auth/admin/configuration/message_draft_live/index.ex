defmodule AppWeb.MessageDraftLive.Index do
  use AppWeb, :live_view

  alias Notification.MessageDrafts, as: MDraft
  alias App.Services.Table.Messages.MessageDraftService

  @impl true
  def mount(_params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("message_draft_view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Message Drafts Configuration", "Accessed Message Drafts Configuration Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      changeset = MDraft.change_message_new(%{})

      socket =
        assign(socket, data: [])
        |> assign_form(changeset)
        |> assign(data_loader: true)
        |> assign(maker_checker: false)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(edit_form: true)
        |> assign(info_wording: "Yes")
        |> assign(info_modal_param: "")
        |> assign(selected_item: nil)
        |> assign(selected_info: nil)
        |> assign(:changeset, changeset)
        |> assign(display_message: false)
        |> assign(live_socket_id: session["live_socket_id"])
        |> assign(display_message_text: nil)
        |> assign(live_socket_id: session["live_socket_id"])
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Message Drafts Configuration", "Accessed Message Drafts Configuration Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok, redirect(socket, to: ~p"/dashboard")}
    end
  end

  @impl true
  def handle_params(params, url, socket) do
    socket.endpoint.broadcast_from!(
      self(),
      "nav:" <> socket.assigns.live_socket_id,
      "change_nav",
      %{uri_path: URI.parse(url).path}
    )

    if connected?(socket), do: send(self(), {:get_list, params})

    {
      :noreply,
      socket
      |> assign(:params, params)
      |> apply_action(socket.assigns.live_action, params)
    }
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Message Drafts")
  end

  @impl true
  def handle_info(socket, data) do
    handle_info_switch(socket, data)
  end

  defp handle_info_switch(data, socket) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "asc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {NrfaWeb.Component.DateFilter, {:filtered, data}} ->
        send(self(), {:get_list, %{"filter" => data}})

        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> apply_action(:index, data)
        }

      {:update_record, _params} ->
        save(socket)

      {_record, _info} ->
        send(self(), {:get_list, socket.assigns.params})

        {
          :noreply,
          assign(socket, :live_action, :index)
          |> apply_action(:index, %{})
        }
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  defp handle_event_switch(target, value, socket) do
    case target do
      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "validate_input" ->
        validate(socket, value)

      "update_rich_text" ->
        validate(socket, value)

      "edit" ->
        edit(socket)

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :done)
        }

      "continue" ->
        {
          :noreply,
          assign(socket, :live_action, :confirm)
          |> assign(:confirmation_model_text, "You want to save you changes")
          |> assign(:confirmation_model_params, value["message_drafts"])
          |> assign(:confirmation_model_agree, "update_record")
        }

      "view_entry" ->
        changeset =
          MDraft.change_message_new(%{"message" => value["data"] || ""})
          |> Map.put(:action, :validate)

        socket =
          assign(socket, :selected_item, value["id"])
          |> assign(:selected_item_name, value["name"])
          |> assign(:selected_info, %{
            head: :text,
            data: value["data"],
            id: "#{1}",
            full: true
          })
          |> assign(:live_action, :show)
          |> assign(:changeset, changeset)
          |> assign(edit_form: true)
          |> assign(status_checker: value["status"])
          |> push_event("rich_text_status", %{id: "editor", status: false})
          |> push_event("rich_text_data", %{id: "editor", value: value["data"]})

        {:noreply, socket}

      "filter" ->
        socket =
          socket
          |> assign(:page_title, "Filter details")
          |> assign(:live_action, :filter)

        {:noreply, socket}

      "export" ->
        LiveFunctions.export_records(socket, value, "message_draft", socket.assigns.params)

      "update_record" ->
        IO.inspect(value, label: "update_record")
        params = Jason.decode!(value["params"])
        if connected?(socket), do: send(self(), {:update_record, params})
        {:noreply, assign(socket, :confirmation_model_icon, "loading")}

      "show_modal" ->
        socket =
          socket
          |> assign(:page_title, "Show Message")
          |> assign(:live_action, :show)
          |> assign(:message_draft, MDraft.get_message!(value["id"]))

        {:noreply, socket}

      "edit_message" ->
        socket =
          socket
          |> assign(:page_title, "Edit Message")
          |> assign(:live_action, :edit)
          |> assign(:message_draft, MDraft.get_message!(value["id"]))

        {:noreply, socket}

      _ ->
        {:noreply, socket}
    end
  end

  defp list(socket, params) do
    {
      :noreply,
      assign(
        socket,
        :data,
        MessageDraftService.index(LivePageControl.create_table_params(socket, params))
      )
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end

  defp edit(%{assigns: assigns} = socket) do
    socket =
      if Audit.page_access("message_draft_modify", assigns.permissions) do
        draft = MDraft.get_message!(assigns.selected_item)

        Task.start(fn ->
          Audit.system_log_live(
            socket,
            "Enabled edit #{draft.service_type} field",
            "Access",
            %{id: assigns.selected_item},
            "Message Drafts Configuration",
            socket.assigns.current_user.id
          )
        end)

        assign(socket, edit_form: false)
        |> assign(draft: draft)
        |> push_event("rich_text_status", %{id: "editor", status: true})
      else
        Task.start(fn ->
          Audit.system_log_live(
            socket,
            "Enable message drafts edit field denied",
            "Access Denied",
            %{id: assigns.selected_item},
            "Message Draft Configuration",
            socket.assigns.current_user.id
          )
        end)

        LiveFunctions.sweet_alert(socket, "You do not have access to perform action", "error")
        socket
      end

    {:noreply, socket}
  end

  defp validate(socket, %{"value" => draft} = _value) do
    changeset =
      socket.assigns.draft
      |> MDraft.change_message(%{"message" => draft})
      |> Map.put(:action, :validate)

    {
      :noreply,
      assign(socket, :changeset, changeset)
    }
  end

  defp save(%{assigns: assigns} = socket) do
    record = assigns.draft

    record
    |> MDraft.update_message(socket.assigns.changeset.changes, socket)
    |> case do
      {:ok, %{message: message}} ->
        Process.send_after(self(), :get_list, 1000)

        LiveFunctions.sweet_alert(
          socket,
          "#{message.service_type} was successfully updated",
          "success"
        )

        {:noreply,
         assign(socket, selected_info: nil)
         |> assign(edit_form: true)
         |> assign(:confirmation_model, false)
         |> assign(:confirmation_model_icon, "exclamation_circle")
         |> assign(:live_action, :done)
         |> assign(display_message: true)
         |> assign(selected_item: nil)
         |> assign(:maker_checker, false)
         |> assign(display_message_text: "#{message.service_type} was successfully updated")}

      {:error, error} ->
        LiveFunctions.sweet_alert(socket, error, "error")
        {:noreply, socket}
    end
  end
end

defmodule AppWeb.ErrorLive.FormComponent do
  use AppWeb, :live_component

  alias App.Utilities

  alias App.Service.ServiceError.Functions
  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.simple_form
        for={@form}
        id="city-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <.input
          field={@form[:code]}
          type="text"
          placeholder="Enter Code Error"
          required
          label={raw(~c"Error Code <span class='text-rose-500'>*</span>")}
        />
        <.input
          field={@form[:error_desc]}
          type="text"
          placeholder="Enter Error Description"
          required
          label={raw(~c"Error Description <span class='text-rose-500'>*</span>")}
        />
        <:actions>
          <div class="align-left">
            <.button type="button" phx-click="close_model">Close</.button>

            <%= if @form.source.valid? do %>
              <.button type="submit" phx-disable-with="submitting...">Submit</.button>
            <% end %>
          </div>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{record: record} = assigns, socket) do
    changeset = Utilities.change_error_code(record)

    socket
    |> assign(assigns)
    |> assign_form(changeset)
    |> ok()
  end

  @impl true
  def handle_event("validate", %{"error_code" => params} = _attrs, %{assigns: assigns} = socket) do
    changeset =
      assigns.record
      |> Utilities.change_error_code(params)
      |> Map.put(:action, :validate)

    socket
    |> assign_form(changeset)
    |> noreply()
  end

  def handle_event("save", %{"error_code" => user_params}, %{assigns: assigns} = socket) do
    save_user(socket, assigns.action, user_params)
    |> noreply()
  end

  defp save_user(socket, :new, user_params) do
    case Functions.create(socket, user_params) do
      {:ok, user} ->
        notify_parent({:saved, user, "Error Created Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}
    end
  end

  defp save_user(socket, :edit, user_params) do
    case Functions.update(socket, socket.assigns.record, user_params) do
      {:ok, user} ->
        notify_parent({:saved, user, "Error Updated Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}
    end
  end

  defp notify_parent(msg), do: send(self(), {__MODULE__, msg})
end

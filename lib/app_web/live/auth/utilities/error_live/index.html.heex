<div class="px-4 sm:px-6 lg:px-8 mt-5">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">Messaging Errors</h1>
    </div>

    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <.header>
        <:actions>
          <.button phx-click="add_error">Add Error</.button>
        </:actions>
      </.header>
    </div>
  </div>
  <!-- FILTERS -->
  <%= if @showFilter do %>
    <div class="border overflow-hidden animate-slide-in-from-top transition-all ease-in-out duration-500 rounded-lg bg-white my-4 flex flex-col gap-4 items-start">
      <h1 class="px-4 py-2 bg-gray-50 border-b font-medium w-full">Filter Errors</h1>

      <FormJ.filter_form
        for={@form}
        class="w-full px-4"
        id="filter"
        phx-change="filter_change"
        phx-submit="search"
      >
        <p class="text-gray-500 font-medium">Date Filters</p>

        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-2 pt-2">
          <FormJ.input_filter field={@form[:start_date]} type="date" label="From" />
          <FormJ.input_filter field={@form[:end_date]} type="date" label="To" />
        </div>

        <div class="flex gap-4 items-center justify-start mt-4 p-4 w-full border-t font-medium">
          <.button
            type="button"
            phx-click="reset_filter"
            class="cursor-pointer hover:text-brand-1 py-2"
          >
            Reset
          </.button>

          <.button type="submit" class="cursor-pointer hover:text-brand-1 py-2">Search</.button>
        </div>
      </FormJ.filter_form>
    </div>
  <% end %>
  <!-- END OF FILTERS -->
  <div class="w-full mt-8 flow-root">
    <Table.main_table id="adverts" rows={@data} params={@params} data_loader={@data_loader}>
      <:col :let={rj} label={table_link(@params, "Error Code", :code)}>
        <%= rj.code %>
      </:col>
      <:col :let={rj} label={table_link(@params, "Error Description", :error_desc)}>
        <%= rj.error_desc %>
      </:col>

      <:col :let={rj} label={table_link(@params, "Created By", :created_by)}>
        <%= rj.created_by %>
      </:col>

      <:col :let={rj} label={table_link(@params, "Created at", :inserted_at)}>
        <%= rj.inserted_at %>
      </:col>

      <:col :let={rj} label={table_link(@params, "Last Modified", :updated_at)}>
        <%= rj.updated_at %>
      </:col>

      <:action :let={rj}>
        <Option.bordered>
          <.link
            phx-click="edit"
            phx-value-id={rj.id}
            class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-1 hover:text-gray-900"
          >
            Edit
          </.link>

          <.link
            phx-click="update_status"
            phx-value-status="D"
            phx-value-id={rj.id}
            class="w-full text-left text-rose-700 block px-4 py-2 text-sm hover:bg-brand-1 hover:text-rose-900"
          >
            Delete
          </.link>
        </Option.bordered>
      </:action>
    </Table.main_table>
  </div>
</div>

<Model.small :if={@live_action in [:filter]} id="transaction-modal" show return_to="close_model">
  <.live_component
    module={AppWeb.Component.DateFilter}
    id={:filter}
    title={@page_title}
    @click.outside="open = false"
    params={@params}
  />
</Model.small>

<Model.small
  :if={@live_action in [:new, :edit]}
  id="user-modal"
  show
  return_to="close_model"
  title={@page_title}
>
  <.live_component
    module={AppWeb.ErrorLive.FormComponent}
    id={@record.id || :new}
    title={@page_title}
    action={@live_action}
    record={@record}
    browser_info={@browser_info}
    current_user={@current_user}
  />
</Model.small>

<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>

defmodule AppWeb.Auth.Reports.TransactionReportLive.Index do
  use AppWeb, :live_view

  alias Logs.Audit
  alias App.Service.Table.Transaction.ServiceTransactions, as: TableQuery
  alias App.{Transactions, Accounts, Clients}

  @impl true
  def mount(_params, session, %{assigns: assigns} = socket) do
    if Audit.page_access(
         "transactions-view",
         assigns.permissions
       ) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Transactions Report", "Accessed Transactions Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      filter_data = %{
        "client_id" => nil,
        "client_name" => nil,
        "reference" => nil,
        "status" => nil,
        "start_date" => nil,
        "end_date" => nil
      }

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(maker_checker: false)
        |> assign(live_socket_id: session["live_socket_id"])
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()
        |> assign(:get_client, Accounts.get_clients!())
        |> assign(showFilter: false)
        |> assign(form: useform(filter_data))

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Transactions Report", "Accessed Transactions Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  defp useform(data) do
    to_form(
      data,
      as: "filter"
    )
  end

  @impl true
  def handle_params(params, url, socket) do
    socket.endpoint.broadcast_from!(
      self(),
      "nav:" <> socket.assigns.live_socket_id,
      "change_nav",
      %{uri_path: URI.parse(url).path}
    )

    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply,
     socket
     |> assign(:params, params)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Transactions")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"batch_id" => "", "sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {AppWeb.Component.DateFilter, {:filtered, data}} ->
        send(self(), {:get_list, %{"filter" => data}})

        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> assign(:page_title, "Listing Transactions")
        }

      {_record, _info} ->
        send(self(), {:get_list, socket.assigns.params})

        {
          :noreply,
          assign(socket, :live_action, :index)
          |> apply_action(:index, %{})
        }
    end
  end

  @impl true
  def handle_event("live_select_change", %{"text" => text, "id" => live_select_id}, socket) do
    clients = Clients.search(text)

    options =
      if Enum.empty?(clients) do
        [{"", ""}]
      else
        clients
      end

    send_update(AppWeb.Component, id: live_select_id, options: options, disabled_values: [""])

    {:noreply, socket}
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, socket) do
    case target do
      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "filter" ->
        value = if socket.assigns.showFilter == true, do: false, else: true

        assign(socket, :showFilter, value)
        |> noreply()

      "filter_change" ->
        assign(socket, form: useform(value["filter"]))
        |> noreply()

      "reset_filter" ->
        socket
        |> assign(form: useform(%{}))
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        noreply(
          socket
          |> assign(checked_ids: [])
          |> assign(param_list: [])
        )

      "export" ->
        LiveFunctions.export_records(socket, value, "transactions", socket.assigns.params)

      "close_model" ->
        socket =
          socket
          |> assign(:new_edit_modal, false)
          |> assign(:view_modal, false)
          |> assign(:live_action, :index)
          |> apply_action(:index, value)

        {:noreply, socket}

      "view_receipt" ->
        socket =
          socket
          |> assign(:page_title, "View Receipt")
          |> assign(:live_action, :view_receipt)
          |> assign(:reference, value["reference"])

        {:noreply, socket}

      "download_receipt" ->
        download_receipt(value, socket)

      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()
    end
  end

  def download_receipt(%{"id" => id}, socket) do
    transaction = Transactions.get_transactions_online_by_id(id)

    case generate_receipt_pdf(%{transaction: transaction, client: transaction.client}) do
      {:ok, pdf_content} ->
        filename = "receipt_#{transaction.reference}.pdf"

        {:noreply,
         socket
         |> LiveFunctions.sweet_alert("Generating PDF...", "info")
         |> push_event("download_file", %{
           content: Base.encode64(pdf_content),
           filename: filename,
           content_type: "application/pdf"
         })}

      {:error, reason} ->
        {:noreply,
         LiveFunctions.sweet_alert(socket, "Failed to generate PDF: #{reason}", "error")}
    end
  end

  defp list(socket, params) do
    {
      :noreply,
      assign(
        socket,
        :data,
        TableQuery.index(LivePageControl.create_table_params(socket, params), socket.assigns)
      )
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end

  defp generate_receipt_pdf(assigns) do
    html =
      assigns
      |> TransactionReceiptComponent.index()
      |> Phoenix.HTML.Safe.to_iodata()
      |> IO.iodata_to_binary()

    PdfGenerator.generate_binary(html, page_size: "A4")
  end
end

defmodule AppWeb.Auth.Reports.SandboxScoresLive.Index do
  use AppWeb, :live_view

  alias Logs.Audit
  alias App.Evaluations
  alias AppWeb.Live.Components.Reports.SipocEvaluationTable

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access(
         "settings-view",
         assigns.permissions
       ) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Sandbox Scores Report", "Accessed Sandbox Scores Report Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      filter_data = %{
        "application_name" => nil,
        "evaluator_email" => nil,
        "status" => nil,
        "start_date" => nil,
        "end_date" => nil
      }

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(maker_checker: false)
        |> assign(params: params)
        |> assign(live_socket_id: session["live_socket_id"])
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()
        |> assign(showFilter: false)
        |> assign(form: useform(filter_data))
        # Can be "detailed" or "summary"
        |> assign(view_mode: "detailed")
        # Initialize empty data for template
        |> assign(evaluators: [])
        |> assign(sections: [])
        |> assign(sections_with_scores: [])
        |> assign(evaluator_grand_totals: [])
        |> assign(criterion_comments: %{})
        |> assign(overall_comment: "")

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Sandbox Scores Report", "Accessed Sandbox Scores Report Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  defp useform(data) do
    to_form(
      data,
      as: "filter"
    )
  end

  @impl true
  def handle_params(params, url, socket) do
    socket.endpoint.broadcast_from!(
      self(),
      "nav:" <> socket.assigns.live_socket_id,
      "change_nav",
      %{uri_path: URI.parse(url).path}
    )

    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply,
     socket
     |> assign(:params, params)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Sandbox Scores Report")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "inserted_at"})

      {:get_list, params} ->
        list(socket, params)

      {AppWeb.Component.DateFilter, {:filtered, data}} ->
        list(socket, data)

      _ ->
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event(event, params, socket), do: handle_event_switch(socket, event, params)

  defp handle_event_switch(socket, event, params) do
    case event do
      "search" ->
        list(socket, params)

      "filter_change" ->
        {:noreply, assign(socket, form: useform(params["filter"]))}

      "showFilter" ->
        {:noreply, assign(socket, showFilter: !socket.assigns.showFilter)}

      "change_view_mode" ->
        {:noreply, assign(socket, view_mode: params["mode"])}

      "export_records" ->
        export_records(socket)

      "update_criterion_comment" ->
        handle_criterion_comment_update(socket, params)

      "save_criterion_comment" ->
        handle_criterion_comment_save(socket, params)

      "update_overall_comment" ->
        handle_overall_comment_update(socket, params)

      _ ->
        {:noreply, socket}
    end
  end

  defp list(socket, params) do
    case socket.assigns.view_mode do
      "summary" ->
        data = Evaluations.list_sandbox_scores_grouped_by_application(socket.assigns.params["id"])

        socket =
          socket
          |> assign(data: data)
          |> assign(data_loader: false)

        {:noreply, socket}

      "detailed" ->
        # Use the new function to get evaluations with criteria
        data = Evaluations.list_sandbox_scores_with_criteria(socket.assigns.params["id"])

        # Apply filters if they exist
        filtered_data = apply_filters_to_evaluations(data, params)

        # Load evaluators and sections for the template
        evaluators = load_evaluators_from_evaluations(filtered_data)
        sections = load_sections_with_criteria()

        # Group data by sections with evaluator scores
        sections_with_scores = build_sections_with_evaluator_scores(sections, evaluators)

        # Calculate grand totals across all sections for each evaluator
        evaluator_grand_totals = calculate_evaluator_grand_totals(sections_with_scores)

        socket =
          socket
          |> assign(data: filtered_data)
          |> assign(evaluators: evaluators)
          |> assign(sections: sections)
          |> assign(sections_with_scores: sections_with_scores)
          |> assign(evaluator_grand_totals: evaluator_grand_totals)
          |> assign(criterion_comments: load_criterion_comments(sections))
          |> assign(overall_comment: load_overall_comment())
          |> assign(data_loader: false)

        {:noreply, socket}
    end
  end

  defp apply_filters_to_evaluations(data, params) do
    data
    |> filter_evaluations_by_application_name(params["application_name"])
    |> filter_evaluations_by_evaluator_email(params["evaluator_email"])
    |> filter_evaluations_by_date_range(params["start_date"], params["end_date"])
  end

  defp filter_evaluations_by_application_name(data, nil), do: data
  defp filter_evaluations_by_application_name(data, ""), do: data

  defp filter_evaluations_by_application_name(data, application_name) do
    Enum.filter(data, fn evaluation ->
      String.contains?(
        String.downcase(evaluation.application_name),
        String.downcase(application_name)
      )
    end)
  end

  defp filter_evaluations_by_evaluator_email(data, nil), do: data
  defp filter_evaluations_by_evaluator_email(data, ""), do: data

  defp filter_evaluations_by_evaluator_email(data, evaluator_email) do
    Enum.filter(data, fn evaluation ->
      String.contains?(
        String.downcase(evaluation.evaluator_email),
        String.downcase(evaluator_email)
      )
    end)
  end

  defp filter_evaluations_by_date_range(data, nil, nil), do: data
  defp filter_evaluations_by_date_range(data, "", ""), do: data

  defp filter_evaluations_by_date_range(data, start_date, end_date) do
    Enum.filter(data, fn evaluation ->
      evaluation_date = NaiveDateTime.to_date(evaluation.inserted_at)

      start_valid =
        if start_date && start_date != "" do
          case Date.from_iso8601(start_date) do
            {:ok, start} -> Date.compare(evaluation_date, start) != :lt
            _ -> true
          end
        else
          true
        end

      end_valid =
        if end_date && end_date != "" do
          case Date.from_iso8601(end_date) do
            {:ok, end_d} -> Date.compare(evaluation_date, end_d) != :gt
            _ -> true
          end
        else
          true
        end

      start_valid && end_valid
    end)
  end

  defp export_records(socket) do
    # TODO: Implement export functionality
    socket =
      LiveFunctions.sweet_alert(socket, "Export functionality coming soon!", "info")

    {:noreply, socket}
  end

  # Comment handling functions
  defp handle_criterion_comment_update(socket, params) do
    criterion_id = params["criterion"]
    comment = params["comment"] || ""

    updated_comments = Map.put(socket.assigns.criterion_comments, criterion_id, comment)

    {:noreply, assign(socket, criterion_comments: updated_comments)}
  end

  defp handle_criterion_comment_save(socket, params) do
    criterion_id = params["criterion"]
    comment = params["comment"] || ""

    # Here you would typically save to database
    # For now, we'll just update the assign and show a success message
    updated_comments = Map.put(socket.assigns.criterion_comments, criterion_id, comment)

    socket =
      socket
      |> assign(criterion_comments: updated_comments)
      |> LiveFunctions.sweet_alert("Comment saved successfully!", "success")

    {:noreply, socket}
  end

  defp handle_overall_comment_update(socket, params) do
    overall_comment = params["overall_comment"] || ""

    socket =
      socket
      |> assign(overall_comment: overall_comment)

    {:noreply, socket}
  end

  # Helper functions for loading data
  defp load_evaluators_from_evaluations([]), do: []

  defp load_evaluators_from_evaluations(evaluations) do
    evaluations
    |> Enum.group_by(& &1.evaluator_email)
    |> Enum.map(fn {email, eval_list} ->
      first_eval = List.first(eval_list)

      # Calculate total score
      total =
        eval_list
        |> Enum.map(&(&1.rating || Decimal.new(0)))
        |> Enum.reduce(Decimal.new(0), &Decimal.add/2)

      # Calculate average percentage (rating as percentage)
      total_percentage =
        eval_list
        |> Enum.map(&(&1.rating || Decimal.new(0)))
        |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
        |> then(fn sum ->
          zero = Decimal.new(0)

          if Decimal.compare(sum, zero) == :gt do
            Decimal.div(sum, length(eval_list))
          else
            zero
          end
        end)

      %{
        email: email,
        name: first_eval.evaluator_name || email,
        evaluations: eval_list,
        total_score: total,
        total_percentage: total_percentage
      }
    end)
  end

  defp load_sections_with_criteria do
    criteria = Evaluations.list_active_criteria()

    criteria
    |> Enum.group_by(& &1.section)
    |> Enum.map(fn {section_num, criteria_list} ->
      # Get the section name (type) from the first criteria in the group
      # since all criteria in the same section should have the same type
      section_name =
        case List.first(criteria_list) do
          nil -> "Section #{section_num}"
          criteria -> criteria.type || "Section #{section_num}"
        end

      %{
        name: section_name,
        number: section_num,
        criteria:
          Enum.map(criteria_list, fn c ->
            %{label: c.name, id: c.id}
          end)
      }
    end)
    |> Enum.sort_by(& &1.number)
  end

  # Helper functions for the template
  defp get_score(evaluator, criterion_label) do
    evaluator.evaluations
    |> Enum.find(fn evaluation ->
      evaluation.criteria_name == criterion_label
    end)
    |> case do
      nil -> nil
      evaluation -> format_score(evaluation.rating)
    end
  end

  defp total_score(evaluator) do
    format_score(evaluator.total_score)
  end

  # Helper functions for the template
  defp format_percentage(nil), do: "0%"

  defp format_percentage(percentage) when is_binary(percentage) do
    case Decimal.parse(percentage) do
      {decimal, _} -> "#{Decimal.round(decimal, 0)}%"
      _ -> "0%"
    end
  end

  defp format_percentage(percentage) do
    "#{Decimal.round(percentage, 0)}%"
  end

  defp format_score(nil), do: "0"

  defp format_score(score) when is_binary(score) do
    case Decimal.parse(score) do
      {decimal, _} -> Decimal.to_string(Decimal.round(decimal, 0))
      _ -> "0"
    end
  end

  defp format_score(score) do
    Decimal.to_string(Decimal.round(score, 0))
  end

  # New helper functions for section-based scoring
  defp build_sections_with_evaluator_scores(sections, evaluators)
       when sections == [] or evaluators == [] do
    []
  end

  defp build_sections_with_evaluator_scores(sections, evaluators) do
    sections
    |> Enum.map(fn section ->
      # Calculate scores for each evaluator in this section
      evaluator_scores =
        evaluators
        |> Enum.map(fn evaluator ->
          section_scores = get_section_scores_for_evaluator(evaluator, section)

          total_score =
            section_scores
            |> Enum.map(& &1.score)
            |> Enum.reduce(Decimal.new(0), &Decimal.add/2)

          # max score is 4 per criterion
          max_possible_score = Decimal.new(length(section.criteria) * 4)

          percentage =
            if Decimal.compare(max_possible_score, Decimal.new(0)) == :gt do
              Decimal.div(Decimal.mult(total_score, Decimal.new(100)), max_possible_score)
            else
              Decimal.new(0)
            end

          %{
            evaluator: evaluator,
            criteria_scores: section_scores,
            section_total: total_score,
            section_max: max_possible_score,
            section_percentage: percentage
          }
        end)

      # Calculate grand totals for this section across all evaluators
      grand_total =
        evaluator_scores
        |> Enum.map(& &1.section_total)
        |> Enum.reduce(Decimal.new(0), &Decimal.add/2)

      grand_max =
        evaluator_scores
        |> Enum.map(& &1.section_max)
        |> Enum.reduce(Decimal.new(0), &Decimal.add/2)

      grand_percentage =
        if Decimal.compare(grand_max, Decimal.new(0)) == :gt do
          Decimal.div(Decimal.mult(grand_total, Decimal.new(100)), grand_max)
        else
          Decimal.new(0)
        end

      %{
        section: section,
        evaluator_scores: evaluator_scores,
        grand_total: grand_total,
        grand_max: grand_max,
        grand_percentage: grand_percentage
      }
    end)
  end

  defp get_section_scores_for_evaluator(nil, _section), do: []
  defp get_section_scores_for_evaluator(_evaluator, %{criteria: []}), do: []

  defp get_section_scores_for_evaluator(evaluator, section) do
    section.criteria
    |> Enum.map(fn criterion ->
      score = get_evaluator_score_for_criterion(evaluator, criterion.label)

      %{
        criterion: criterion,
        score: score || Decimal.new(0)
      }
    end)
  end

  defp get_evaluator_score_for_criterion(nil, _criterion_label), do: Decimal.new(0)
  defp get_evaluator_score_for_criterion(%{evaluations: []}, _criterion_label), do: Decimal.new(0)

  defp get_evaluator_score_for_criterion(%{evaluations: nil}, _criterion_label),
    do: Decimal.new(0)

  defp get_evaluator_score_for_criterion(evaluator, criterion_label) do
    evaluator.evaluations
    |> Enum.find(fn evaluation ->
      evaluation.criteria_name == criterion_label
    end)
    |> case do
      nil -> Decimal.new(0)
      evaluation -> evaluation.rating || Decimal.new(0)
    end
  end

  # Helper functions for calculating overall totals across all sections
  defp calculate_evaluator_grand_totals(sections_with_scores) do
    # Get all evaluators from the first section (they should be consistent across sections)
    case List.first(sections_with_scores) do
      nil ->
        []

      first_section ->
        first_section.evaluator_scores
        |> Enum.map(fn evaluator_score ->
          evaluator = evaluator_score.evaluator

          # Calculate totals across all sections for this evaluator
          {total_score, total_max} =
            sections_with_scores
            |> Enum.reduce({Decimal.new(0), Decimal.new(0)}, fn section, {acc_score, acc_max} ->
              evaluator_section =
                Enum.find(section.evaluator_scores, fn es ->
                  es.evaluator.email == evaluator.email
                end)

              case evaluator_section do
                nil ->
                  {acc_score, acc_max}

                es ->
                  {Decimal.add(acc_score, es.section_total), Decimal.add(acc_max, es.section_max)}
              end
            end)

          total_percentage =
            if Decimal.compare(total_max, Decimal.new(0)) == :gt do
              Decimal.div(Decimal.mult(total_score, Decimal.new(100)), total_max)
            else
              Decimal.new(0)
            end

          %{
            evaluator: evaluator,
            grand_total_score: total_score,
            grand_total_max: total_max,
            grand_total_percentage: total_percentage
          }
        end)
    end
  end

  # Additional helper functions for template access
  defp get_section_score_for_evaluator(sections_with_scores, section_number, evaluator_email) do
    sections_with_scores
    |> Enum.find(fn section_data ->
      section_data.section.number == section_number
    end)
    |> case do
      nil ->
        Decimal.new(0)

      section_data ->
        section_data.evaluator_scores
        |> Enum.find(fn evaluator_score ->
          evaluator_score.evaluator.email == evaluator_email
        end)
        |> case do
          nil -> Decimal.new(0)
          evaluator_score -> evaluator_score.section_total
        end
    end
  end

  defp get_section_percentage_for_evaluator(sections_with_scores, section_number, evaluator_email) do
    sections_with_scores
    |> Enum.find(fn section_data ->
      section_data.section.number == section_number
    end)
    |> case do
      nil ->
        Decimal.new(0)

      section_data ->
        section_data.evaluator_scores
        |> Enum.find(fn evaluator_score ->
          evaluator_score.evaluator.email == evaluator_email
        end)
        |> case do
          nil -> Decimal.new(0)
          evaluator_score -> evaluator_score.section_percentage
        end
    end
  end

  defp get_section_grand_total(sections_with_scores, section_number) do
    sections_with_scores
    |> Enum.find(fn section_data ->
      section_data.section.number == section_number
    end)
    |> case do
      nil -> Decimal.new(0)
      section_data -> section_data.grand_total
    end
  end

  defp get_section_grand_percentage(sections_with_scores, section_number) do
    sections_with_scores
    |> Enum.find(fn section_data ->
      section_data.section.number == section_number
    end)
    |> case do
      nil -> Decimal.new(0)
      section_data -> section_data.grand_percentage
    end
  end

  # Helper functions for getting criterion scores within evaluator_scores
  defp get_criterion_score_for_evaluator(evaluator_score, criterion_id) do
    case evaluator_score do
      nil ->
        Decimal.new(0)

      %{criteria_scores: criteria_scores} when is_list(criteria_scores) ->
        criteria_scores
        |> Enum.find(fn criterion_score ->
          criterion_score.criterion.id == criterion_id
        end)
        |> case do
          nil -> Decimal.new(0)
          criterion_score -> criterion_score.score || Decimal.new(0)
        end

      _ ->
        Decimal.new(0)
    end
  end

  # Helper functions for overall grand totals
  defp get_overall_grand_total(evaluator_grand_totals) do
    evaluator_grand_totals
    |> Enum.map(& &1.grand_total_score)
    |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
  end

  defp get_overall_grand_max(evaluator_grand_totals) do
    evaluator_grand_totals
    |> Enum.map(& &1.grand_total_max)
    |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
  end

  defp get_overall_grand_percentage(evaluator_grand_totals) do
    total_score = get_overall_grand_total(evaluator_grand_totals)
    total_max = get_overall_grand_max(evaluator_grand_totals)

    if Decimal.compare(total_max, Decimal.new(0)) == :gt do
      Decimal.div(Decimal.mult(total_score, Decimal.new(100)), total_max)
    else
      Decimal.new(0)
    end
  end

  # Helper functions for average calculations
  defp calculate_criterion_average(evaluator_scores, criterion_id) do
    if Enum.empty?(evaluator_scores) do
      format_score(Decimal.new(0))
    else
      scores =
        Enum.map(evaluator_scores, fn evaluator_score ->
          case Enum.find(
                 evaluator_score.criteria_scores || [],
                 &(&1.criterion.id == criterion_id)
               ) do
            nil -> Decimal.new(0)
            score -> score.score || Decimal.new(0)
          end
        end)

      scores
      |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
      |> Decimal.div(Decimal.new(length(scores)))
      |> Decimal.round(0)
      |> format_score()
    end
  end

  defp calculate_section_total_average(evaluator_scores) do
    if Enum.empty?(evaluator_scores) do
      format_score(Decimal.new(0))
    else
      total_scores =
        Enum.map(evaluator_scores, fn evaluator_score ->
          evaluator_score.section_total || Decimal.new(0)
        end)

      total_scores
      |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
      |> Decimal.div(Decimal.new(length(total_scores)))
      |> Decimal.round(0)
      |> format_score()
    end
  end

  defp calculate_section_max_average(criteria) do
    # For max scores, the average is always the same as the max

    criteria
    |> Enum.map(fn _criterion -> Decimal.new(4) end)
    |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
  end

  defp calculate_section_percentage_average(evaluator_scores, criteria) do
    if Enum.empty?(evaluator_scores) do
      "0"
    else
      percentage_scores =
        Enum.map(evaluator_scores, fn evaluator_score ->
          evaluator_score.section_percentage || Decimal.new(0)
        end)

      percentage_scores
      |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
      |> Decimal.div(Decimal.new(length(percentage_scores)))
      |> Decimal.round(0)
      |> Decimal.to_string()
    end
  end

  # Helper functions for overall grand totals averages
  defp calculate_overall_total_average(evaluator_grand_totals) do
    if Enum.empty?(evaluator_grand_totals) do
      format_score(Decimal.new(0))
    else
      total_scores =
        Enum.map(evaluator_grand_totals, fn evaluator_total ->
          evaluator_total.grand_total_score || Decimal.new(0)
        end)

      total_scores
      |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
      |> Decimal.div(Decimal.new(length(total_scores)))
      |> Decimal.round(0)
      |> format_score()
    end
  end

  defp calculate_overall_max_average(evaluator_grand_totals) do
    if Enum.empty?(evaluator_grand_totals) do
      format_score(Decimal.new(0))
    else
      max_scores =
        Enum.map(evaluator_grand_totals, fn evaluator_total ->
          evaluator_total.grand_total_max || Decimal.new(0)
        end)

      max_scores
      |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
      |> Decimal.div(Decimal.new(length(max_scores)))
      |> Decimal.round(0)
      |> format_score()
    end
  end

  defp calculate_overall_percentage_average(evaluator_grand_totals) do
    if Enum.empty?(evaluator_grand_totals) do
      "0"
    else
      percentage_scores =
        Enum.map(evaluator_grand_totals, fn evaluator_total ->
          evaluator_total.grand_total_percentage || Decimal.new(0)
        end)

      percentage_scores
      |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
      |> Decimal.div(Decimal.new(length(percentage_scores)))
      |> Decimal.round(0)
      |> Decimal.to_string()
    end
  end

  # Helper functions for comments
  defp load_criterion_comments(sections) do
    # This would typically load from database based on application_id
    # For now, return empty map - comments will be stored in assigns
    %{}
  end

  defp load_overall_comment() do
    # This would typically load from database based on application_id
    # For now, return empty string
    ""
  end
end

<div class="px-4 sm:px-6 lg:px-8 mt-8">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-xl font-semibold text-gray-900">SIPOC Evaluation Score Sheet</h1>
      
      <p class="mt-1 text-sm text-gray-600">
        Evaluator-wise breakdown of scores across SIPOC sections.
      </p>
    </div>
    
    <div class="flex gap-2">
      <.button
        type="button"
        onclick="window.history.back()"
        class="px-4 py-2 rounded-lg transition-colors text-sm"
      >
        Back
      </.button>
    </div>
  </div>
  
  <SipocEvaluationTable.sipoc_evaluation_table
    evaluators={@evaluators}
    sections_with_scores={@sections_with_scores}
    evaluator_grand_totals={@evaluator_grand_totals}
  />
</div>

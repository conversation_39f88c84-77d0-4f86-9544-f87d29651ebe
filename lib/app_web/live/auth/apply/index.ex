defmodule AppWeb.ApplyLive.Index do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})
  alias App.Licenses

  @impl true
  def mount(_params, _session, socket) do
    categories =
      Licenses.list_active_license_categories_on_dashboard([
        "BOTH",
        socket.assigns.current_user.registration_type
      ])

    {:ok, assign(socket, page_title: "Apply", categories: categories)}
  end

  @impl true
  def handle_event("view", %{"id" => category}, socket) do
    {:noreply, push_navigate(socket, to: ~p"/license/registration/#{category}")}
  end
end

defmodule AppWeb.Registration.Navigation do
  @moduledoc false
  use AppWeb, :live_component

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <h2 class="sr-only">Steps</h2>
      <div>
        <ol class="grid grid-cols-1 divide-x divide-gray-100 backdrop-blur-sm overflow-hidden rounded-lg border border-white text-sm text-white sm:grid-cols-3">
          <%= for james <- @menu do %>
            <li class={"#{ if james["position"] != @first["position"] || james["position"] != @last["position"]  do
              "relative"
              end} flex items-center justify-center gap-2 p-4 #{ if james["position"] == @current_position  do
              "bg-brand-700"
              end}"}>
              <%= raw(james["icon"] || "") %>
              <p class="leading-none">
                <strong class="block font-medium"><%= james["name"] %></strong>
                <small class="mt-1"><%= james["description"] %></small>
              </p>
            </li>
          <% end %>
        </ol>
      </div>
    </div>
    """
  end

  @impl true
  def update(%{menu_list: menu, current_position: _} = assigns, socket) do
    list = Enum.sort(menu, &(&1["position"] < &2["position"]))

    {
      :ok,
      socket
      |> assign(assigns)
      |> assign(menu: list)
      |> assign(first: List.first(list))
      |> assign(last: List.last(list))
    }
  end
end

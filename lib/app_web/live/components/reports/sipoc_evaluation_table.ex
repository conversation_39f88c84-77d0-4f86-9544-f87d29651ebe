defmodule AppWeb.Live.Components.Reports.SipocEvaluationTable do
  @moduledoc """
  A reusable component for rendering SIPOC evaluation score tables.
  Displays evaluator-wise breakdown of scores across SIPOC sections.
  """

  use Phoenix.Component

  # Import Decimal for calculations
  alias Decimal

  @doc """
  Renders a SIPOC evaluation score table.

  ## Attributes

  - `evaluators` - List of evaluators with their email and name
  - `sections_with_scores` - List of sections with their criteria and evaluator scores
  - `evaluator_grand_totals` - List of grand totals for each evaluator
  - `class` - Additional CSS classes for the table container

  ## Examples

      <.sipoc_evaluation_table
        evaluators={@evaluators}
        sections_with_scores={@sections_with_scores}
        evaluator_grand_totals={@evaluator_grand_totals}
      />

  """
  attr(:evaluators, :list, required: true)
  attr(:sections_with_scores, :list, required: true)
  attr(:evaluator_grand_totals, :list, required: true)
  attr(:class, :string, default: "")

  def sipoc_evaluation_table(assigns) do
    ~H"""
    <div class={["mt-4 overflow-auto border rounded-lg shadow", @class]}>
      <table class="min-w-full table-auto border-collapse text-sm text-gray-700">
        <thead class="bg-gray-100">
          <tr>
            <th class="border px-4 py-2 text-left font-semibold">Criteria</th>
            
            <%= for evaluator <- @evaluators do %>
              <th class="border px-4 py-2 text-center font-semibold">
                <div><%= evaluator.name %></div>
                
                <div class="text-xs text-gray-400"><%= evaluator.email %></div>
              </th>
            <% end %>
            
            <th class="border px-4 py-2 text-center font-semibold">Average</th>
          </tr>
        </thead>
        
        <tbody>
          <%= for section_data <- @sections_with_scores do %>
            <% section = section_data.section %>
            <!-- Section Header -->
            <tr class="bg-blue-50 font-semibold">
              <td colspan={length(@evaluators) + 3} class="border px-4 py-2 text-center text-blue-800">
                Section <%= section.number %>: ASSESSMENT AGAINST <%= section.name %>
              </td>
            </tr>
            <!-- Criteria Rows for this Section -->
            <%= for criterion <- section.criteria do %>
              <tr class="hover:bg-gray-50">
                <td class="border px-4 py-2"><%= criterion.label %></td>
                
                <%= for evaluator_score <- section_data.evaluator_scores do %>
                  <% criterion_score =
                    get_criterion_score_for_evaluator(evaluator_score, criterion.id) %>
                  <td class="border px-4 py-2 text-center">
                    <%= if criterion_score, do: format_score(criterion_score), else: "-" %>
                  </td>
                <% end %>
                
                <td class="border px-4 py-2 text-center bg-yellow-50 font-medium">
                  <%= calculate_criterion_average(section_data.evaluator_scores, criterion.id) %>
                </td>
              </tr>
            <% end %>
            <!-- Committee Comments Row -->
            <tr class="bg-blue-25 border-t-2 border-blue-200">
              <td class="border px-4 py-2 text-right font-medium text-blue-800">
                Member Comments
              </td>
              
              <%= for evaluator_score <- section_data.evaluator_scores do %>
                <td class="border px-4 py-2 text-center text-sm">
                  <%= if Map.get(evaluator_score, :committee_comments),
                    do: Map.get(evaluator_score, :committee_comments),
                    else: "-" %>
                </td>
              <% end %>
            </tr>
            <!-- Section Totals Row -->
            <tr class="bg-gray-100 font-semibold">
              <td class="border px-4 py-2 text-right">Section Total</td>
              
              <%= for evaluator_score <- section_data.evaluator_scores do %>
                <td class="border px-4 py-2 text-center">
                  <%= format_score(evaluator_score.section_total) %>
                </td>
              <% end %>
              
              <td class="border px-4 py-2 text-center bg-yellow-100 font-bold">
                <%= calculate_section_total_average(section_data.evaluator_scores) %>
              </td>
            </tr>
            <!-- Section Maximum Row -->
            <tr class="bg-gray-100 font-semibold">
              <td class="border px-4 py-2 text-right">Section Maximum</td>
              
              <%= for evaluator_score <- section_data.evaluator_scores do %>
                <td class="border px-4 py-2 text-center">
                  <%= format_score(evaluator_score.section_max) %>
                </td>
              <% end %>
              
              <td class="border px-4 py-2 text-center bg-yellow-100 font-bold">
                <%= if section_data.evaluator_scores != [] do %>
                  <%= format_score(List.first(section_data.evaluator_scores).section_max) %>
                <% else %>
                  <%= format_score(0) %>
                <% end %>
              </td>
            </tr>
            <!-- Section Percentage Row -->
            <tr class="bg-gray-100 font-semibold">
              <td class="border px-4 py-2 text-right">Section Percentage</td>
              
              <%= for evaluator_score <- section_data.evaluator_scores do %>
                <td class="border px-4 py-2 text-center">
                  <%= format_percentage(evaluator_score.section_percentage) %>
                </td>
              <% end %>
              
              <td class="border px-4 py-2 text-center bg-yellow-100 font-bold">
                <%= calculate_section_percentage_average(
                  section_data.evaluator_scores,
                  section.criteria
                ) %>%
              </td>
            </tr>
            <!-- Section Separator -->
            <tr>
              <td colspan={length(@evaluators) + 3} class="border-0 py-2"></td>
            </tr>
          <% end %>
          <!-- Overall Grand Totals Section -->
          <tr class="bg-green-100 font-bold">
            <td colspan={length(@evaluators) + 3} class="border px-4 py-2 text-center text-green-800">
              OVERALL TOTALS
            </td>
          </tr>
          
          <tr class="bg-green-50 font-semibold">
            <td class="border px-4 py-2 text-right">Grand Total Score</td>
            
            <%= for evaluator_total <- @evaluator_grand_totals do %>
              <td class="border px-4 py-2 text-center">
                <%= format_score(evaluator_total.grand_total_score) %>
              </td>
            <% end %>
            
            <td class="border px-4 py-2 text-center bg-yellow-100 font-bold">
              <%= calculate_overall_total_average(@evaluator_grand_totals) %>
            </td>
          </tr>
          
          <tr class="bg-green-50 font-semibold">
            <td class="border px-4 py-2 text-right">Grand Maximum</td>
            
            <%= for evaluator_total <- @evaluator_grand_totals do %>
              <td class="border px-4 py-2 text-center">
                <%= format_score(evaluator_total.grand_total_max) %>
              </td>
            <% end %>
            
            <td class="border px-4 py-2 text-center bg-yellow-100 font-bold">
              <%= calculate_overall_max_average(@evaluator_grand_totals) %>
            </td>
          </tr>
          
          <tr class="bg-green-50 font-semibold">
            <td class="border px-4 py-2 text-right">Grand Percentage</td>
            
            <%= for evaluator_total <- @evaluator_grand_totals do %>
              <td class="border px-4 py-2 text-center">
                <%= format_percentage(evaluator_total.grand_total_percentage) %>
              </td>
            <% end %>
            
            <td class="border px-4 py-2 text-center bg-yellow-100 font-bold">
              <%= calculate_overall_percentage_average(@evaluator_grand_totals) %>%
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    """
  end

  # Helper functions for formatting
  defp format_percentage(percentage) do
    "#{Decimal.round(percentage, 0)}%"
  end

  defp format_score(nil), do: "0"

  defp format_score(score) when is_binary(score) do
    case Decimal.parse(score) do
      {decimal, _} -> Decimal.to_string(Decimal.round(decimal, 0))
      _ -> "0"
    end
  end

  defp format_score(score) do
    Decimal.to_string(Decimal.round(score, 0))
  end

  # Helper functions for getting criterion scores within evaluator_scores
  defp get_criterion_score_for_evaluator(evaluator_score, criterion_id) do
    case evaluator_score do
      nil ->
        Decimal.new(0)

      %{criteria_scores: criteria_scores} when is_list(criteria_scores) ->
        criteria_scores
        |> Enum.find(fn criterion_score ->
          criterion_score.criterion.id == criterion_id
        end)
        |> case do
          nil -> Decimal.new(0)
          criterion_score -> criterion_score.score || Decimal.new(0)
        end

      _ ->
        Decimal.new(0)
    end
  end

  # Helper functions for average calculations
  defp calculate_criterion_average(evaluator_scores, criterion_id) do
    if Enum.empty?(evaluator_scores) do
      format_score(Decimal.new(0))
    else
      scores =
        Enum.map(evaluator_scores, fn evaluator_score ->
          case Enum.find(
                 evaluator_score.criteria_scores || [],
                 &(&1.criterion.id == criterion_id)
               ) do
            nil -> Decimal.new(0)
            score -> score.score || Decimal.new(0)
          end
        end)

      scores
      |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
      |> Decimal.div(Decimal.new(length(scores)))
      |> Decimal.round(0)
      |> format_score()
    end
  end

  defp calculate_section_total_average(evaluator_scores) do
    if Enum.empty?(evaluator_scores) do
      format_score(Decimal.new(0))
    else
      total_scores =
        Enum.map(evaluator_scores, fn evaluator_score ->
          evaluator_score.section_total || Decimal.new(0)
        end)

      total_scores
      |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
      |> Decimal.div(Decimal.new(length(total_scores)))
      |> Decimal.round(0)
      |> format_score()
    end
  end

  defp calculate_section_percentage_average(evaluator_scores, criteria) do
    if Enum.empty?(evaluator_scores) do
      "0"
    else
      percentage_scores =
        Enum.map(evaluator_scores, fn evaluator_score ->
          evaluator_score.section_percentage || Decimal.new(0)
        end)

      percentage_scores
      |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
      |> Decimal.div(Decimal.new(length(percentage_scores)))
      |> Decimal.round(0)
      |> Decimal.to_string()
    end
  end

  # Helper functions for overall grand totals averages
  defp calculate_overall_total_average(evaluator_grand_totals) do
    if Enum.empty?(evaluator_grand_totals) do
      format_score(Decimal.new(0))
    else
      total_scores =
        Enum.map(evaluator_grand_totals, fn evaluator_total ->
          evaluator_total.grand_total_score || Decimal.new(0)
        end)

      total_scores
      |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
      |> Decimal.div(Decimal.new(length(total_scores)))
      |> Decimal.round(0)
      |> format_score()
    end
  end

  defp calculate_overall_max_average(evaluator_grand_totals) do
    if Enum.empty?(evaluator_grand_totals) do
      format_score(Decimal.new(0))
    else
      max_scores =
        Enum.map(evaluator_grand_totals, fn evaluator_total ->
          evaluator_total.grand_total_max || Decimal.new(0)
        end)

      max_scores
      |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
      |> Decimal.div(Decimal.new(length(max_scores)))
      |> Decimal.round(0)
      |> format_score()
    end
  end

  defp calculate_overall_percentage_average(evaluator_grand_totals) do
    if Enum.empty?(evaluator_grand_totals) do
      "0"
    else
      percentage_scores =
        Enum.map(evaluator_grand_totals, fn evaluator_total ->
          evaluator_total.grand_total_percentage || Decimal.new(0)
        end)

      percentage_scores
      |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
      |> Decimal.div(Decimal.new(length(percentage_scores)))
      |> Decimal.round(0)
      |> Decimal.to_string()
    end
  end
end

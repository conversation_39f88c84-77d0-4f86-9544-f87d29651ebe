# A LiveComponent that renders content in an iframe for printing purposes.
# This component helps isolate print styles and ensures proper printing behavior
# by rendering content in a dedicated iframe context.
defmodule AppWeb.Components.IframePrint do
  use Phoenix.LiveComponent

  # Renders the component with an iframe containing the provided content
  # slot - The content to be rendered inside the iframe for printing
  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%!-- Hidden iframe that will be used for printing --%>
      <iframe
        id={@id}
        class="hidden"
        phx-hook="IframePrint"
        phx-update="ignore"
        data-print-target={@print_target}
      >
      </iframe>
      <%!-- Content slot that will be cloned into the iframe --%>
      <div id={"#{@id}-content"} class="hidden">
        <%= render_slot(@inner_block) %>
      </div>
    </div>
    """
  end
end

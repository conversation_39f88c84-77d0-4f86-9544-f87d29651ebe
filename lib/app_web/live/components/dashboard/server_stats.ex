defmodule AppWeb.Dashboard.ServerStats do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <!-- MEMORY USAGE -->
      <div>
        <h3 class="text-lg font-semibold text-gray-800">Memory Usage</h3>
        <p class="text-2xl font-bold text-brand-2 mt-2">
          <%= @stats.used_memory %> / <%= @stats.total_memory %> GB
        </p>
        <p class="text-sm text-gray-600 mt-1">Used memory vs total memory</p>
      </div>
      <!-- CPU USAGE -->
      <div>
        <h3 class="text-lg font-semibold text-gray-800">CPU Usage</h3>
        <p class="text-2xl font-bold text-teal-500 mt-2">
          <%= @stats.used_cpu_cores %> / <%= @stats.total_cpu_cores %> Cores
        </p>
        <p class="text-sm text-gray-600 mt-1">Used CPU cores vs total</p>
      </div>
      <!-- STORAGE USAGE -->
      <div>
        <h3 class="text-lg font-semibold text-gray-800">Storage Usage</h3>
        <p class="text-2xl font-bold text-yellow-500 mt-2">
          <%= @stats.total_storage %>
          <%= if @stats.used_unit != @stats.total_unit,
            do: @stats.total_unit %> / <%= @stats.used_storage %> <%= @stats.used_unit %>
        </p>
        <p class="text-sm text-gray-600 mt-1">Used storage vs total</p>
      </div>
      <!-- OPERATING SYSTEM -->
      <div>
        <h3 class="text-lg font-semibold text-gray-800">Operating System</h3>
        <p class="text-xl font-bold text-gray-500 mt-2"><%= @stats.os %></p>
        <p class="text-sm text-gray-600 mt-1">The application is running on</p>
      </div>
    </div>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket), do: :timer.send_interval(1000, self(), :update)
    stats = SystemStats.get_system_stats()
    {:ok, assign(socket, stats: stats)}
  end

  @impl true
  def handle_info(:update, socket) do
    stats = SystemStats.get_system_stats()
    {:noreply, assign(socket, stats: stats)}
  end
end

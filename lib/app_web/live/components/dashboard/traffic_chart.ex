defmodule AppWeb.Dashboard.TrafficChart do
  use AppWeb, :live_view

  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})

  def render(assigns) do
    ~H"""
    <div class="h-96" phx-update="ignore" id="TransactionByPaymentProvider">
      <div id="trafficChart" phx-hook="BarChart"></div>
    </div>
    """
  end

  def mount(_params, _session, %{assigns: assigns} = socket) do
    series =
      if assigns.client do
        Reports.Context.dashboard_chart_stats(assigns.client.id)
      else
        Reports.Context.dashboard_chart_stats()
      end

    socket = push_event(socket, "bar_chart_update", %{series: series})

    {:ok, socket}
  end
end

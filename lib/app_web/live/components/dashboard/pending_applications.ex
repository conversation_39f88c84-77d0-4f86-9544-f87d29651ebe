defmodule AppWeb.Dashboard.PendingApplications do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})

  alias App.Licenses

  def render(assigns) do
    ~H"""
    <.stats_card
      title="New Licence Applications"
      icon="hero-clipboard-document-check"
      stats_value={@total}
      stats_desc="Total of New Licence Applications"
    />
    """
  end

  def mount(_params, _session, socket) do
    {:ok, assign(socket, :total, Licenses.count_applications_by_status("new"))}
  end
end

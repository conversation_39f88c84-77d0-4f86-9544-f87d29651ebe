defmodule AppWeb.Dashboard.ViewComponent do
  use AppWeb, :live_component

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="overflow-hidden rounded-lg shadow-sm border border-gray-200">
        <!-- Header with license color and information -->
        <div
          class="p-4"
          style={"background-color: #{@details.color}; background-image: linear-gradient(to right, #{@details.color}, #{@details.color})"}
        >
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-bold text-white flex items-center">
              <.licence_icon name={@details.icon} class="w-6 h-6 mr-2 text-white opacity-90" /> <%= @details.name %>
            </h2>

            <span class="bg-white text-sm font-medium px-3 py-1 rounded-full shadow-sm">
              Form #<%= @details.form_number %>
            </span>
          </div>
        </div>
        <!-- License Details Content -->
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Left Column -->
            <div class="space-y-4">
              <!-- Section -->
              <div>
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Section</p>

                <p class="text-base font-medium text-gray-900 flex items-center">
                  <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-gray-700 mr-2 text-sm">
                    <%= @details.section %>
                  </span>
                </p>
              </div>

              <div>
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">
                  Security Act No.
                </p>

                <p class="text-base font-medium text-gray-900 flex items-center">
                  <span class="inline-flex items-center justify-center h-6 px-3 rounded-md bg-blue-50 text-blue-700 text-sm">
                    <%= @details.security_act_no %>
                  </span>
                </p>
              </div>

              <div>
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">
                  License Amount
                </p>

                <p class="text-base font-medium text-gray-900 flex items-center">
                  <span class="inline-flex items-center justify-center h-6 px-3 rounded-md bg-blue-50 text-blue-700 text-sm">
                    <%= NumberF.currency(@details.amount) %>
                  </span>
                </p>
              </div>
            </div>
            <!-- Right Column -->
            <div class="space-y-4">
              <div>
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">
                  Associated License
                </p>

                <%= if @details.associated_license do %>
                  <div class="flex items-start">
                    <div
                      class="w-2 h-2 rounded-full mt-1.5 mr-2"
                      style={"background-color: #{@details.associated_license.color}"}
                    >
                    </div>

                    <p class="text-base font-medium text-gray-900">
                      <%= @details.associated_license.name %>
                    </p>
                  </div>
                <% else %>
                  <p class="text-base text-gray-500 italic">No associated license</p>
                <% end %>
              </div>

              <div>
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">
                  Download Form
                </p>

                <p class="text-base font-medium text-gray-900 flex items-center">
                  <a
                    href={
                      String.replace(
                        @details.file_path,
                        Path.join(:code.priv_dir(:app), "static/"),
                        ""
                      )
                    }
                    download
                    class="inline-flex items-center justify-center h-8 px-4 rounded-md bg-blue-50 text-blue-700 text-sm hover:bg-blue-100 transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                      />
                    </svg>
                    Download
                  </a>
                </p>
              </div>

              <div>
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">
                  Compensation Levy
                </p>

                <p class="text-base font-medium text-gray-900 flex items-center">
                  <span class="inline-flex items-center justify-center h-6 px-3 rounded-md bg-blue-50 text-blue-700 text-sm">
                    <%= NumberF.currency(@details.other_fees) %>
                  </span>
                </p>
              </div>
            </div>
          </div>

          <div class="mt-6">
            <div class="flex items-center mb-2">
              <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Notes</p>

              <div class="ml-2 h-px bg-gray-200 flex-grow"></div>
            </div>

            <div class="p-4 bg-gray-50 rounded-lg border border-gray-100 text-sm whitespace-pre-wrap text-gray-700 leading-relaxed">
              <%= @details.note %>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-4 flex justify-end">
        <.button
          type="button"
          phx-click="close_model"
          class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Close
        </.button>
      </div>
    </div>
    """
  end

  @impl true
  def update(%{details: details} = assigns, socket) do
    {
      :ok,
      socket
      |> assign(assigns)
      |> assign(details: details)
    }
  end
end

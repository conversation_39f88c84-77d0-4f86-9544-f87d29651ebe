defmodule AppWeb.Dashboard.TotalIssueLicenses do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})

  def render(assigns) do
    ~H"""
    <.stats_card
      title="Total Issued Licences"
      icon="hero-document-check"
      stats_value={@total}
      stats_desc="Total Number of Issued Licences"
      stats_desc_class="text-gray-500 text-xs"
      stats_value_class="text-2xl font-medium text-brand-200"
      icon_class="w-8 h-8 p-1 text-white bg-brand-200 rounded-full"
    />
    """
  end

  def mount(_params, _session, socket) do
    {:ok, assign(socket, :total, App.Licenses.get_total_issued_licenses())}
  end
end

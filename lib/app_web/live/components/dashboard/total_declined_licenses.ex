defmodule AppWeb.Dashboard.TotalDeclinedLicenses do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})

  alias App.Licenses

  def render(assigns) do
    ~H"""
    <.stats_card
      title="Returned to Applicant"
      icon="hero-x-circle"
      stats_value={@total}
      stats_desc="Total Number of Applications Returned to Applicant"
    />
    """
  end

  def mount(_params, _session, socket) do
    {:ok, assign(socket, :total, Licenses.count_applications_by_status("returned"))}
  end
end

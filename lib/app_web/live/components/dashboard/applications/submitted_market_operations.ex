defmodule AppWeb.Dashboard.Admin.SubmittedMarketOperations do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})

  alias App.Service.Dashboard.Stats

  def render(assigns) do
    ~H"""
    <Card.application_card
      title="Submitted to Market Operations"
      count={@total}
      description="Applications with market operations"
      navigate_to={~p"/license/Market_Operations"}
      gradient_from="from-green-500"
      gradient_to="to-emerald-600"
    >
      <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>
    </Card.application_card>
    """
  end

  def mount(_params, _session, socket) do
    {:ok, assign(socket, :total, Stats.market_operations(socket))}
  end
end

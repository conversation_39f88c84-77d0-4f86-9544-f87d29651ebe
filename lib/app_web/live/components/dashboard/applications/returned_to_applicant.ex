defmodule AppWeb.Dashboard.Admin.ReturnedToApplicant do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})

  alias App.Service.Dashboard.Stats

  def render(assigns) do
    ~H"""
    <Card.application_card
      title="Returned to Applicant"
      count={@total}
      description="Applications returned to applicant"
      navigate_to={~p"/license/Returned_to_Applicant"}
      gradient_from="from-rose-300"
      gradient_to="to-rose-600"
    >
      <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    </Card.application_card>
    """
  end

  def mount(_params, _session, socket) do
    {:ok, assign(socket, :total, Stats.returned_to_application(socket))}
  end
end

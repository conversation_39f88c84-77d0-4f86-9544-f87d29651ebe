defmodule AppWeb.Dashboard.Admin.TotalLicenseApplications do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})

  alias App.Service.Dashboard.Stats

  def render(assigns) do
    ~H"""
    <Card.application_card
      title="Total License Applications"
      count={@total}
      description="All license applications"
      navigate_to={~p"/license/all"}
      gradient_from="from-blue-500"
      gradient_to="to-indigo-600"
    >
      <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
        <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
    </Card.application_card>
    """
  end

  def mount(_params, _session, socket) do
    {:ok, assign(socket, :total, Stats.all(socket))}
  end
end

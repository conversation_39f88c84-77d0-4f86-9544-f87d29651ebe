defmodule AppWeb.Dashboard.Sandbox do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})
  alias App.Licenses

  @impl true
  def render(assigns) do
    ~H"""
    <div
      class="p-6"
      x-data="{ show: false }"
      x-init="setTimeout(() => show = true, 100)"
      x-show="show"
      x-transition:enter="transition ease-out duration-500"
      x-transition:enter-start="opacity-0 transform -translate-y-4"
      x-transition:enter-end="opacity-100 transform translate-y-0"
    >
      <div
        class="sm:flex sm:items-center mb-10"
        x-data="{ show: false }"
        x-init="setTimeout(() => show = true, 200)"
        x-show="show"
        x-transition:enter="transition ease-out duration-500"
        x-transition:enter-start="opacity-0 transform -translate-x-4"
        x-transition:enter-end="opacity-100 transform translate-x-0"
      >
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-bold text-gray-800 mb-6">Securities Licenses & Forms</h1>
        </div>

        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none" phx-update="ignore" id="time2">
          <span phx-hook="LocalTime" id="time" class="text-lg"></span>
        </div>
      </div>

      <%= if Enum.empty?(@licenses) do %>
        <.empty_state />
      <% else %>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <%= for {license, idx} <- Enum.with_index(@licenses) do %>
            <div
              x-data="{ show: false }"
              x-init={"setTimeout(() => show = true, #{100 * idx})"}
              x-show="show"
              x-transition:enter="transition ease-out duration-500"
              x-transition:enter-start="opacity-0 transform scale-95"
              x-transition:enter-end="opacity-100 transform scale-100"
              class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
              style={"border-top: 4px solid #{license["color"]}"}
            >
              <div class="p-6">
                <div class="flex items-start justify-between">
                  <div class="mr-4">
                    <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                      Form <%= license["form_number"] %>
                    </span>

                    <h3 class="mt-2 text-lg font-medium text-gray-900"><%= license["title"] %></h3>
                  </div>

                  <div class="flex-shrink-0">
                    <div
                      class="w-10 h-10 rounded-full flex items-center justify-center"
                      style={"background-color: #{license["color"]}"}
                    >
                      <.licence_icon name={license["icon"]} class="h-5 w-5 text-white" />
                    </div>
                  </div>
                </div>

                <div class="mt-4 flex justify-between items-center">
                  <button
                    phx-click="apply"
                    phx-value-form={license["id"]}
                    class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors duration-300"
                  >
                    Apply
                  </button>

                  <button
                    phx-click="more_info"
                    phx-value-form={license["id"]}
                    class="text-blue-600 text-sm hover:underline"
                  >
                    More info
                  </button>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>

    <Model.small
      :if={@live_action in [:view]}
      id="user-modal"
      show
      return_to="close_model"
      title={@page_title}
    >
      <.live_component
        module={AppWeb.Dashboard.ViewComponent}
        id={@details.id}
        title={@page_title}
        action={@live_action}
        details={@details}
      />
    </Model.small>
    """
  end

  @impl true
  def mount(_params, session, socket) do
    category = Licenses.get_category_by_name!(session["name"])
    licenses = Licenses.list_active_licenses_on_dashboard(category.id, ["SECURITIES"])

    {:ok, assign(socket, licenses: licenses, page_title: category.name)}
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, socket) do
    case target do
      "apply" ->
        apply_license(value, socket)

      "more_info" ->
        {
          :noreply,
          assign(socket, :details, Licenses.get_license_w_preload!(value["form"]))
          |> assign(:page_title, "More Information")
          |> assign(:live_action, :view)
        }

      "close_model" ->
        socket =
          socket
          |> assign(:view_modal, false)
          |> assign(:live_action, :index)

        {:noreply, socket}

      _ ->
        {:noreply, socket}
    end
  end

  def apply_license(%{"form" => form_number}, socket) do
    # Handle apply event - you can add your logic here
    # For example, redirect to the application form
    {:noreply, push_navigate(socket, to: ~p"/license/registration/#{form_number}")}
  end
end

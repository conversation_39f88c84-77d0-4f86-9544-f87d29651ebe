defmodule AppWeb.Components.Transactions.PendingAmounts do
  use AppWeb, :live_view
  # alias App.Transaction

  @impl true
  def render(assigns) do
    ~H"""
    <div class="p-4 space-y-4 rounded-lg bg-white shadow-lg w-full h-fit">
      <span class="flex gap-4 justify-between text-brand-200 items-center font-medium">
        Total Pending Amount
        <.icon_tag name="user-group" class="w-8 h-8 p-1 text-white bg-brand-200 rounded-full" />
      </span>

      <div class="flex items-center justify-between gap-6">
        <div class="space-y-2">
          <h1 class="text-2xl font-medium text-brand-200">
            <%= NumberF.currency(@count, "ZMW ", 0) %>
          </h1>

          <p class="text-gray-500 text-xs">Total Pending Amount</p>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    # count = Transaction.total_pending_amount() || 0

    {
      :ok,
      assign(socket, :count, 0)
    }
  end
end

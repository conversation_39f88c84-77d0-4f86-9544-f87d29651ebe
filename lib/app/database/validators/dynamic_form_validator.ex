defmodule App.Validators.DynamicFormValidator do
  import Ecto.Changeset

  @moduledoc """
  Provides validation for dynamic forms with multiple field types and multi-step processes.
  Supports text, number, upload, textarea, checkbox, radio, and checkbox_group fields.
  """

  @doc """
  Creates a dynamic schema and validates a changeset based on the provided fields.

  ## Parameters

  - `params` - The map of parameters to validate
  - `fields` - A list of field definitions with field_name, field_type, etc.
  - `options` - Additional options for validation

  ## Returns

  An Ecto.Changeset for the validated data
  """
  def validate_step(params, fields, options \\ []) do
    # Extract the fields for the current step
    current_step = Keyword.get(options, :step, 0)

    step_fields = filter_fields_for_step(fields, current_step)

    # Convert fields to a map of field names to their types
    types = build_types_map(step_fields)

    # Create a changeset with dynamic schema
    {%{}, types}
    |> cast(params, Map.keys(types))
    |> validate_required(get_required_fields(step_fields))
    |> apply_custom_validations(step_fields, params)
  end

  # Get fields that belong to the current step
  defp filter_fields_for_step(fields, _step) do
    fields
    |> Enum.filter(fn field ->
      # If field has a step property, use it, otherwise default to step 0

      field_step = Map.get(field, :step, 0)

      # field_step == step
      field_step
    end)
  end

  # Build a map of field names to their Ecto types
  defp build_types_map(fields) do
    fields
    |> Enum.map(fn field ->
      type = get_ecto_type(field.field_type)
      {String.to_atom(field.field_name), type}
    end)
    |> Enum.into(%{})
  end

  # Map field types to Ecto types
  defp get_ecto_type(field_type) do
    case field_type do
      "text" -> :string
      "textarea" -> :string
      "number" -> :integer
      "upload" -> :string
      # "upload" -> {:array, :map}
      "checkbox" -> :boolean
      "radio" -> :string
      "checkbox_group" -> {:array, :string}
      _ -> :string
    end
  end

  # Get required fields
  defp get_required_fields(fields) do
    fields
    |> Enum.filter(fn field ->
      # Add your logic to determine if a field is required
      # For example, you might have a `required: true` in your field definition
      Map.get(field, :required, true)
    end)
    |> Enum.map(fn field -> String.to_atom(field.field_name) end)
  end

  # Apply custom validations based on field types
  defp apply_custom_validations(changeset, fields, params) do
    Enum.reduce(fields, changeset, fn field, acc_changeset ->
      field_name = String.to_atom(field.field_name)

      acc_changeset
      |> validate_by_type(field_name, field.field_type, field.field_options)
      |> check_dependencies(field_name, field, params)
    end)
  end

  # Apply validations based on field type
  defp validate_by_type(changeset, field_name, "number", options) do
    changeset
    |> validate_number_field(field_name, options)
  end

  defp validate_by_type(changeset, field_name, "text", options) do
    changeset
    |> validate_text_field(field_name, options)
  end

  defp validate_by_type(changeset, field_name, "textarea", options) do
    changeset
    |> validate_textarea_field(field_name, options)
  end

  defp validate_by_type(changeset, field_name, "upload", options) do
    changeset
    |> validate_upload_field(field_name, options)
  end

  defp validate_by_type(changeset, _field_name, "checkbox", _options) do
    # No special validation for checkbox
    changeset
  end

  defp validate_by_type(changeset, field_name, "radio", options) do
    # Validate that the value is one of the allowed options
    if options && Enum.any?(options) do
      changeset
      |> validate_inclusion(field_name, options)
    else
      changeset
    end
  end

  defp validate_by_type(changeset, field_name, "checkbox_group", options) do
    # Validate that all values are in the allowed options
    if options && Enum.any?(options) do
      validate_change(changeset, field_name, fn ^field_name, values ->
        if is_list(values) && Enum.all?(values, &(&1 in options)) do
          []
        else
          [{field_name, "contains invalid options"}]
        end
      end)
    else
      changeset
    end
  end

  defp validate_by_type(changeset, _field_name, _type, _options), do: changeset

  # Number-specific validations
  defp validate_number_field(changeset, field_name, options) when is_map(options) do
    changeset
    |> maybe_add_validation(field_name, Map.get(options, "min"), fn cs, name, min ->
      validate_number(cs, name, greater_than_or_equal_to: min)
    end)
    |> maybe_add_validation(field_name, Map.get(options, "max"), fn cs, name, max ->
      validate_number(cs, name, less_than_or_equal_to: max)
    end)
  end

  defp validate_number_field(changeset, _field_name, _options), do: changeset

  # Text-specific validations
  defp validate_text_field(changeset, field_name, options) when is_map(options) do
    changeset
    |> maybe_add_validation(field_name, Map.get(options, "min_length"), fn cs, name, min ->
      validate_length(cs, name, min: min)
    end)
    |> maybe_add_validation(field_name, Map.get(options, "max_length"), fn cs, name, max ->
      validate_length(cs, name, max: max)
    end)
    |> maybe_add_validation(field_name, Map.get(options, "pattern"), fn cs, name, pattern ->
      validate_format(cs, name, ~r/#{pattern}/)
    end)
  end

  defp validate_text_field(changeset, _field_name, _options), do: changeset

  # Textarea-specific validations
  defp validate_textarea_field(changeset, field_name, options) when is_map(options) do
    changeset
    |> maybe_add_validation(field_name, Map.get(options, "min_length"), fn cs, name, min ->
      validate_length(cs, name, min: min)
    end)
    |> maybe_add_validation(field_name, Map.get(options, "max_length"), fn cs, name, max ->
      validate_length(cs, name, max: max)
    end)
  end

  defp validate_textarea_field(changeset, _field_name, _options), do: changeset

  # Upload field validations
  defp validate_upload_field(changeset, _field_name, _options) do
    # Basic validation that the field contains uploads
    changeset
  end

  # Check dependent fields - fixed to handle nil values safely
  defp check_dependencies(changeset, _field_name, field, params) do
    # Check if this field has dependencies
    field_dependents = Map.get(field, :field_dependents)

    # If field has no dependencies, it's always required
    if field_dependents == nil || !is_list(field_dependents) || Enum.empty?(field_dependents) do
      changeset
    else
      dependent_selection = Map.get(field, :dependent_selection)
      # Get the first dependent field (supporting both string and atom keys)
      dependent_field_name = List.first(field_dependents)

      # Only proceed if we have a valid dependent field name
      if is_binary(dependent_field_name) do
        dependent_field = String.to_atom(dependent_field_name)

        # Get the actual value of the dependent field (support both string and atom keys)
        actual_value =
          Map.get(params, to_string(dependent_field)) ||
            Map.get(params, dependent_field)

        if actual_value == dependent_selection do
          # Field is required when dependency is met
          validate_required(changeset, [String.to_atom(field.field_name)])
        else
          # The dependent field doesn't have the activating value, so this field is optional
          changeset
        end
      else
        # If dependent_field_name is not a binary, just return the changeset as is
        changeset
      end
    end
  end

  # Helper to conditionally add a validation
  defp maybe_add_validation(changeset, _field_name, nil, _validator), do: changeset

  defp maybe_add_validation(changeset, field_name, value, validator) do
    validator.(changeset, field_name, value)
  end
end

defmodule App.Evaluation.ApplicationEvaluators do
  use Ecto.Schema
  import Ecto.Changeset

  schema "application_evaluators" do
    field :status, :boolean, default: false
    field :enable_edits, :boolean, default: true
    field :comments, :string

    belongs_to :application, App.Licenses.LicenseMapping
    belongs_to :evaluator, App.Accounts.User

    timestamps()
  end

  @doc false
  def changeset(evaluation_criterion, attrs) do
    evaluation_criterion
    |> cast(attrs, [:application_id, :evaluator_id, :status, :enable_edits, :comments])
    |> validate_required([:application_id, :evaluator_id])
  end
end

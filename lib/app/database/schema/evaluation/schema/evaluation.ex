defmodule App.Evaluation.Evaluation do
  use Ecto.Schema
  import Ecto.Changeset

  schema "evaluations" do
    field :comments, :string
    field :rating, :decimal
    field :status, :integer, default: 0

    belongs_to :criteria, App.Evaluation.EvaluationCriteria
    belongs_to :application, App.Licenses.LicenseMapping
    belongs_to :evaluator, App.Accounts.User

    timestamps()
  end

  @doc false
  def changeset(evaluation, attrs) do
    evaluation
    |> cast(attrs, [
      :comments,
      :criteria_id,
      :application_id,
      :evaluator_id,
      :rating,
      :status
    ])
    |> validate_required([:criteria_id, :application_id, :evaluator_id, :rating, :comments])
    |> validate_number(:rating, greater_than_or_equal_to: 0)
    |> foreign_key_constraint(:criteria_id)
    |> foreign_key_constraint(:application_id)
    |> foreign_key_constraint(:evaluator_id)
  end

  def insert_changeset(evaluation, attrs) do
    evaluation
    |> cast(attrs, [
      :comments,
      :criteria_id,
      :application_id,
      :evaluator_id,
      :rating,
      :status
    ])
    |> validate_required([:criteria_id, :application_id, :evaluator_id])
    |> foreign_key_constraint(:criteria_id)
    |> foreign_key_constraint(:application_id)
    |> foreign_key_constraint(:evaluator_id)
  end
end

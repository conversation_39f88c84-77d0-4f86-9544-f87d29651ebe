defmodule App.Evaluation.SandboxScore do
  use Ecto.Schema
  import Ecto.Changeset

  schema "sandbox_scores" do
    field :section_score, :decimal
    field :max_score, :decimal
    field :percentage_score, :decimal
    field :grand_total, :decimal
    field :max_grand_total, :decimal
    field :grand_percentage_score, :decimal
    field :decision, :string
    field :comments, :string
    field :status, :integer, default: 1

    belongs_to :evaluation, App.Evaluation.Evaluation
    belongs_to :application, App.Licenses.LicenseMapping

    timestamps()
  end

  @doc false
  def changeset(sandbox_score, attrs) do
    sandbox_score
    |> cast(attrs, [
      :section_score,
      :max_score,
      :percentage_score,
      :grand_total,
      :max_grand_total,
      :grand_percentage_score,
      :decision,
      :evaluation_id,
      :application_id,
      :comments,
      :status
    ])
    |> validate_required([
      :section_score,
      :max_score,
      :evaluation_id,
      :application_id
    ])
    |> validate_number(:section_score, greater_than_or_equal_to: 0)
    |> validate_number(:max_score, greater_than: 0)
    |> validate_number(:percentage_score, greater_than_or_equal_to: 0, less_than_or_equal_to: 100)
    |> validate_number(:grand_total, greater_than_or_equal_to: 0)
    |> validate_number(:max_grand_total, greater_than: 0)
    |> validate_number(:grand_percentage_score,
      greater_than_or_equal_to: 0,
      less_than_or_equal_to: 100
    )
    |> foreign_key_constraint(:evaluation_id)
    |> foreign_key_constraint(:application_id)
  end
end

defmodule App.Evaluation.EvaluationCriteria do
  use Ecto.Schema
  import Ecto.Changeset

  schema "evaluation_criteria" do
    field :name, :string
    field :type, :string
    field :section, :integer, default: 1
    field :description, :string
    field :status, :integer, default: 1

    timestamps()
  end

  @doc false
  def changeset(evaluation_criterion, attrs) do
    evaluation_criterion
    |> cast(attrs, [:name, :type, :section, :description, :status])
    |> validate_required([:name, :type])
  end
end

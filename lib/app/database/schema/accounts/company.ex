defmodule App.Accounts.Company do
  use Ecto.Schema
  import Ecto.Changeset
  alias App.Database.CustomDB

  @mail_regex ~r/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,9}$/
  # ensure that the email looks valid

  schema "companies" do
    field :address, :string
    field :email, :string
    field :name, :string
    field :incorporation_date, :date
    field :mobile, :string
    field :registration_number, :string
    field :tpin, :string
    field :status, :integer, default: 1

    belongs_to :user, App.Accounts.User

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(company, attrs, _opts \\ []) do
    company
    |> cast(attrs, [
      :name,
      :email,
      :address,
      :incorporation_date,
      :registration_number,
      :mobile,
      :tpin,
      :status,
      :user_id
    ])
    |> validate_required([
      :name,
      # :email,
      :address,
      :incorporation_date,
      :registration_number,
      # :mobile,
      :tpin
    ])
    |> validate_length(:tpin, is: 10, message: "must be 10 digits")
    # |> validate_email_and_tpin_format()
    # |> validate_email()
    # |> CustomDB.validate_mobile_number2()
    # |> validate_mobile_number?()
    |> maybe_validate_unique_tpin()
  end

  defp validate_email_and_tpin_format(changeset) do
    changeset
    |> validate_format(:email, ~r/@/, message: "must contain the @ symbol")
    |> validate_format(:email, ~r/@\w+\.\w+$/, message: "must be a valid email address")
    |> validate_format(:email, ~r/\A[^@]+@[^@]+\z$/, message: "must contain only one @ symbol")
    |> validate_format(:tpin, ~r/^\d+$/, message: "must contain only numbers")
  end

  defp validate_email(changeset) do
    changeset
    |> validate_format(:email, @mail_regex, message: "must have the @ sign and no spaces")
    |> validate_length(:email, max: 160)
    |> unsafe_validate_unique(:email, App.Repo)
    |> unique_constraint(:email)
  end

  defp maybe_validate_unique_tpin(changeset) do
    changeset
    |> unsafe_validate_unique(:tpin, App.Repo)
    |> unique_constraint(:tpin, message: "TPIN has already been taken")
  end

  defp validate_mobile_number?(changeset) do
    changeset
    |> validate_required([:mobile])
    |> validate_length(:mobile, max: 13)
  end
end

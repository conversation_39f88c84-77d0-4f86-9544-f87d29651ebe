defmodule App.Licenses.ApprovalLevels do
  use Ecto.Schema
  import Ecto.Changeset

  schema "approval_levels" do
    field :approval_status, :integer
    field :status, :integer
    field :count_down, :integer
    field :condition_tracking, :integer
    field :genarate_summary, :integer
    field :evaluation, :integer
    belongs_to :department, App.Roles.DepartmentRoles
    belongs_to(:role, App.Roles.AccessRoles)
    belongs_to :categories, App.Licenses.LicenseCategories

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(approval_levels, attrs) do
    approval_levels
    |> cast(attrs, [
      :approval_status,
      :status,
      :department_id,
      :role_id,
      :count_down,
      :condition_tracking,
      :genarate_summary,
      :categories_id,
      :evaluation
    ])
    |> validate_required([:approval_status, :department_id, :role_id])
    |> validate_number(:approval_status, greater_than: 0, message: "must be greater than 0")
    |> unsafe_validate_unique([:approval_status, :categories_id], App.Repo)
    |> unique_constraint([:approval_status, :categories_id])
    |> unique_constraint([:count_down, :categories_id])
    |> unique_constraint([:condition_tracking, :categories_id])
    |> unique_constraint([:genarate_summary, :categories_id])
  end
end

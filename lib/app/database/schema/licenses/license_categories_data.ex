defmodule App.Licenses.LicenseCategoriesData do
  use Ecto.Schema
  import Ecto.Changeset

  schema "license_categories_data" do
    field :status, :integer, default: 1
    belongs_to :license_field, App.Licenses.LicenseFields
    belongs_to :categories, App.Licenses.LicenseCategories

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(license_categories_data, attrs) do
    license_categories_data
    |> cast(attrs, [:status, :license_field_id, :categories_id])
    |> validate_required([:license_field_id, :categories_id])
  end
end

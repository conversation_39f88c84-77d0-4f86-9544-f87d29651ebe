defmodule App.Licenses.LicenseMapping do
  use Ecto.Schema
  import Ecto.Changeset

  @columns [
    :status,
    :current_step,
    :approval_status,
    :summary_user_draft,
    :approved,
    :user_id,
    :license_id,
    :data,
    :reason,
    :comments,
    :is_replaced,
    :replace_reason,
    :reprint_reason,
    :replace_comments,
    :reprint_comments,
    :to_reprint,
    :record_name,
    :initiator_id,
    :meeting_number,
    :associated_license_id,
    :categories_id,
    :count_down_days,
    :count_down_start_date,
    :account_id,
    :show_summary,
    :show_evaluators,
    :is_resubmitted,
    :condition_tracking,
    :condition_status,
    :revoked
  ]

  schema "user_license_mapping" do
    field :status, :integer, default: 0
    field :current_step, :integer, default: 1
    field :approval_status, :boolean, default: false
    field :is_replaced, :boolean, default: false
    field :to_reprint, :boolean, default: false
    field :approved, :boolean, default: false
    field :reason, :string
    field :reprint_reason, :string
    field :replace_reason, :string
    field :reprint_comments, :string
    field :replace_comments, :string
    field :comments, :string
    field :record_name, :string
    field :meeting_number, :string
    field :summary_user_draft, :string
    field :count_down_start_date, :date
    field :show_summary, :boolean, default: false
    field :show_evaluators, :boolean, default: false
    field :is_resubmitted, :boolean, default: false
    field :revoked, :boolean, default: false
    field :count_down_days, :integer
    field :condition_tracking, :boolean, default: false
    field :condition_status, :integer, default: 0

    belongs_to :associated_license, App.Licenses.LicenseMapping

    belongs_to :user, App.Accounts.User
    belongs_to :initiator, App.Accounts.User
    belongs_to :license, App.Licenses.License
    belongs_to :categories, App.Licenses.LicenseCategories
    belongs_to :account, App.Schema.LicenseAccounts
    field :data, :map

    has_many :license_approval_logs, App.Licenses.LicenseApprovalLogs

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(license_mapping, attrs, input_fields) do
    license_mapping
    |> cast(attrs, @columns)
    |> validate_required(input_fields)
  end

  def changeset_draft(license_mapping, attrs) do
    license_mapping
    |> cast(attrs, @columns)
  end

  def insert_changeset(license_mapping, attrs) do
    license_mapping
    |> cast(attrs, @columns)
  end

  def revoke_update_changeset(license_mapping, attrs) do
    license_mapping
    |> cast(attrs, [:revoked, :reason])
  end

  def attach_changeset(license_mapping, attrs) do
    license_mapping
    |> cast(attrs, [:associated_license_id])
  end

  def data_update_changeset(license_mapping, attrs) do
    license_mapping
    |> cast(attrs, [:data])
  end

  def upload_changeset(license_mapping, attrs, upload_fields) do
    license_mapping
    |> cast(attrs, @columns)
    |> validate_required(upload_fields)
  end
end

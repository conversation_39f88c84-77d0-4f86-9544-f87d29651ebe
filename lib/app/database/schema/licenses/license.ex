defmodule App.Licenses.License do
  use Ecto.Schema
  import Ecto.Changeset

  schema "licenses" do
    field :form_number, :integer
    field :role_id, :integer
    field :name, :string
    field :type, :string, default: "INDIVIDUAL"
    field :color, :string
    field :icon, :string
    field :section, :integer
    field :security_act_no, :integer
    field :note, :string
    field :count_down_days, :integer
    field :reason, :string
    field :status, :string, default: "A"
    field :primary_key, :string
    field :require_license, :boolean, default: false
    field :require_cohort, :boolean, default: false

    field :cohort_window, :boolean, default: false
    field :window_start_date, :date
    field :window_end_date, :date
    field :file_path, :string
    field :amount, :decimal, default: 0.0
    field :other_fees, :decimal, default: 0.0
    belongs_to :associated_license, App.Licenses.License
    belongs_to :categories, App.Licenses.LicenseCategories
    belongs_to :certificate, App.Licenses.LicenseCertificates

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(license, attrs) do
    license
    |> cast(attrs, [
      :icon,
      :color,
      :reason,
      :section,
      :note,
      :security_act_no,
      :name,
      :role_id,
      :form_number,
      :status,
      :associated_license_id,
      :categories_id,
      :primary_key,
      :require_license,
      :require_cohort,
      :cohort_window,
      :window_start_date,
      :window_end_date,
      :file_path,
      :count_down_days,
      :amount,
      :other_fees,
      :certificate_id,
      :type
    ])
    |> validate_required([:name, :status, :security_act_no, :note, :section, :amount, :type])
    |> validate_cohort_requirements()
    |> validate_date_range()
  end

  # Validate cohort-related requirements
  defp validate_cohort_requirements(changeset) do
    require_cohort = get_field(changeset, :require_cohort)
    cohort_window = get_field(changeset, :cohort_window)

    cond do
      # If cohort is required, cohort_window must be set
      require_cohort == true and is_nil(cohort_window) ->
        add_error(changeset, :cohort_window, "must be selected when cohort is required")

      # If cohort window is open, dates are required
      require_cohort == true and cohort_window == true ->
        changeset
        |> validate_required([:window_start_date, :window_end_date],
          message: "is required when cohort window is open"
        )

      true ->
        changeset
    end
  end

  # Validate date range for cohort windows
  defp validate_date_range(changeset) do
    start_date = get_field(changeset, :window_start_date)
    end_date = get_field(changeset, :window_end_date)
    require_cohort = get_field(changeset, :require_cohort)
    cohort_window = get_field(changeset, :cohort_window)

    cond do
      # Only validate dates if cohort is required and window is open
      require_cohort != true or cohort_window != true ->
        changeset

      # Both dates must be present for validation
      is_nil(start_date) or is_nil(end_date) ->
        changeset

      # Validate that end date is after start date
      Date.compare(end_date, start_date) != :gt ->
        add_error(changeset, :window_end_date, "must be after the start date")

      # Validate that start date is not in the past
      Date.compare(start_date, Date.utc_today()) == :lt ->
        add_error(changeset, :window_start_date, "cannot be in the past")

      true ->
        changeset
    end
  end
end

defmodule App.Licenses.LicenseCertificates do
  use AppWeb, :schema

  schema "licence_certificates" do
    field :template, :string
    field :name, :string
    field :description, :string
    field :status, :integer, default: 1

    timestamps()
  end

  @doc false
  def changeset(licence_certificates, attrs) do
    licence_certificates
    |> cast(attrs, [
      :template,
      :name,
      :description,
      :status
    ])
    |> unsafe_validate_unique([:name], App.Repo)
    |> unique_constraint([:name])
  end
end

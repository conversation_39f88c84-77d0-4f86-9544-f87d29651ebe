defmodule App.Licenses.LicenseCategories do
  use Ecto.Schema
  import Ecto.Changeset

  schema "license_categories" do
    field :name, :string
    field :description, :string
    field :color, :string
    field :status, :integer, default: 1
    field :type, :string

    timestamps()
  end

  @doc false
  def changeset(license_category, attrs) do
    license_category
    |> cast(attrs, [:name, :description, :color, :status, :type])
    |> validate_required([:name, :description, :color, :type])
  end
end

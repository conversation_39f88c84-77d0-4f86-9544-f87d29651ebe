defmodule App.Settings.Setting do
  use AppWeb, :schema

  schema "settings" do
    field :deleted_at, :naive_datetime
    field :description, :string
    field :name, :string
    field :status, :boolean, default: false
    field :updated_by, :id

    timestamps()
  end

  @doc false
  def changeset(setting, attrs) do
    setting
    |> cast(attrs, [:name, :status, :description, :deleted_at])
    |> validate_required([:name, :status, :description])
    |> unsafe_validate_unique(:name, App.Repo)
    |> unique_constraint(:name)
  end
end

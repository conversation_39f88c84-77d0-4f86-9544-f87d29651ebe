defmodule App.Settings.SettingsConfig do
  use AppWeb, :schema

  schema "settings_configuration" do
    field :deleted_at, :naive_datetime
    field :description, :string
    field :regex, :string
    field :name, :string
    field :select_details, :string
    field :value, :string
    field :value_type, :string
    field :field_type, :string
    field :updated_by, :id

    field :remarks, :string, virtual: true, redact: true
    timestamps()
  end

  @doc false
  def changeset(settings_config, attrs) do
    settings_config
    |> cast(attrs, [
      :name,
      :value,
      :value_type,
      :select_details,
      :regex,
      :description,
      :deleted_at
    ])
    |> validate_required([:name, :value, :value_type, :select_details, :description])
  end
end

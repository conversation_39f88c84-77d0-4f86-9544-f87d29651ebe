defmodule App.Management.ApiManagement do
  use AppWeb, :schema

  schema "api_management" do
    field :base_url, :string
    field :data, :map
    field :key, :string
    field :name, :string
    field :access_point, :string
    field :status, :integer
    field :type, :string
    field :updated_by, :id
    field :created_by, :id

    timestamps()
  end

  @doc false
  def changeset(api_management, attrs) do
    api_management
    |> cast(attrs, [:name, :type, :base_url, :key, :status, :data, :access_point])
    |> validate_required([:name, :base_url, :status, :data])
  end

  @doc false
  def update_changeset(api_management, attrs) do
    api_management
    |> cast(attrs, [:name, :type, :base_url, :key, :status, :data, :access_point])
  end
end

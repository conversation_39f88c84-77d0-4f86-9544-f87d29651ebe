defmodule App.SummaryData.SummarisedData do
  use Ecto.Schema
  import Ecto.Changeset

  schema "tbl_summarised_data" do
    field :license_field_id, :integer
    field :license_id, :integer
    field :status, :integer

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(summarised_data, attrs) do
    summarised_data
    |> cast(attrs, [:license_field_id, :license_id, :status])
    |> validate_required([:license_field_id, :license_id, :status])
  end
end

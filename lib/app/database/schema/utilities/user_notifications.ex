defmodule App.Notification.UserNotifications do
  use AppWeb, :schema

  schema "user_notifications" do
    field :message, :string
    field :reference, :string
    field :reason, :string
    field :page_url, :string
    field :status, :boolean
    field :type, :string
    field :read_id, {:array, :integer}

    belongs_to(:user, App.Accounts.User)

    belongs_to(:role, App.Roles.DepartmentRoles)

    timestamps()
  end

  @doc false
  def changeset(notification, attrs) do
    notification
    |> cast(attrs, [
      :reason,
      :message,
      :page_url,
      :user_id,
      :read_id,
      :reference,
      :role_id,
      :type,
      :status
    ])
    |> validate_required([:role_id, :message, :page_url])
  end
end

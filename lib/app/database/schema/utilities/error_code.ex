defmodule App.Utilities.ErrorCode do
  use Ecto.Schema
  import Ecto.Changeset

  schema "tbl_error_master" do
    field(:code, :string)
    field(:error_desc, :string)

    belongs_to(:maker, App.Accounts.User, foreign_key: :maker_id, type: :id)
    belongs_to(:checker, App.Accounts.User, foreign_key: :checker_id, type: :id)

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(error_code, attrs) do
    error_code
    |> cast(attrs, [:code, :error_desc, :maker_id, :checker_id])
    |> validate_required([:code, :error_desc])
    |> unique_constraint(:code)
    |> unsafe_validate_unique([:code], App.Repo)
    |> validate_length(:code, is: 3, message: "should be 3 characters")
    |> validate_length(:error_desc,
      min: 4,
      max: 70,
      message: "should be between 4 and 70 characters"
    )
  end
end

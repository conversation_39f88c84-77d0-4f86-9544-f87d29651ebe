defmodule App.Roles.DepartmentRoles do
  use AppWeb, :schema
  alias App.{Accounts.User, Roles.AccessRoles, Roles.DepartmentRolePermission}

  schema "department_roles" do
    field :name, :string
    field :description, :string
    field :system_gen, :boolean, default: false
    field :editable, :boolean, default: true
    field :user_interface, :boolean, default: false
    field :status, :integer, default: 1
    field :deleted_at, :naive_datetime

    belongs_to :created_by_user, User, foreign_key: :created_by
    belongs_to :updated_by_user, User, foreign_key: :updated_by

    has_many :access_roles, AccessRoles, foreign_key: :department_role_id

    has_many :department_role_permissions, DepartmentRolePermission,
      foreign_key: :department_role_id

    has_many :permissions, through: [:department_role_permissions, :permission]

    timestamps()
  end

  def changeset(department_role, attrs) do
    department_role
    |> cast(attrs, [
      :name,
      :description,
      :system_gen,
      :editable,
      :user_interface,
      :status,
      :created_by,
      :updated_by
    ])
    |> validate_required([:name, :description])
    |> validate_length(:name, max: 50)
    |> unique_constraint(:name)
  end
end

defmodule App.Roles.DepartmentRolePermission do
  use AppWeb, :schema
  alias App.Roles.{DepartmentRole, Permission}

  schema "department_role_permissions" do
    belongs_to :department_role, DepartmentRole
    belongs_to :permission, Permission

    timestamps()
  end

  def changeset(department_role_permission, attrs) do
    department_role_permission
    |> cast(attrs, [:department_role_id, :permission_id])
    |> validate_required([:department_role_id, :permission_id])
    |> unique_constraint([:department_role_id, :permission_id])
  end
end

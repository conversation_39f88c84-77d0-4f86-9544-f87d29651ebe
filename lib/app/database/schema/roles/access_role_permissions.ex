defmodule App.Roles.AccessRolePermission do
  use AppWeb, :schema
  alias App.Roles.{AccessRoles, Permission}

  schema "access_role_permissions" do
    belongs_to :access_role, AccessRoles
    belongs_to :permission, Permission

    timestamps()
  end

  def changeset(access_role_permission, attrs) do
    access_role_permission
    |> cast(attrs, [:access_role_id, :permission_id])
    |> validate_required([:access_role_id, :permission_id])
    |> unique_constraint([:access_role_id, :permission_id])
  end
end

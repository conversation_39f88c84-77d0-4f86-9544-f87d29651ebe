defmodule App.Roles.Permission do
  use AppWeb, :schema

  schema "permissions" do
    field :description, :string
    field :service, :string
    field :tab, :string
    field :code, :string

    timestamps()
  end

  @doc false
  def changeset(permission, attrs) do
    permission
    |> cast(attrs, [:service, :description, :tab, :code])
    |> validate_required([:service, :description, :tab, :code])
    |> unique_constraint(:code)
  end
end

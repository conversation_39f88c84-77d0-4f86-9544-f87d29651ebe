defmodule App.Licensing.IssuedLicense do
  use Ecto.Schema
  import Ecto.Changeset

  schema "issued_licenses" do
    field :expiring_date, :date
    field :reason, :string
    field :status, :integer, default: 1
    belongs_to :application, App.Licenses.LicenseMapping
    belongs_to :holder, App.Accounts.User
    belongs_to :license, App.Licenses.License
    belongs_to :certificate, App.Licenses.LicenseCertificates
    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(issued_license, attrs) do
    issued_license
    |> cast(attrs, [
      :status,
      :reason,
      :certificate_id,
      :license_id,
      :holder_id,
      :application_id,
      :expiring_date
    ])
    |> validate_required([
      :status,
      :reason,
      :application_id,
      :license_id,
      :holder_id,
      :certificate_id,
      :expiring_date
    ])
  end
end

defmodule App.NumberingLogic do
  # Entry point: starts numbering with an empty prefix.
  def generate(sections) do
    generate(sections, [])
  end

  # Base case: no sections left.
  defp generate([], _prefix), do: []

  # Process each section at the current level.
  defp generate(sections, prefix) do
    sections
    |> Enum.with_index(1)
    |> Enum.flat_map(fn {{title, subsections}, index} ->
      current_prefix = prefix ++ [index]
      current_number = format(current_prefix)
      # Process children with the updated prefix.
      children = generate(subsections, current_prefix)
      [%{number: current_number, title: title} | children]
    end)
  end

  # Format the prefix list into a section number string.
  # Top-level: e.g., [1] becomes "1.0"
  defp format([n]), do: "#{n}.0"
  defp format(numbers), do: Enum.join(numbers, ".")
end

# Example usage:
# sections = [
#  {"BACKGROUND", [
#    {"Date Complete Application Received", []},
#    {"Education", [
#      {"Applicant", []}
#    ]},
#    {"Work Experience",
#      [{"The Applicant has previously",[]} ]},
#    {"Nationality",[{"The Applicants",[]}]},
#  ]},
#  {"PRESENT OCCUPATION", [{"The Applicant is presently employed as a",[]}]},
#  {"CHARACTER REFEREES", [{" The Applicant has provided",[]}]},
#  {"CAPITAL MARKETS ASSOCIATION", [{"The Applicant is a member ", []}]},
#  {"FINANCIAL STATUS", [{"In compliance with the requirements of Rule", []}]},
#  {"MANAGEMENT RECOMMENDATION", [{" Management recommends that ",[]}, {"We hereby submit the application",[]}]}
#
# ]

App.NumberingLogic.generate([
  {"BACKGROUND",
   [
     {"Date Complete Application Received", []},
     {"Education",
      [
        {"Applicant", []}
      ]},
     {"Work Experience", [{"The Applicant has previously", []}]},
     {"Nationality", [{"The Applicants", []}]}
   ]},
  {"PRESENT OCCUPATION", [{"The Applicant is presently employed as a", []}]},
  {"CHARACTER REFEREES", [{" The Applicant has provided", []}]},
  {"CAPITAL MARKETS ASSOCIATION", [{"The Applicant is a member ", []}]},
  {"FINANCIAL STATUS", [{"In compliance with the requirements of Rule", []}]},
  {"MANAGEMENT RECOMMENDATION",
   [{" Management recommends that ", []}, {"We hereby submit the application", []}]}
])
# |> IO.inspect(label: "OPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP")

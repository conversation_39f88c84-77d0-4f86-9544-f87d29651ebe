defmodule App.LicenseReviews do
  @moduledoc """
  The LicenseReviews context.
  """
  # App.LicenseReviews.query_field_label_for_review(1)
  alias App.CustomContext

  import Ecto.Query, warn: false
  alias App.Repo

  alias App.LicenseReviews.LicenseReview
  alias App.{Repo, Notification.UserNotifications, Utilities}

  @topic "license_review"

  def subscribe(), do: CustomContext.subscribe(@topic)
  def subscribe(id), do: CustomContext.subscribe("#{@topic}#{id}")

  @doc """
  Returns the list of license_reviews.

  ## Examples

      iex> list_license_reviews()
      [%LicenseReview{}, ...]

  """
  def list_license_reviews do
    Repo.all(LicenseReview)
  end

  def get_attention_field(license_id) do
    LicenseReview
    |> where([a], a.license_id == ^license_id and a.attention_status in ^["submitted", "corrected"])
    |> select([a], %{
      attention_field: a.attention_field,
      reason: a.reason,
      field_id: a.field_id,
      approver_id: a.user_id,
      user_license_mapping_id: a.user_license_mapping_id,
      attention_status: a.attention_status,
      license_id: a.license_id
    })
    |> Repo.all()
  end

  def get_attention_field_by_id(license_id, field_id) do
    LicenseReview
    |> where(
      [a],
      a.license_id == ^license_id and a.field_id == ^field_id and
        a.attention_status == ^"submitted"
    )
    |> select([a], %{
      attention_field: a.attention_field,
      reason: a.reason,
      field_id: a.field_id,
      user_license_mapping_id: a.user_license_mapping_id,
      attention_status: a.attention_status,
      license_id: a.license_id
    })
    |> Repo.one()
  end

  @doc """
  Gets a single license_review.

  Raises `Ecto.NoResultsError` if the License review does not exist.

  ## Examples

      iex> get_license_review!(123)
      %LicenseReview{}

      iex> get_license_review!(456)
      ** (Ecto.NoResultsError)

  """
  def get_license_review!(id), do: Repo.get!(LicenseReview, id)

  def get_license_review_field_id(id) do
    LicenseReview
    |> where([a], a.field_id == ^id)
    |> limit(1)
    |> Repo.one()
  end

  def get_license_review_field(id) when is_list(id) do
    LicenseReview
    |> where([a], a.field_id in ^id)
   # |> limit(1)
    |> Repo.all()
  end

  def get_license_review_field(id) do
    LicenseReview
    |> where([a], a.field_id == ^id)

    |> Repo.all()
  end


  @doc """
  Creates a license_review.

  ## Examples

      iex> create_license_review(%{field: value})
      {:ok, %LicenseReview{}}

      iex> create_license_review(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_license_review(attrs \\ %{}) do
    %LicenseReview{}
    |> LicenseReview.changeset(attrs)
    |> Repo.insert()
  end

  def submit_for_review(attrs, socket) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(
      :get,
      fn repo, _ ->
        {
          :ok,
          LicenseReview
          |> where(
            [a],
            a.field_id == ^attrs["field_id"]
            #                  a.user_license_mapping_id == ^attrs["user_license_mapping_id"] and
            #                  a.license_id == ^attrs["license_id"]
          )
          |> limit(1)
          |> repo.one()
        }
      end
    )
    |> Ecto.Multi.insert_or_update(
      :submit_review,
      fn %{get: record} ->
        LicenseReview.changeset(
          record || %LicenseReview{},
          %{
            attention_field: attrs["field_label"],
            user_id: socket.assigns.current_user.id,
            attention_status: "submitted",
            reason: attrs["reason"],
            field_id: attrs["field_id"],
            license_id: attrs["license_id"],
            associated_id: attrs["associate_id"],
            user_license_mapping_id: attrs["user_license_mapping_id"]
          }
        )
      end
    )
    |> Repo.transaction()
    |> CustomContext.notify_subs("#{@topic}#{attrs["user_license_mapping_id"]}", "update_review")
  end

  def get_lince_review_by_field_id(id) do
    LicenseReview
    |> where([a], a.license_id in ^id)
    |> limit(1)
    |> Repo.one()
  end

  def query_by_field_id(id) do
    LicenseReview
    |> where([a], a.field_id == ^id and a.attention_status == ^"submitted")
    |> limit(1)
    |> Repo.one()
  end

  def update_license_review(reason, ids, record, fields) when is_list(ids) do
    _get_level = Utilities.get_current_approval_level!(record.status, record.license.categories_id)

    _multi =
      Ecto.Multi.new()
      |> Ecto.Multi.update_all(
        :update_review,
        from(r in LicenseReview, where: r.license_id in ^ids),
        set: [
          reason: reason
        ]
      )
      |> Ecto.Multi.insert(
        :create_notification,
        fn %{update_review: _update_review} ->
          UserNotifications.changeset(
            %UserNotifications{},
            %{
              "message" =>
                "A Licence Application has been returned because the following field(s):#{fields} #{reason}",
              "page_url" => "/license/applications/" <> Enum.join(Enum.uniq(ids), ","),
              "type" => "LICENCE_DECLINED_CHECKED",
              "reason" => reason,
              "role_id" => 8,
              "user_id" => record.user_id
            }
          )
        end
      )
      |> Repo.transaction()
  end

  def remove_review(attrs, _socket) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(
      :get,
      fn repo, _ ->
        {
          :ok,
          LicenseReview
          |> where(
            [a],
            a.field_id == ^attrs["id"] and
              a.user_license_mapping_id == ^attrs["user_license_mapping_id"] and
              a.license_id == ^attrs["license_id"]
          )
          |> limit(1)
          |> repo.one()
        }
      end
    )
    |> Ecto.Multi.update(
      :update_review,
      fn %{get: field_id} ->
        LicenseReview.changeset(field_id, %{
          attention_field: attrs["field"],
          attention_status: "removed"
        })
      end
    )
    |> Repo.transaction()
    |> CustomContext.notify_subs("#{@topic}#{attrs["user_license_mapping_id"]}", "update_review")
  end

  def query_field_label_for_review(id) do
    LicenseReview
    |> where([a], a.attention_status in ^["submitted", "corrected"] and a.user_license_mapping_id == ^id)
    |> select(
      [a],
      %{
        attention_field: a.attention_field,
        id: a.field_id,
        user_license_mapping_id: a.user_license_mapping_id,
        attention_status: a.attention_status,
        license_id: a.license_id,
        associated_id: a.associated_id,
        reason: a.reason
      }
    )
    # |> where([a], )
    |> Repo.all()
  end

  def displaying_field_label_for_review(id) do
    LicenseReview
    |> where([a], a.attention_status == ^"submitted" and a.license_id == ^id)
    |> select(
      [a],
      %{
        attention_field: a.attention_field,
        id: a.id,
        attention_status: a.attention_status,
        license_id: a.license_id
      }
    )
    |> Repo.all()
    |> Enum.map(fn x -> x.attention_field end)
  end

  def query_field_d(id) do
    LicenseReview
    |> where([a], a.attention_status == ^"submitted" and a.license_id == ^id)
    |> select(
      [a],
      %{
        attention_field: a.attention_field,
        id: a.id,
        attention_status: a.attention_status,
        license_id: a.license_id
      }
    )
    |> Repo.all()
    |> Enum.map(fn x -> x.license_id end)
  end

  def querying_field_label_from_review_by_id(id) do
    LicenseReview
    |> where([a], a.attention_status == ^"submitted" and a.license_id == ^id)
    |> select(
      [a],
      %{
        attention_field: a.attention_field,
        id: a.id,
        attention_status: a.attention_status,
        license_id: a.license_id,
        field_id: a.field_id
      }
    )
    |> limit(1)
    |> Repo.one()
  end

  def checking_field_label_for_review() do
    LicenseReview
    |> where([a], a.attention_status == ^"submitted")
    |> select(
      [a],
      %{
        attention_field: a.attention_field,
        field_id: a.field_id
      }
    )
    |> Repo.all()
    |> Enum.map(fn x -> x.field_id end)
  end

  @doc """
  Updates a license_review.

  ## Examples

      iex> update_license_review(license_review, %{field: new_value})
      {:ok, %LicenseReview{}}

      iex> update_license_review(license_review, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_license_reviews(%LicenseReview{} = license_review, attrs) do
    license_review
    |> LicenseReview.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a license_review.

  ## Examples

      iex> delete_license_review(license_review)
      {:ok, %LicenseReview{}}

      iex> delete_license_review(license_review)
      {:error, %Ecto.Changeset{}}

  """
  def delete_license_review(%LicenseReview{} = license_review) do
    Repo.delete(license_review)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking license_review changes.

  ## Examples

      iex> change_license_review(license_review)
      %Ecto.Changeset{data: %LicenseReview{}}

  """
  def change_license_review(%LicenseReview{} = license_review, attrs \\ %{}) do
    LicenseReview.changeset(license_review, attrs)
  end
end

defmodule App.Management do
  @moduledoc """
  The Management context.
  """

  import Ecto.Query, warn: false
  alias App.{Repo}
  alias Logs.Audit

  alias App.Management.{ApiManagement}

  def get_api_management!(id), do: Repo.get!(ApiManagement, id)

  def get_by_id(id) do
    ApiManagement
    |> where([a], a.id == ^id)
    |> preload(:payment_provider)
    |> Repo.one()
  end

  def get_data_by_name(name) do
    ApiManagement
    |> where([a], a.name == ^name)
    |> select([a], a.data)
    |> limit(1)
    |> Repo.one()
  end

  def get_api_management_by_name_and_type(name, type) do
    if data = Cachex.get!(:system, "ApiManagement:#{name}:#{type}") do
      data
    else
      data =
        ApiManagement
        |> where([a], a.name == ^name and a.type == ^type)
        |> limit(1)
        |> Repo.one()

      Cachex.put(:system, "ApiManagement:#{name}:#{type}", data, expire: :timer.minutes(5))
      data
    end
  end

  def update_api(socket, %ApiManagement{} = api_management, attrs) do
    Ecto.Multi.update(Ecto.Multi.new(), :entry, ApiManagement.changeset(api_management, attrs))
    |> Audit.create_system_log_session_live_multi(
      socket,
      "Update New API [#{api_management.name} - #{api_management.type}]",
      "UPDATE",
      attrs,
      "API Maintenance"
    )
    |> Repo.transaction()
  end

  def update_api_management_multi(multi, attrs) do
    api_management = get_by_id(attrs["id"])
    Ecto.Multi.update(multi, :entry, ApiManagement.changeset(api_management, attrs))
  end

  def update_api_management(api, attrs) do
    api
    |> ApiManagement.update_changeset(attrs)
    |> Repo.update!()
  end

  # def change_api_management(%ApiManagement{} = api, attrs \\ %{}) do
  #   ApiManagement.create_changeset(api, attrs)
  # end

  def change_api_management1(attrs \\ %{}) do
    ApiManagement.changeset(%ApiManagement{}, attrs)
  end
end

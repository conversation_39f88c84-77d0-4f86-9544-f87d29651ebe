defmodule App.CustomContext do
  @moduledoc """
  The CustomContext context.
  """

  alias App.{
    Database.CustomDB,
    Repo
  }

  def sum_decimal(list) do
    List.flatten(list)
    |> Enum.reduce(Decimal.new(0), &Decimal.add(&1, &2))
  end

  def subscribe(topic), do: Phoenix.PubSub.subscribe(App.PubSub, to_string(topic))

  def process_transaction(data, type \\ :all) do
    if type == :infinity do
      Repo.transaction(data, timeout: :infinity)
    else
      Repo.transaction(data)
    end
  end

  def filters(attrs) do
    CustomDB.filter(%CustomDB{}, attrs)
  end

  def add_error(message, field, params) do
    CustomDB.add_error_to(%CustomDB{}, message, field, params)
  end

  def after_save({:ok, post}, func) do
    {:ok, _post} = func.(post)
  end

  def after_save(error, _func), do: error

  def notify_subs(result, topic \\ :all, event, module \\ __MODULE__)

  def notify_subs({:ok, result}, topic, event, module) do
    try do
      Phoenix.PubSub.broadcast(App.PubSub, topic, {module, event, result})
    rescue
      e ->
        e
    end

    {:ok, result}
  end

  def notify_subs({:error, result}, _topic, _event, _module) when is_bitstring(result),
    do: {:error, result}

  def notify_subs({:error, result}, _topic, _event, _module),
    do: {:error, traverse_errors(result.errors)}

  def notify_subs(
        {:error, _failed_operation, failed_value, _changes_so_far},
        _topic,
        _event,
        _module
      ),
      do: {:error, traverse_errors(failed_value.errors)}

  def traverse_errors(errors) do
    for({key, {msg, _opts}} <- errors, do: "#{key} #{msg}")
    |> List.first()
  end

  def notify_parent(msg, module \\ __MODULE__), do: send(self(), {module, msg})
end

defmodule App.Evaluations do
  @moduledoc """
  The Evaluations context.
  """

  import Ecto.Query, warn: false

  alias App.Evaluation.{ApplicationEvaluators, Evaluation, EvaluationCriteria, SandboxScore}
  alias App.Repo
  alias Logs.Audit

  def list_evaluations do
    Repo.all(Evaluation)
  end

  def list_active_criteria do
    EvaluationCriteria
    |> where([a], a.status == 1)
    |> order_by([a], asc: a.section)
    |> Repo.all()
  end

  def get_evaluation!(id), do: Repo.get!(Evaluation, id)

  def get_sandbox_score_by_evaluation_id(evaluation_id) do
    SandboxScore
    |> where([s], s.evaluation_id == ^evaluation_id)
    |> limit(1)
    |> Repo.one()
  end

  def list_sandbox_scores_with_criteria(application_id) do
    # Get all evaluations with their criteria and evaluator info
    evaluations =
      Evaluation
      |> join(:left, [e], c in assoc(e, :criteria))
      |> join(:left, [e], u in assoc(e, :evaluator))
      |> join(:left, [e], a in assoc(e, :application))
      |> where([e], e.status == 1 and e.application_id == ^application_id)
      |> order_by([e, c], asc: c.section, asc: c.id)
      |> select([e, c, u, a], %{
        id: e.id,
        rating: e.rating,
        comments: e.comments,
        criteria_id: c.id,
        criteria_name: c.name,
        criteria_section: c.section,
        evaluator_id: u.id,
        evaluator_email: u.email,
        evaluator_name: fragment("? || ' ' || ?", u.first_name, u.last_name),
        application_id: a.id,
        application_name: a.record_name,
        inserted_at: e.inserted_at
      })
      |> Repo.all()

    evaluations
  end

  def list_sandbox_scores(application_id) do
    SandboxScore
    |> join(:left, [a], b in assoc(a, :evaluation))
    |> join(:left, [a, b], c in assoc(a, :application))
    |> join(:left, [a, b], d in assoc(b, :evaluator))
    |> where([a], a.status == 1 and a.application_id == ^application_id)
    |> order_by([a], desc: a.inserted_at)
    |> select([a, b, c, d], %{
      id: a.id,
      section_score: a.section_score,
      max_score: a.max_score,
      percentage_score: a.percentage_score,
      grand_total: a.grand_total,
      max_grand_total: a.max_grand_total,
      grand_percentage_score: a.grand_percentage_score,
      decision: a.decision,
      comments: a.comments,
      evaluation_id: a.evaluation_id,
      application_id: a.application_id,
      inserted_at: a.inserted_at,
      application_name: c.record_name,
      evaluator_email: d.email,
      evaluator_name: fragment("? || ' ' || ?", d.first_name, d.last_name)
    })
    |> Repo.all()
  end

  def list_sandbox_scores_grouped_by_application(application_id) do
    list_sandbox_scores(application_id)
    |> Enum.group_by(& &1.application_id)
    |> Enum.map(fn {application_id, scores} ->
      # Get application info from first score
      first_score = List.first(scores)

      # Group scores by evaluator
      evaluator_scores =
        scores
        |> Enum.group_by(& &1.evaluator_email)
        |> Enum.map(fn {evaluator_email, eval_scores} ->
          %{
            evaluator_email: evaluator_email,
            evaluator_name: List.first(eval_scores).evaluator_name,
            scores: eval_scores,
            total_score:
              eval_scores
              |> Enum.map(&(&1.section_score || Decimal.new(0)))
              |> Enum.reduce(Decimal.new(0), &Decimal.add/2),
            grand_percentage: List.first(eval_scores).grand_percentage_score || Decimal.new(0)
          }
        end)

      %{
        application_id: application_id,
        application_name: first_score.application_name,
        evaluators: evaluator_scores,
        total_evaluators: length(evaluator_scores)
      }
    end)
  end

  def get_unconfirmed_evaluator_by_user!(id) do
    ApplicationEvaluators
    |> where([a], a.evaluator_id == ^id and a.status == false)
    |> limit(1)
    |> Repo.one()
  end

  # check if evalutor has completed all evaluations
  def check_evaluator_completion(evaluator_id, application_id) do
    Evaluation
    |> where([a], a.evaluator_id == ^evaluator_id and a.application_id == ^application_id)
    |> where([a], a.status == 1)
    |> Repo.aggregate(:count, :id)
  end

  def check_confirmation!(user_id, application_id) do
    ApplicationEvaluators
    |> where(
      [a],
      a.evaluator_id == ^user_id and a.application_id == ^application_id and a.status == true
    )
    |> Repo.exists?()
  end

  def get_evaluations_by_evaluator!(evaluator_id, application_id) do
    Evaluation
    |> join(:left, [a], b in assoc(a, :criteria))
    |> where([a], a.evaluator_id == ^evaluator_id and a.application_id == ^application_id)
    |> order_by([a, b], asc: b.section)
    |> preload([:criteria, :application])
    |> Repo.all()
  end

  def create_evaluation(%{assigns: assigns} = socket, attrs \\ %{}) do
    active_criteria = list_active_criteria()
    evaluator = get_unconfirmed_evaluator_by_user!(assigns.current_user.id)
    time = NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update_evaluator,
      ApplicationEvaluators.changeset(evaluator, %{
        status: true
      })
    )
    |> Ecto.Multi.insert_all(
      :evaluations,
      Evaluation,
      Enum.map(active_criteria, fn criteria ->
        %{
          criteria_id: criteria.id,
          application_id: assigns.application.id,
          evaluator_id: assigns.current_user.id,
          inserted_at: time,
          updated_at: time,
          status: 0
        }
      end)
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} agreed to the evaluation",
      "SANDBOX EVALUATION",
      attrs,
      "Sandbox Evaluation"
    )
    |> Repo.transaction()
  end

  def update_ratings(record, attrs \\ %{}) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "rating",
      Evaluation.insert_changeset(
        record,
        %{
          "rating" => attrs["rating"],
          "comments" => attrs["comments"],
          "status" => 0
        }
      )
    )
    |> Repo.transaction()
  end

  def submit_evaluation(records, %{assigns: assigns} = socket, general_comment \\ "") do
    # Calculate scores for all evaluations
    scores = calculate_sandbox_scores(records)

    multi =
      Enum.reduce(records, Ecto.Multi.new(), fn record, multi ->
        # Get the scores for current evaluation
        evaluation_scores = calculate_individual_evaluation_scores(record, records)

        # Get existing sandbox score
        sandbox_score = get_sandbox_score_by_evaluation_id(record.id)

        Ecto.Multi.update(
          multi,
          "submit_evaluation_#{record.id}",
          Evaluation.insert_changeset(
            record,
            %{
              "status" => 1
            }
          )
        )
        |> Ecto.Multi.insert_or_update(
          "sandbox_score_#{record.id}",
          SandboxScore.changeset(
            sandbox_score || %SandboxScore{},
            Map.merge(evaluation_scores, %{
              evaluation_id: record.id,
              application_id: record.application_id,
              grand_total: scores.grand_total,
              max_grand_total: scores.max_grand_total,
              grand_percentage_score: scores.grand_percentage_score,
              comments: general_comment
            })
          )
        )
      end)

    multi
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} submitted a evaluation for application: #{assigns.application.record_name}",
      "SANDBOX EVALUATION",
      %{},
      "Sandbox Evaluation"
    )
    |> Repo.transaction()
  end

  defp calculate_sandbox_scores(evaluations) do
    # Calculate total scores across all sections
    grand_total =
      evaluations
      |> Enum.map(fn eval ->
        if eval.rating, do: Decimal.to_float(eval.rating), else: 0.0
      end)
      |> Enum.sum()

    # Calculate max possible score (4 points per evaluation)
    max_grand_total = length(evaluations) * 4.0

    # Calculate grand percentage
    grand_percentage_score =
      if max_grand_total > 0,
        do: grand_total / max_grand_total * 100,
        else: 0.0

    %{
      grand_total: Decimal.new(Float.to_string(grand_total)),
      max_grand_total: Decimal.new(Float.to_string(max_grand_total)),
      grand_percentage_score: Decimal.new(Float.to_string(grand_percentage_score))
    }
  end

  defp calculate_individual_evaluation_scores(evaluation, all_evaluations) do
    # Get all evaluations in the same section as current evaluation
    section_evaluations =
      Enum.filter(all_evaluations, fn eval ->
        eval.criteria.section == evaluation.criteria.section
      end)

    # Calculate section score
    section_score =
      section_evaluations
      |> Enum.map(fn eval ->
        if eval.rating, do: Decimal.to_float(eval.rating), else: 0.0
      end)
      |> Enum.sum()

    # max score for this section
    max_score = length(section_evaluations) * 4.0

    # percentage score for this section
    percentage_score =
      if max_score > 0,
        do: section_score / max_score * 100,
        else: 0.0

    # Return scores
    %{
      section_score: Decimal.new(Float.to_string(section_score)),
      max_score: Decimal.new(Float.to_string(max_score)),
      percentage_score: Decimal.new(Float.to_string(percentage_score))
    }
  end

  def change_evaluations(attrs \\ %{}) do
    Evaluation.changeset(%Evaluation{}, attrs)
  end

  def change_record_evaluations(%Evaluation{} = record, attrs \\ %{}) do
    Evaluation.insert_changeset(record, attrs)
  end
end

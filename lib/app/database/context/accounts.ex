defmodule App.Accounts do
  @moduledoc """
  The Accounts context.
  """

  import Ecto.Query, warn: false
  alias App.Repo
  alias App.Roles.AccessRoles
  alias App.Schema.LicenseAccounts
  alias Logs.Audit

  alias App.{
    Accounts.User,
    Accounts.UserToken,
    CustomContext,
    Send.Email
  }

  #App.Accounts.get_license_account_declined_by_user_id(6)

  @topic "users"

  #App.Accounts.get_license_account_by_application_id()

  def get_license_account_by_application_id(application_id) do
    LicenseAccounts
    |> where([a], a.application_id == ^application_id)
    |> limit(1)
    |> Repo.one()
  end

  def get_license_account_declined_by_user_id(id) do
     LicenseAccounts
    |> where([a], a.user_id == ^id)
    |> limit(1)
    |> Repo.one()

  end

  def get_account_total(application_id) do
    LicenseAccounts
    |> where([a], a.application_id == ^application_id)
    # |> select([a], a.license_amount)
    |> limit(1)
    |> Repo.one()
  end

  def get_unverified_accounts() do
    LicenseAccounts
    |> join(:left, [a], b in assoc(a, :application))
    |> where(
      [a, b],
      a.verified == false and a.balance >= 0 and b.status == 1 and a.declined == false and
        b.approval_status == true
    )
    |> preload([:user, :application])
    |> Repo.all()
  end

  def get_approval_role_id(role_id) do
    User
    |> where([a], a.role_id == ^role_id)
    |> limit(1)
    |> Repo.one()

  end

  def subscribe, do: CustomContext.subscribe(@topic)

  def get_user!(id), do: Repo.get!(User, id)

  def get_username_as_email_by_id(id) do
    User
    |> where([a], a.id == ^id)
    |> select([a], a.email)
    |> limit(1)
    |> Repo.one()
  end

  def get_all_user_sessions do
    UserToken
    |> select([a], {a.token, a.user_id})
    |> Repo.all()
  end

  def change_user(%User{} = user, attrs \\ %{}),
    do: User.changeset(user, attrs)

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking user changes.

  ## Examples

      iex> change_user_registration(user)
      %Ecto.Changeset{data: %User{}}

  """

  def change_user_login(attrs \\ %{}) do
    User.login_changeset(%User{}, attrs, hash_password: false, validate_email: false)
  end

  def change_cllent_status(socket, attrs, user) do
    new_status = if attrs["status"] == "D", do: "Disable", else: "Enable"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_status",
      User.changeset(user, %{status: attrs["status"], reason: attrs["reason"]})
    )
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.first_name} #{new_status}d status for user name: #{user.first_name}",
      "ClIENT MAINTENANCE STATUS",
      attrs,
      "Client maintenance"
    )
    |> Repo.transaction()
  end

  ## Settings

  @doc """
  Returns an `%Ecto.Changeset{}` for changing the user email.

  ## Examples

      iex> change_user_email(user)
      %Ecto.Changeset{data: %User{}}

  """

  def total_clients() do
    User
    |> where([a], a.status == "A" and a.user_type == "CLIENT")
    |> Repo.aggregate(:count, :id)
  end

  def get_access_roles_by_department(department_id) do
    AccessRoles
    |> where([a], a.department_role_id == ^department_id and a.status == 1)
    |> select([a], a.id)
    |> Repo.all()
  end

  def get_department_users(department_id) do
    roles = get_access_roles_by_department(department_id)

    User
    |> where([a], a.status == "A" and a.role_id in ^roles)
    |> Repo.all()
  end

  def total_dealers() do
    User
    |> where([a], a.status == "A" and a.role_id in [10])
    |> Repo.aggregate(:count, :id)
  end

  def get_admin!(id), do: Repo.get!(User, id)

  def create_admin_user(
        socket,
        attrs,
        password \\ NumberF.default_password()
      ) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "user",
      User.registration_admin_changeset(
        %User{},
        Map.merge(attrs, %{
          "password" => password,
          "user_role" => 2,
          "user_type" => "STAFF",
          "status" => "A"
        })
      )
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "Admin User: #{socket.assigns.current_user.email}
        has created a user of the email: #{attrs["email"]}, firstname: #{attrs["first_name"]}, surname: #{attrs["last_name"]}, mobile number: #{attrs["mobile"]}",
      "CREATE",
      attrs,
      "User Maintenance"
    )
    |> Repo.transaction()
    |> case do
      {:ok, records} ->
        Task.start(fn ->
          Email.send_email_notification(records["user"].email, password)
        end)

        {:ok, records}

      {:error, reason} ->
        {:error, reason}

      {:error, _, error, _} ->
        {:error, error}
    end
  end

  def update_admin_user(_socket, %User{} = user, attrs) do
    # temp for missing admins
    changeset = User.registration_admin_changeset(user, attrs)

    # Perform the update in a transaction
    Ecto.Multi.new()
    |> Ecto.Multi.update("update_admin", changeset)
    |> Repo.transaction()
  end

  def change_admin_user_status(socket, attrs, user) do
    new_status = if attrs["status"] == "D", do: "Disable", else: "Enable"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_status",
      User.registration_admin_changeset(user, %{status: attrs["status"]})
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} #{new_status}d status for: #{user.email}",
      "ADMIN USER STATUS",
      attrs,
      "Admin Users"
    )
    |> Repo.transaction()
  end

  def reset_admin(socket, _attrs, admin, password \\ NumberF.default_password()) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update_admin,
      User.registration_admin_changeset(admin, %{hashed_password: password})
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      """
      Admin User: #{socket.assigns.current_user.email}
      has reset the password for: #{admin.email}
      """,
      "UPDATE",
      "Password Reset",
      "User Maintenance"
    )
    |> Repo.transaction()
    |> case do
      {:ok, _records} ->
        Task.start(fn ->
          Email.confirm_password_reset(password, admin.email)
        end)

        {:ok, "Password successfully reset"}

      {:error, _reason} ->
        {:error, "Failed to reset password"}

      {:error, _, error, _} ->
        {:error, error}
    end
  end

  def reset_admin_password(socket, _attrs, admin, password \\ NumberF.default_password()) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update_admin,
      User.registration_admin_changeset(admin, %{password: password})
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      """
      Admin User: #{socket.assigns.current_user.email}
      has reset the password for: #{admin.email}
      """,
      "UPDATE",
      "Password Reset",
      "User Maintenance"
    )
    |> Repo.transaction()
    |> case do
      {:ok, _records} ->
        Task.start(fn ->
          Email.confirm_password_reset(password, admin.email)
        end)

        {:ok, "Password successfully reset"}

      {:error, _reason} ->
        {:error, "Failed to reset password"}

      {:error, _, error, _} ->
        {:error, error}
    end
  end

  def change_user_status(socket, attrs, user) do
    new_status = if attrs["status"] == "0", do: "Disable", else: "Enable"

    Ecto.Multi.new()
    |> Ecto.Multi.update(:update_status, User.changeset(user, %{status: attrs["status"]}))
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.username} #{new_status}d status for: #{user.username}",
      "CHANGE STATUS",
      attrs,
      "User Maintenance"
    )
    |> Repo.transaction()
    |> CustomContext.notify_subs(@topic, :status_change, __MODULE__)
  end

  @doc """
  Emulates that the email will change without actually changing
  it in the database.

  ## Examples

      iex> apply_user_email(user, "valid password", %{email: ...})
      {:ok, %User{}}

      iex> apply_user_email(user, "invalid password", %{email: ...})
      {:error, %Ecto.Changeset{}}

  """

  def get_last_user_token_by_user_id(user_id) do
    UserToken
    |> where([a], a.user_id == ^user_id)
    |> order_by([:desc, :id])
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Updates the user email using the given token.

  If the token matches, the user email is updated and the token is deleted.
  The confirmed_at date is also updated to the current time.
  """

  def change_user_password(user, attrs \\ %{}) do
    User.password_changeset(user, attrs, hash_password: false)
  end

  @doc """
  Updates the user password.

  ## Examples

      iex> update_user_password(user, "valid password", %{password: ...})
      {:ok, %User{}}

      iex> update_user_password(user, "invalid password", %{password: ...})
      {:error, %Ecto.Changeset{}}

  """
  def update_user_password(user, password, attrs) do
    changeset =
      user
      |> User.password_changeset(attrs)
      |> User.validate_current_password(password)

    Ecto.Multi.new()
    |> Ecto.Multi.update(:user, changeset)
    |> Ecto.Multi.delete_all(:tokens, UserToken.user_and_contexts_query(user, :all))
    |> Repo.transaction()
    |> case do
      {:ok, %{user: user}} -> {:ok, user}
      {:error, :user, changeset, _} -> {:error, changeset}
    end
  end

  ## Session

  @doc """
  Generates a session token.
  """
  def generate_user_session_token(user) do
    {token, user_token} = UserToken.build_session_token(user)
    Repo.insert!(user_token)
    token
  end

  @doc """
  Gets the user with the given signed token.
  """
  def get_user_by_session_token(token) do
    {:ok, query} = UserToken.verify_session_token_query(token)
    Repo.one(query)
  end

  @doc """
  Deletes the signed token with the given context.
  """
  def delete_user_session_token(token) do
    Repo.delete_all(UserToken.token_and_context_query(token, "session"))
    :ok
  end

  def update_where_session_token_by_token(token) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(
      :post,
      fn repo, _changes ->
        case repo.get_by(UserToken, token: token) do
          nil -> {:error, :not_found}
          post -> {:ok, post}
        end
      end
    )
    |> Ecto.Multi.update(:user, fn %{post: token} -> UserToken.update_session(token) end)
    |> Repo.transaction()
  end

  def change_admin_user_profile(%User{} = user, attrs \\ %{}) do
    User.registration_admin_changeset(user, attrs)
  end

  # Missing functions referenced in warnings
  def add_user_to_role(user_id, role_id, _current_user_id) do
    user = get_user!(user_id)
    User.changeset(user, %{role_id: role_id})
    |> Repo.update()
  end

  def change_device_status(_device_id, _status, _current_user) do
    # This function should be implemented based on your device schema
    {:ok, "Device status changed"}
  end

  def get_devices!(_user_id) do
    # This function should return devices for a user
    # Implementation depends on your device schema
    []
  end

  def get_clients!() do
    User
    |> where([u], u.user_type == "CLIENT" and u.status == "A")
    |> Repo.all()
  end

  def get_client_name_by_id(id) do
    User
    |> where([u], u.id == ^id)
    |> select([u], fragment("? || ' ' || ?", u.first_name, u.last_name))
    |> Repo.one()
  end

  def search_username_by_username(username) do
    User
    |> where([u], ilike(u.username, ^"%#{username}%"))
    |> Repo.all()
  end

  def disable_user_for_maker_checker(user_id, status, _current_user) do
    user = get_user!(user_id)
    User.changeset(user, %{status: status})
    |> Repo.update()
  end

end

defmodule App.Licensing do
  @moduledoc """
  The Licensing context.
  """
  import Ecto.Query, warn: false
  alias App.Repo
  alias App.Licensing.IssuedLicense
  alias App.Notification.UserNotifications
  alias Logs.Audit
  alias App.Send.Email
  alias App.Licenses.LicenseCertificates
  alias App.Schema.LicenseAccounts

  # alias App.Schema.License.IssuedLicense


  def list_issued_licenses do
    Repo.all(IssuedLicense)
  end

  @doc """
  Gets a single issued_license.

  Raises `Ecto.NoResultsError` if the Issued license does not exist.

  ## Examples

      iex> get_issued_license!(123)
      %IssuedLicense{}

      iex> get_issued_license!(456)
      ** (Ecto.NoResultsError)

  """
  def get_issued_license!(id), do: Repo.get!(IssuedLicense, id)

  def get_issued_license_w_preload!(id) do
    IssuedLicense
    |> where([il], il.id == ^id)
    |> preload([:license, :holder])
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Creates a issued_license.

  ## Examples

      iex> create_issued_license(%{field: value})
      {:ok, %IssuedLicense{}}

      iex> create_issued_license(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_issued_license(attrs \\ %{}) do
    %IssuedLicense{}
    |> IssuedLicense.changeset(attrs)
    |> Repo.insert()
  end

  def change_issued_license_status(socket, attrs, record) do
    new_status = if attrs["status"] == "0", do: "Revoke", else: "Activate"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_status",
      IssuedLicense.changeset(record, %{
        status: attrs["status"],
        reason: attrs["reason"]
      })
    )
    |> Ecto.Multi.insert(
      "create_notification",
      fn %{"update_status" => license} ->
        UserNotifications.changeset(
          %UserNotifications{},
          %{
            "message" => "A Licence has been been #{new_status}d because: #{license.reason}",
            "page_url" => "/license/applications?id=#{license.id}",
            "type" => "LICENCES",
            "reason" => license.reason,
            "user_id" => license.holder_id,
            "role_id" => 8
          }
        )
      end
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} #{new_status}d a license: #{record.license.name}",
      "LICENSE STATUS",
      attrs,
      "Licence Applications"
    )
    |> Repo.transaction()
    |> case do
      # {:ok, record} ->
      #   Email.send_license_status_update(
      #     update_status.user.email,
      #     record["status"].license_name
      #   )

      #   {:ok, record}

      {:ok, record} ->
        #  IO.inspect(record, label: "Change License Status Attributes")
        Email.license_status_update(
          record["update_status"].holder.email,
          record["update_status"].license.name,
          record["update_status"].license.status
        )

        {:ok, record}

      {:error, error} ->
        {:error, error}

      {:error, _, error, _} ->
        {:error, error}
    end
  end

  def approve_licenses(user_id, name) do
    LicenseCertificates
    |> join(:left, [a], b in LicenseCertificates, on: a.certificate_id == b.id)
    |> where([a, b], a.user_id == ^user_id and a.approved == false and b.name == ^name)
    |> limit(1)
    |> Repo.exists?()
  end

  def get_license_by_id(id) do
    LicenseCertificates
    |> where([a], a.id == ^id)
    |> limit(1)
    |> Repo.one()
  end

  def get_license_account_by_application_id(application_id) do
    LicenseAccounts
    |> where([a], a.application_id == ^application_id)
    |> limit(1)
    |> Repo.one()
  end

  def get_certificates!() do
    LicenseCertificates
    |> where([a], a.status == 1)
    |> select([a], {a.name, a.id})
    |> Repo.all()
  end

  def update_issued_license(%IssuedLicense{} = issued_license, attrs) do
    issued_license
    |> IssuedLicense.changeset(attrs)
    |> Repo.update()
  end

  def list_issued_licenses_by_date(date) do
    from(il in IssuedLicense,
      where: fragment("DATE(?)", il.inserted_at) == ^date
    )
    |> Repo.all()
  end

  @doc """
  Deletes a issued_license.

  ## Examples

      iex> delete_issued_license(issued_license)
      {:ok, %IssuedLicense{}}

      iex> delete_issued_license(issued_license)
      {:error, %Ecto.Changeset{}}

  """
  def delete_issued_license(%IssuedLicense{} = issued_license) do
    Repo.delete(issued_license)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking issued_license changes.

  ## Examples

      iex> change_issued_license(issued_license)
      %Ecto.Changeset{data: %IssuedLicense{}}

  """
  def change_issued_license(%IssuedLicense{} = issued_license, attrs \\ %{}) do
    IssuedLicense.changeset(issued_license, attrs)
  end
end

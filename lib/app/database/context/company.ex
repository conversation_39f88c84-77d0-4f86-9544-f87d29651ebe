defmodule App.Companies do
  @moduledoc """
  Context module for managing companies.
  """

  import Ecto.Query, warn: false
  alias App.{Repo, Roles, Notification.UserNotifications}
  alias App.Accounts.Company

  alias App.Accounts.User
  alias App.Licenses.{LicenseMapping, NoticeOfChange}

  @doc """
  Returns the list of companies.
  """
  def list_companies do
    Repo.all(Company)
  end

  @doc """
  Gets a single company by ID.
  """
  def get_company!(id), do: Repo.get!(Company, id)

  def get_notice_of_change!(id) do
    NoticeOfChange
    |> where([a], a.id == ^id)
    |> preload([:user])
    |> limit(1)
    |> Repo.one()
  end

  def get_company_by_user!(id) do
    Company
    |> where([a], a.user_id == ^id and a.status == 1)
    |> limit(1)
    |> Repo.one()
  end

  def list_active_companies do
    Company
    |> where([a], a.status == 1)
    |> order_by([a], asc: a.name)
    |> select([a], {a.name, a.id})
    |> Repo.all()
  end

  def get_company_id_by_user!(id) do
    Company
    |> where([a], a.user_id == ^id)
    |> select([a], a.id)
    |> limit(1)
    |> Repo.one()
  end

  def search_associate!(nrc) do
    LicenseMapping
    |> where(
      [a],
      a.data["national_id"] == ^nrc and a.approval_status == false and a.status > 0
    )
    |> preload([:user, :license])
    |> Repo.all()
  end

  def search_single_assoc!(nrc) do
    LicenseMapping
    |> where(
      [a],
      a.data["national_id"] == ^nrc and a.approval_status == false and a.status > 0
    )
    |> preload([:user, :license])
    |> limit(1)
    |> Repo.one()
  end

  # def search_associate!(nrc) do
  #   LicenseMapping
  #   |> where(
  #     [a],
  #     a.data["national_id"] == ^nrc and a.approval_status == false and a.status > 0 and
  #       is_nil(a.associated_license_id)
  #   )
  #   |> preload([:user, :license])
  #   |> Repo.all()
  # end

  def get_main_application_id!(user_id) do
    LicenseMapping
    |> where([a], a.user_id == ^user_id and a.approval_status == false)
    |> select([a], a.id)
    |> limit(1)
    |> Repo.one()
  end

  def attach_application(_socket, attrs, application) do
   # IO.inspect application, label: "application Application!!!!!!!!!!"
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "attach",
      LicenseMapping.attach_changeset(application, %{
        associated_license_id: attrs["main_id"]
      })
    )

    |> Ecto.Multi.update(
      "attach_company",
      User.attach_changeset(application.user, %{
        company_id: attrs["company_id"]
      })
    )
#    |> Logs.Audit.create_system_log_session_live_multi(
#      socket,
#      "User: #{socket.assigns.current_user.email} attached an application: #{application.record_name} to their company.",
#      "APPLICATION ATTACHMENT",
#      attrs,
#      "Application Attachment"
#    )
    |> Repo.transaction()

  end

  def bulk_attach_applications(socket, attrs, entries) do
    multi =
      Enum.with_index(entries)
      |> Enum.reduce(Ecto.Multi.new(), fn {entry, idx}, multi ->
        application = search_single_assoc!(entry["col1"])

        Ecto.Multi.update(
          multi,
          {:attach, idx},
          LicenseMapping.attach_changeset(application, %{
            associated_license_id: attrs["main_id"]
          })
        )
        |> Ecto.Multi.update(
          {:attach_company, idx},
          User.attach_changeset(application.user, %{
            company_id: attrs["company_id"]
          })
        )
      end)

    multi
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} attached representatives (bulk) to their company.",
      "APPLICATION ATTACHMENT",
      attrs,
      "Application Attachment"
    )
    |> Repo.transaction(timeout: :infinity)
  end

  def submit_attach_request(socket, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "notice",
      NoticeOfChange.changeset(
        %NoticeOfChange{},
        %{
          "user_id" => socket.assigns.current_user.id,
          "company_id" => attrs["user"]["company_id"],
          "license_id" => attrs["license_id"],
          "data" => attrs
        }
      )
    )
    |> Ecto.Multi.insert(
      "create_notification",
      fn %{"notice" => notice} ->
        UserNotifications.changeset(
          %UserNotifications{},
          %{
            "message" => "A notice of change has been submitted for your review.",
            "page_url" => "/client/representatives/pending?id=#{notice.id}",
            "type" => "NOTICE OF CHANGE",
            "user_id" => socket.assigns.company.user_id,
            "role_id" => 8
          }
        )
      end
    )
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} submitted a notice of change.",
      "NOTICE OF CHANGES",
      attrs,
      "Notice of Changes"
    )
    |> Repo.transaction()
  end

  def approve_request(socket, attrs, record) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "approve",
      NoticeOfChange.changeset(record, %{
        "status" => attrs["status"]
      })
    )
    |> Ecto.Multi.update(
      "attach_company",
      User.attach_changeset(record.user, %{
        "company_id" => attrs["company_id"]
      })
    )
    |> Ecto.Multi.insert(
      "create_notification",
      UserNotifications.changeset(
        %UserNotifications{},
        %{
          "message" => "A notice of change has been approved.",
          "page_url" => "/",
          "type" => "NOTICE OF CHANGE",
          "user_id" => record.user_id,
          "role_id" => 8
        }
      )
    )
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} approved a notice of change.",
      "NOTICE OF CHANGES",
      attrs,
      "Notice of Changes"
    )
    |> Repo.transaction()
  end

  def decline_request(socket, attrs, record) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "decline",
      NoticeOfChange.changeset(record, %{
        "status" => attrs["status"]
      })
    )
    |> Ecto.Multi.insert(
      "create_notification",
      UserNotifications.changeset(
        %UserNotifications{},
        %{
          "message" => "A notice of change has been declined. Reason: #{attrs["reason"]}",
          "page_url" => "/",
          "type" => "NOTICE OF CHANGE",
          "user_id" => record.user_id,
          "role_id" => 8
        }
      )
    )
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} declined a notice of change.",
      "NOTICE OF CHANGES",
      attrs,
      "Notice of Changes"
    )
    |> Repo.transaction()
  end

  def list_company_representatives(company_id) do
    Company
    |> join(:left, [a], b in assoc(a, :user))
    |> where([a, b], b.company_id == ^company_id)
    |> Repo.all()
  end

  @doc """
  Creates a company.
  """
  def create_self_reg_company(attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "users",
      User.self_registration_insert_changeset(
        %User{},
        %{
          "auto_password" => "N",
          "role_id" => Roles.get_access_role_by_department(8, "Client"),
          "sex" => "FEMALE",
          "user_type" => "CLIENT",
          "registration_type" => "BUSINESS",
          "email" => attrs.email,
          "first_name" => attrs.name,
          "mobile" => attrs.mobile,
          "last_name" => "ACC",
          "password" => attrs.password
        }
      )
    )
    |> Ecto.Multi.insert(
      "company",
      fn %{"users" => user} ->
        Company.changeset(
          %Company{},
          %{
            "name" => attrs.name,
            "email" => attrs.email,
            "address" => attrs.address,
            "incorporation_date" => attrs.incorporation_date,
            "registration_number" => attrs.registration_number,
            "tpin" => attrs.tpin,
            "mobile" => attrs.mobile,
            "password" => attrs.password,
            "user_id" => user.id
          }
        )
      end
    )
    |> Repo.transaction()
  end

  @doc """
  Updates a company.
  """
  def update_company(%Company{} = company, attrs) do
    company
    |> Company.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a company.
  """
  def delete_company(%Company{} = company) do
    Repo.delete(company)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking company changes.
  """
  def change_company(%Company{} = company, attrs \\ %{}) do
    Company.changeset(company, attrs)
  end

  def self_change_company(attrs \\ %{}) do
    Company.changeset(%Company{}, attrs)
  end

  def client_change_company(attrs \\ %{}) do
    User.change_company_changeset(%User{}, attrs)
  end

  def client_change_company2(%User{} = user, attrs \\ %{}) do
    User.change_company_changeset(user, attrs)
  end
end

defmodule App.Utilities do
  @moduledoc """
  The Utilities context.
  """

  import Ecto.Query, warn: false

  alias App.{
    Repo,
    Notification.UserNotifications,
    Licenses.ApprovalLevels,
    Licenses.ConditionalLevels,
    Licenses.LicenseCertificates
  }

  alias Logs.Audit
  alias App.Maintenance.Committees

  alias App.Utilities.{ErrorCode, LicenceDraft}

  @doc """
  Returns the list of tbl_error_master.

  ## Examples

      iex> list_tbl_error_master()
      [%ErrorCode{}, ...]

  """
  def list_errors do
    ErrorCode
    |> preload([:maker, :checker])
    |> Repo.all()
  end

  @doc """
  Gets a single error_code.

  Raises `Ecto.NoResultsError` if the Error code does not exist.

  ## Examples

      iex> get_error_code!(123)
      %ErrorCode{}

      iex> get_error_code!(456)
      ** (Ecto.NoResultsError)

  """
  def get_error_code!(id), do: Repo.get!(<PERSON>rrorCode, id)

  @doc """
  Creates a error_code.

  ## Examples

      iex> create_error_code(%{field: value})
      {:ok, %ErrorCode{}}

      iex> create_error_code(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_error(%{assigns: assigns} = socket, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "error_code",
      ErrorCode.changeset(
        %ErrorCode{},
        %{
          "code" => attrs["code"],
          "error_desc" => attrs["error_desc"],
          "maker_id" => assigns.current_user.id
        }
      )
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} created an error code: #{attrs["code"]}",
      "ERROR CODE CREATION",
      attrs,
      "Error Codes"
    )
    |> Repo.transaction()
  end

  def update_error(socket, %ErrorCode{} = record, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_profile",
      ErrorCode.changeset(record, attrs)
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} updated an error code: #{attrs["code"]}",
      "ERROR CODE CREATION",
      attrs,
      "Error Codes"
    )
    |> Repo.transaction()
  end

  def delete_error(socket, attrs, record) do
    Ecto.Multi.new()
    |> Ecto.Multi.delete(:delete, record)
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} deleted a error code: #{attrs["code"]}",
      "ERROR CODE CREATION",
      attrs,
      "Error Codes"
    )
    |> Repo.transaction()

    # |> CustomContext.notify_subs(@topic, :status_change, __MODULE__)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking error_code changes.

  ## Examples

      iex> change_error_code(error_code)
      %Ecto.Changeset{source: %ErrorCode{}}

  """

  def change_error_code(%ErrorCode{} = error_code, attrs \\ %{}) do
    ErrorCode.changeset(error_code, attrs)
  end

  # Committees --->>>>>

  def get_committee!(id), do: Repo.get!(Committees, id)

  def create_committee(%{assigns: assigns} = socket, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "create",
      Committees.changeset(
        %Committees{},
        attrs
      )
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} created a commitee: #{attrs["name"]}",
      "COMMITTEE CREATION",
      attrs,
      "Committees"
    )
    |> Repo.transaction()
  end

  def update_committee(socket, %Committees{} = record, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update",
      Committees.changeset(record, attrs)
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} updated a committee: #{attrs["name"]}",
      "COMMITTEE UPDATE",
      attrs,
      "Committees"
    )
    |> Repo.transaction()
  end

  def change_committee_status(socket, attrs, record) do
    new_status = if attrs["status"] == 0, do: "Disable", else: "Enable"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_status",
      Committees.changeset(record, %{status: attrs["status"]})
    )
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} #{new_status}d a committee: #{record.name}",
      "COMMITTEE UPDATE",
      attrs,
      "Committees"
    )
    |> Repo.transaction()
  end

  def change_commitee(%Committees{} = committee, attrs \\ %{}) do
    Committees.changeset(committee, attrs)
  end

  def get_notification_by_user_id(user_id) do
    UserNotifications
    |> where([a], a.user_id == ^user_id and a.status == false)
    |> order_by(desc: :id)
    |> limit(5)
    |> Repo.all()
  end

  def get_notification_by_role_id(role_id, user_id) do
    UserNotifications
    |> where([a], a.role_id == ^role_id and ^user_id not in a.read_id)
    |> order_by(desc: :id)
    |> limit(5)
    |> Repo.all()
  end

  def get_notification!(id), do: Repo.get!(UserNotifications, id)

  def update_notification(
        %UserNotifications{} = notification,
        attrs,
        %{assigns: assigns} = _socket
      ) do
    check_id =
      if assigns.current_user.id in notification.read_id do
        notification.read_id
      else
        [assigns.current_user.id | notification.read_id]
      end

    notification
    |> UserNotifications.changeset(%{
      "status" => attrs["status"],
      "read_id" => check_id
    })
    |> Repo.update()
  end

  def read_all_notifications(records, attrs, %{assigns: assigns} = _socket) do
    records
    |> Enum.reduce(Ecto.Multi.new(), fn record, multi ->
      check_id =
        if assigns.current_user.id in record.read_id do
          record.read_id
        else
          [assigns.current_user.id | record.read_id]
        end

      Ecto.Multi.update(
        multi,
        "read_all_#{record.id}",
        UserNotifications.changeset(record, %{
          "status" => attrs["status"],
          "read_id" => check_id
        })
      )
    end)
    |> Repo.transaction()
  end

  def change_notification(%UserNotifications{} = notification, attrs \\ %{}) do
    UserNotifications.changeset(notification, attrs)
  end

  # Approval Levels --->>>>>

  def get_approval_level!(id), do: Repo.get!(ApprovalLevels, id)

  def get_approval_levels!() do
    ApprovalLevels
    |> where([a], a.status == 1)
    |> Repo.all()
  end

  def get_level_by_status_department!(status, department_id) do
    if department_id == 1 do
      true
    else
      ApprovalLevels
      |> where(
        [a],
        a.status == 1 and a.approval_status == ^status and a.department_id == ^department_id
      )
      |> limit(1)
      |> Repo.exists?()
    end
  end

  def get_level_by_status_role!(status, role_id) do
    if role_id == 1 do
      true
    else
      ApprovalLevels
      |> where(
        [a],
        a.status == 1 and a.approval_status == ^status and a.role_id == ^role_id
      )
      |> limit(1)
      |> Repo.exists?()
    end
  end

  def get_level_by_category_role!(category_id, status, role_id) do
    if role_id == 1 do
      true
    else
      ApprovalLevels
      |> where(
        [a],
        a.status == 1 and a.approval_status == ^status and a.role_id == ^role_id and
          a.categories_id == ^category_id
      )
      |> limit(1)
      |> Repo.exists?()
    end
  end

  def get_conditional_level_by_status_role!(status, role_id) do
    if role_id == 1 do
      true
    else
      ConditionalLevels
      |> where(
        [a],
        a.status == 1 and a.approval_status == ^status and a.role_id == ^role_id
      )
      |> limit(1)
      |> Repo.exists?()
    end
  end

  def condition_level_by_role!(status, role_id) do
    cond do
      role_id == 1 ->
        true

      true ->
        ConditionalLevels
        |> where(
          [a],
          a.status == 1 and a.approval_status == ^status and a.role_id == ^role_id
        )
        |> distinct([a], a.role_id)
        |> limit(1)
        |> Repo.exists?()
    end
  end

  def get_final_approval_level_status!() do
    ApprovalLevels
    |> order_by([a], desc: a.approval_status)
    |> limit(1)
    |> select([a], a.approval_status)
    |> Repo.one()
  end

  def get_approval_level_list() do
    ApprovalLevels
    |> where([a], a.status == 1)
    |> order_by([a], asc: a.approval_status)
    |> select([a], a.approval_status)
    |> Repo.all()
  end

  def get_approval_level_list_by_category(id) do
    ApprovalLevels
    |> where([a], a.status == 1 and a.categories_id == ^id)
    |> order_by([a], asc: a.approval_status)
    |> select([a], a.approval_status)
    |> Repo.all()
  end

  def get_conditional_approval_level_list() do
    ConditionalLevels
    |> where([a], a.status == 1)
    |> order_by([a], asc: a.approval_status)
    |> select([a], a.approval_status)
    |> Repo.all()
  end

  def get_approvals_by_department!(department_id) do
    ApprovalLevels
    |> where([a], a.department_id == ^department_id)
    |> Repo.all()
  end

  def get_current_level_role(status) do
    ApprovalLevels
    |> join(:left, [a], b in assoc(a, :role))
    |> where(
      [a],
      a.status == 1 and a.approval_status == ^status
    )
    |> select([a, b], b.name)
    |> limit(1)
    |> Repo.one()
  end

  def get_current_level_role_by_category(status, category_id) do
    ApprovalLevels
    |> join(:left, [a], b in assoc(a, :role))
    |> where(
      [a],
      a.status == 1 and a.approval_status == ^status and a.categories_id == ^category_id
    )
    |> select([a, b], b.name)
    |> limit(1)
    |> Repo.one()
  end

  def count_approval_levels() do
    ApprovalLevels
    |> where([a], a.status == 1)
    |> Repo.aggregate(:count, :id)
  end

  def approval_levels() do
    ApprovalLevels
    |> where([a], a.approval_status in [7, 8, 9])
    |> select([a], a.role_id)
    |> Repo.all()
  end

  def get_current_approval_level!(status, category) do
    ApprovalLevels
    |> where(
      [a],
      a.status == 1 and a.approval_status == ^status and a.categories_id == ^category
    )
    |> limit(1)
    |> Repo.one()
  end

  def create_approval_level(%{assigns: assigns} = socket, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "approval",
      ApprovalLevels.changeset(
        %ApprovalLevels{},
        attrs
      )
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} created an approval level: #{attrs["approval_status"]}",
      "APPROVAL LEVELS CREATE",
      attrs,
      "Approval Levels"
    )
    |> Repo.transaction()
  end

  def update_approval_level(socket, %ApprovalLevels{} = record, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_approval",
      ApprovalLevels.changeset(record, attrs)
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} updated an approval level: #{attrs["approval_status"]}",
      "APPROVAL LEVELS UPDATE",
      attrs,
      "Approval Levels"
    )
    |> Repo.transaction()
  end

  def change_level_status(socket, attrs, record) do
    new_status = if attrs["status"] == 0, do: "Disable", else: "Enable"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_status",
      ApprovalLevels.changeset(record, %{status: attrs["status"]})
    )
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} #{new_status}d an approval level: #{record.approval_status}",
      "APPROVAL LEVELS STATUS",
      attrs,
      "Approval Levels"
    )
    |> Repo.transaction()
  end

  def change_approval_levels(%ApprovalLevels{} = levels, attrs \\ %{}) do
    ApprovalLevels.changeset(levels, attrs)
  end

  ########### Conditional levels
  def get_conditional_approval_level!(id), do: Repo.get!(ConditionalLevels, id)

  def get_conditional_approval_levels!() do
    ConditionalLevels
    |> where([a], a.status == 1)
    |> Repo.all()
  end

  def get_condition_approval_level!(status) do
    ConditionalLevels
    |> where(
      [a],
      a.status == 1 and a.approval_status == ^status
    )
    |> limit(1)
    |> Repo.one()
  end

  def create_conditional_level(%{assigns: assigns} = socket, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "approval",
      ConditionalLevels.changeset(
        %ConditionalLevels{},
        attrs
      )
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} created an conditional approval level: #{attrs["approval_status"]}",
      "CONDITIONAL APPROVAL LEVELS CREATE",
      attrs,
      "Conditional Approval Levels"
    )
    |> Repo.transaction()
  end

  def update_conditional_level(socket, %ConditionalLevels{} = record, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_approval",
      ConditionalLevels.changeset(record, attrs)
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} updated a conditional approval level: #{attrs["approval_status"]}",
      "CONDITIONAL APPROVAL LEVELS UPDATE",
      attrs,
      "Conditional Approval Levels"
    )
    |> Repo.transaction()
  end

  def change_conditional_level_status(socket, attrs, record) do
    new_status = if attrs["status"] == 0, do: "Disable", else: "Enable"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_status",
      ConditionalLevels.changeset(record, %{status: attrs["status"]})
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} #{new_status}d a conditional approval level: #{record.approval_status}",
      "CONDITIONAL APPROVAL LEVELS STATUS",
      attrs,
      "Conditional Approval Levels"
    )
    |> Repo.transaction()
  end

  def change_conditional_approval_levels(%ConditionalLevels{} = levels, attrs \\ %{}) do
    ConditionalLevels.changeset(levels, attrs)
  end

  def get_licence_draft!(id), do: Repo.get!(LicenceDraft, id)

  def get_draft!(id) do
    LicenceDraft
    |> where([a], a.license_id == ^id)
    |> limit(1)
    |> Repo.one()
  end

  def get_certificate_draft!(id) do
    LicenseCertificates
    |> where([a], a.id == ^id)
    |> limit(1)
    |> Repo.one()
  end

  def get_licence_drafts_preload!(id) do
    LicenceDraft
    |> where([a], a.id == ^id)
    |> preload([:license])
    |> limit(1)
    |> Repo.one()
  end

  def change_licence_draft(attrs \\ %{}) do
    LicenceDraft.changeset(%LicenceDraft{}, attrs)
  end

  def change_licence_draft2(%LicenceDraft{} = licence_draft, attrs \\ %{}) do
    LicenceDraft.changeset(licence_draft, attrs)
  end

  def update_licence_drafts(%LicenceDraft{} = licence_draft, attrs) do
    licence_draft
    |> LicenceDraft.changeset(attrs)
    |> Repo.update()
  end

  def update_draft(%LicenceDraft{} = licence, attrs, socket) do
    Ecto.Multi.new()
    |> Ecto.Multi.update("licence_draft", LicenceDraft.changeset(licence, attrs))
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} Edited Licence Draft",
      "UPDATE",
      attrs,
      "Licence Draft Configuration"
    )
    |> Repo.transaction()
  end

  ### Certificates

  def get_certificates! do
    LicenseCertificates
    |> where([a], a.status == 1)
    |> select([a], {a.name, a.id})
    |> Repo.all()
  end

  def change_certificate_draft(attrs \\ %{}) do
    LicenseCertificates.changeset(%LicenseCertificates{}, attrs)
  end

  def change_certificate_draft2(%LicenseCertificates{} = licence_draft, attrs \\ %{}) do
    LicenseCertificates.changeset(licence_draft, attrs)
  end

  def update_certificate_drafts(%LicenseCertificates{} = licence_draft, attrs) do
    licence_draft
    |> LicenseCertificates.changeset(attrs)
    |> Repo.update()
  end

  def update_certificate_draft(%LicenseCertificates{} = licence, attrs, socket) do
    Ecto.Multi.new()
    |> Ecto.Multi.update("licence_certificates", LicenseCertificates.changeset(licence, attrs))
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} Edited Certificate Draft",
      "UPDATE",
      attrs,
      "Certificate Draft Configuration"
    )
    |> Repo.transaction()
  end
end

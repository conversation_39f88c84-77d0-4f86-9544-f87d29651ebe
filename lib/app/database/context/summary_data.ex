defmodule App.SummaryData do
  @moduledoc """
  The SummaryData context.
  """

  # App.SummaryData.checking_off_toggle()

  import Ecto.Query, warn: false
  alias App.Repo

  alias App.SummaryData.SummarisedData

  @doc """
  Returns the list of tbl_summarised_data.

  ## Examples

      iex> list_tbl_summarised_data()
      [%SummarisedData{}, ...]

  """
  def list_tbl_summarised_data do
    Repo.all(SummarisedData)
  end

  @doc """
  Gets a single summarised_data.

  Raises `Ecto.NoResultsError` if the Summarised data does not exist.

  ## Examples

      iex> get_summarised_data!(123)
      %SummarisedData{}

      iex> get_summarised_data!(456)
      ** (Ecto.NoResultsError)

  """
  def get_summarised_data!(id), do: Repo.get!(SummarisedData, id)

  @doc """
  Creates a summarised_data.

  ## Examples

      iex> create_summarised_data(%{field: value})
      {:ok, %SummarisedData{}}

      iex> create_summarised_data(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """

  # def create_summarised_data(attrs \\ %{}) do
  #   %SummarisedData{}
  #   |> SummarisedData.changeset(attrs)
  #   |> Repo.insert()
  # end

  def create_summarised_data(socket, attrs \\ %{}) do
    if MapSet.new(attrs["license_field_id"]) |> Enum.sort() ==
         MapSet.new(checking_off_toggle()) |> Enum.sort() do
      nil
    else
      attrs["license_field_id"]
      |> Enum.reduce(Ecto.Multi.new(), fn license_field_id, multi ->
        Ecto.Multi.run(multi, "record#{license_field_id}", fn _repo, _ ->
          {
            :ok,
            get_by_license_field_id_and_license_id(license_field_id, attrs["license_id"]) ||
              %SummarisedData{}
          }
        end)
        |> Ecto.Multi.insert_or_update("insert#{license_field_id}", fn changes ->
          SummarisedData.changeset(changes["record#{license_field_id}"], %{
            "license_id" => attrs["license_id"],
            "license_field_id" => license_field_id,
            "status" => 1
          })
        end)
      end)
      |> Ecto.Multi.update_all(
        "remove",
        fn _changes ->
          from(
            a in SummarisedData,
            where:
              a.license_id == ^attrs["license_id"] and
                a.license_field_id not in ^attrs["license_field_id"],
            update: [set: [status: 0]]
          )
        end,
        []
      )
      |> Logs.Audit.create_system_log_session_live_multi(
        socket,
        "User: #{socket.assigns.current_user.first_name} created license summary",
        "LICENSE MAINTENANCE",
        attrs,
        "license summary"
      )
      |> Repo.transaction()
    end
  end

  def get_by_license_field_id_and_license_id(id, license_id) do
    SummarisedData
    |> where([a], a.license_field_id == ^id and a.license_id == ^license_id)
    |> limit(1)
    |> Repo.one()
  end

  def checking_off_toggle() do
    SummarisedData
    |> where([a], a.status == ^1)
    |> select([a], %{
      id: a.id,
      license_field_id: a.license_field_id
    })
    |> Repo.all()
    |> Enum.map(& &1.license_field_id)
  end

  #    def get_licence_data_by_license_id_summary(license) do
  #    SummarisedData
  #    |> where([a, _b], a.license_id == ^license and a.status == ^1)
  #    |> select([a, b], %{
  #      id: a.id,
  #      license_id: a.license_id,
  #      # field_name: b.field_name,
  #      # field_label: b.field_label
  #    })
  #    # |> select([_, a], a.license_id)
  #    |> Repo.all()
  #  end

  #  def get_licence_data_by_license_id_summary(license) do
  #    SummarisedData
  #    |> join(:left, [a], b in LicenseFields, on: a.license_field_id == b.id)
  #      # |> join(:left, [a], b in LicenseFields, on: a.license_field_id == b.id)
  #    |> where([a, _b], a.license_field_id == ^license and a.status == ^1)
  #    |> select([a, b], %{
  #      id: a.id,
  #      license_id: a.license_id,
  #      field: a.license_field_id,
  #      field_name: b.field_name,
  #      field_label: b.field_label
  #    })
  #      # |> select([_, a], a.license_id)
  #    |> Repo.all()
  #  end

  def checking_license_field_id_by_license_id_summary(license_id) do
    SummarisedData
    |> where([a], a.license_id == ^license_id and a.status == ^1)
    |> select([a], a.license_field_id)
    |> Repo.all()
  end

  @doc """
  Updates a summarised_data.

  ## Examples

      iex> update_summarised_data(summarised_data, %{field: new_value})
      {:ok, %SummarisedData{}}

      iex> update_summarised_data(summarised_data, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_summarised_data(%SummarisedData{} = summarised_data, attrs) do
    summarised_data
    |> SummarisedData.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a summarised_data.

  ## Examples

      iex> delete_summarised_data(summarised_data)
      {:ok, %SummarisedData{}}

      iex> delete_summarised_data(summarised_data)
      {:error, %Ecto.Changeset{}}

  """
  def delete_summarised_data(%SummarisedData{} = summarised_data) do
    Repo.delete(summarised_data)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking summarised_data changes.

  ## Examples

      iex> change_summarised_data(summarised_data)
      %Ecto.Changeset{data: %SummarisedData{}}

  """
  def change_summarised_data(%SummarisedData{} = summarised_data, attrs \\ %{}) do
    SummarisedData.changeset(summarised_data, attrs)
  end
end

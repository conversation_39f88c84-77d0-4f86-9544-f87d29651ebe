defmodule App.Notifications.Messages do
  @moduledoc false
  alias Logs.Audit

  def session_end(message, reference),
    do: %{
      "message" => message,
      "success" => false,
      "status" => 700,
      "api_reference" => to_string(reference)
    }

  def success_message(message, data \\ %{}, reference)

  def success_message(message, data, api_logs) when is_struct(api_logs) do
    new_message =
      if data == %{} do
        %{
          "message" => message,
          "success" => true,
          "status" => 0,
          "api_reference" => to_string(api_logs.reference)
        }
      else
        %{
          "message" => message,
          "success" => true,
          "status" => 0,
          "data" => data,
          "api_reference" => to_string(api_logs.reference)
        }
      end

    api_log = Audit.api_log_update!(api_logs, new_message)
    Jason.decode!(api_log.response)
  end

  def success_message(message, data, reference) do
    if data == %{} do
      %{
        "message" => message,
        "success" => true,
        "status" => 0,
        "api_reference" => to_string(reference)
      }
    else
      %{
        "message" => message,
        "success" => true,
        "status" => 0,
        "data" => data,
        "api_reference" => to_string(reference)
      }
    end
  end

  def successs_message(message, data) do
    if data == %{} do
      %{status: 0, message: message, success: true}
    else
      %{status: 0, message: message, success: true, data: data}
    end
  end

  def error_message(message, api_logs) when is_struct(api_logs) do
    api_log =
      Audit.api_log_update!(api_logs, %{
        "message" => message,
        "success" => false,
        "status" => 1,
        "api_reference" => to_string(api_logs.reference)
      })

    Jason.decode!(api_log.response)
  end

  def error_message(message, reference),
    do: %{
      "message" => message,
      "success" => false,
      "status" => 1,
      "api_reference" => to_string(reference)
    }
end

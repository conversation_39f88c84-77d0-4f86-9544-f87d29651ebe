defmodule App.SetUp.Setting do
  @moduledoc false
  alias App.{Repo, Settings.Setting, Settings.SettingsConfig}

  def init do
    [
      %{
        name: "send_emails",
        description: "This tells the system whether to send E-Mails or not",
        status: false
      },
      %{
        name: "send_text_messages",
        description: "This tells the system whether to send text messages or not.",
        status: true
      }
    ]
    |> Enum.each(fn data ->
      Repo.insert!(%Setting{
        name: data[:name],
        description: data[:description],
        status: data[:status]
      })
    end)
  end

  def init1 do
    [
      %{
        name: "user_inactive_session_notification",
        field_type: "number",
        value_type: "minutes",
        value: "60",
        select_details: Jason.encode!(["minutes"]),
        description: "Notify the user when the page is inactive in x interval"
      },
      %{
        name: "inactive_user_model_timeout",
        field_type: "number",
        value_type: "minutes",
        value: "90",
        select_details: Jason.encode!(["minutes"]),
        description: "Notify the user when the page is inactive in x interval"
      },
      %{
        name: "login_failed_attempts_max",
        field_type: "number",
        value_type: "times",
        value: "3",
        select_details: Jason.encode!(["times"]),
        description: "Notify the user when the page is inactive in x interval"
      },
      %{
        name: "user_session_service_interval",
        field_type: "number",
        value_type: "minutes",
        value: "30",
        description: "Drop all active LNSW transactions within the specified period on time",
        select_details: Jason.encode!(["minutes"])
      },
      %{
        name: "sandbox_evaluation_percentage",
        field_type: "number",
        value_type: "%",
        value: "75",
        select_details: Jason.encode!(["%"]),
        description:
          "This is the percentage of the total score that is required to pass the sandbox evaluation"
      }
    ]
    |> Enum.each(fn data ->
      Repo.insert!(%SettingsConfig{
        name: data[:name],
        description: data[:description],
        field_type: data[:field_type],
        value_type: data[:value_type],
        value: data[:value],
        select_details: data[:select_details]
      })
    end)
  end
end

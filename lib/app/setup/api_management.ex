defmodule App.SetUp.ApiManagement do
  @moduledoc false
  alias App.{Repo, Management.ApiManagement, Management}

  def init do
    [
      %{
        name: "EMAIL",
        type: "SERVER",
        key: "vcct gqlp mqvc gwdb",
        base_url: "http://************:9000/api/send/email",
        data: %{
          host: "smtp.gmail.com",
          port: "587",
          cc: "<EMAIL>",
          sender: "PBS SMS Gateway",
          user: "<EMAIL>"
        }
      },
      %{
        name: "CLIENT",
        type: "SERVER",
        key: "",
        base_url: "http://127.0.0.1:5000",
        data: %{
          get_file: "/file/read",
          remove_file: "/file/remove"
        }
      },
      %{
        name: "SMS",
        type: "SERVER",
        key: "",
        base_url: "http://127.0.0.1:4000/api/send/sms/"
      },
      %{
        name: "API",
        type: "SERVER",
        key: "",
        base_url: "http://127.0.0.1:7000",
        data: %{
          scheduled_single: "/internal/scheduled/single",
          scheduled_broadcast: "/internal/scheduled/broadcast",
          scheduled_multicast: "/internal/scheduled/multicast",
          single: "/internal/single",
          broadcast: "/internal/broadcast",
          multicast: "/internal/multicast"
        }
      },
      %{
        name: "ZED_MOBILE",
        type: "NOTIFICATION",
        key: "8KMmNROmh7+DSrjAd8u59m8cr0s3O0UkczqQHJAIV08=",
        base_url: "https://102.68.113.126:28900/api/SubmitSMS",
        data: %{
          UId: "102"
        }
      },
      %{
        name: "MTN",
        type: "NOTIFICATION",
        key: "8KMmNROmh7+DSrjAd8u59m8cr0s3O0UkczqQHJAIV08=",
        base_url: "https://cpassmessaging.mtn.zm",
        data: %{
          token: ":32147/v1/accounts/users/login",
          send: "/api/v1/sms/send",
          category: "TXN",
          country: "ZM",
          email: "<EMAIL>",
          email_key: "account@123456"
        }
      },
      %{
        name: "INFOBIP",
        type: "NOTIFICATION",
        key: "pwd",
        base_url: "https://api.infobip.com/sms/1/text/single",
        data: %{
          username: "username"
        }
      },
      %{
        name: "ZAMTEL",
        type: "NOTIFICATION",
        key: "6ea677d51df057729ce8a3d7182f93f3",
        base_url:
          "https://bulksms.zamtel.co.zm/api/v2.1/action/send/api_key/{{key}}/contacts/[{{mobiles}}]/senderId/{{sender_id}}/message/{{message}}"
      },
      %{
        name: "MOCEAN",
        type: "NOTIFICATION",
        key: "Password123",
        base_url: "https://rest.moceanapi.com/rest/2/sms",
        data: %{
          sercet: "Password123",
          format: "json"
        }
      },
      %{
        name: "ProbasePayment",
        type: "Payments",
        key: "X9nY^4%J/@*kE9W[n#5M",
        base_url: "https://paymentservices.probasegroup.com",
        data: %{
          api_key:
            "fPJuFXQNPWh3F7j6T2Gjx6JMCUM2FUDRz2nXrp2gBFgagb27FdFaCzpvAvZnYTQx3QV7Sd64WJkbJ46SqQ2EkqDnq8Fe5QrZwCjtUDU2N3MBSY=",
          redirect_endpoint: "/pbs/payments",
          transaction_lookup: "/pbs/Payments/Api/V1/TransactionLookup",
          redirect_method: "POST",
          redirect_system_id: "Super Merchant",
          redirect_callback: "https://smsclient.sms.probasegroup.com/payment/response",
          redirect_source_institution: "PBS SMS",
          api_endpoint: "/pbs/Payments/Api/V1/ProcessTransaction"
        }
      }
    ]
    |> Enum.each(fn data ->
      if api = Management.get_api_management_by_name_and_type(data[:name], data[:type]) do
        Management.update_api_management(
          api,
          %{
            access_point: data[:access_point],
            key: data[:key],
            base_url: data[:base_url],
            data: data[:data]
          }
        )
      else
        Repo.insert!(%ApiManagement{
          name: data[:name],
          type: data[:type],
          access_point: data[:access_point],
          key: data[:key],
          base_url: data[:base_url],
          data: data[:data]
        })
      end
    end)
  end
end

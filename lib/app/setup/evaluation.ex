defmodule App.SetUp.Evaluation do
  @moduledoc false
  alias App.Repo

  alias App.Evaluation.{EvaluationCriteria}

  defp run_seeds_db(schema), do: Repo.insert!(schema)

  def init do
    run_seeds_db(%EvaluationCriteria{
      name: "Inclusiveness",
      type: "SANDBOX PRINCIPLES",
      section: 1,
      description:
        "The Sandbox shall implore Participants to develop innovations that promote financial inclusion in the context of, universal access and usage, good quality & reliability, affordability, scalability, sustainability, and impact (among others).",
      status: 1
    })

    run_seeds_db(%EvaluationCriteria{
      name: "Consumer Protection",
      type: "SANDBOX PRINCIPLES",
      section: 1,
      description:
        "The Sandbox shall require Participants to put in place appropriate measures to safeguard the interests of consumers of the proposed innovation.",
      status: 1
    })

    run_seeds_db(%EvaluationCriteria{
      name: "Ethics & Integrity",
      type: "SANDBOX PRINCIPLES",
      section: 1,
      description:
        "The Sandbox shall require Participants to have a good code of conduct, a strong corporate governance framework and compliance with legal / regulatory requirements as well as conform with international best practise standards. The Sandbox Participants must have credibility and integrity and must not use the platform as a tool to circumvent legal / regulatory requirements.",
      status: 1
    })

    run_seeds_db(%EvaluationCriteria{
      name: "Transparency",
      type: "SANDBOX PRINCIPLES",
      section: 1,
      description:
        "The Sandbox shall require Participants to adopt an open communication policy, to be accountable and to disclose material information when necessary. This will enhance collaboration between the Commission and Participants as well as ensure effective monitoring of the Sandbox process.",
      status: 1
    })

    run_seeds_db(%EvaluationCriteria{
      name: "Scope of Sandbox",
      type: "ELIGIBILITY CRITERIA",
      section: 2,
      description:
        "The Participant must ensure that the proposed innovation is within the scope of the Sandbox, that is, must be in alignment with the Sandbox objectives and Sandbox principles.",
      status: 1
    })

    run_seeds_db(%EvaluationCriteria{
      name: "Impactful Innovation",
      type: "ELIGIBILITY CRITERIA",
      section: 2,
      description:
        "The Participant must ensure the proposed innovation is significantly different from existing products currently on offer in the capital markets. The innovation must demonstrate the ability to promote alternative finance / investment as well as deepen the capital markets.",
      status: 1
    })

    run_seeds_db(%EvaluationCriteria{
      name: "Consumer Benefit",
      type: "ELIGIBILITY CRITERIA",
      section: 2,
      description:
        "The Participant must ensure the proposed innovation directly or indirectly benefits potential clients, that is, must be customer-centric, of high quality, affordable and safe to use et cetera. The participant must ensure potential clients are aware that the proposed innovation is undergoing testing in the Sandbox.",
      status: 1
    })

    run_seeds_db(%EvaluationCriteria{
      name: "Consumer Protection and Risk Management",
      type: "ELIGIBILITY CRITERIA",
      section: 2,
      description:
        "The Participant must ensure that adequate resources and measures are in place to safeguard the interests of consumers during the test trial.
This should among other things include;
(i)  internal processes and controls that will ensure only well informed consumers take up the proposed innovation;
(ii)  proper dispute resolution mechanisms; and
(iii) a compensation plan to compensate persons who suffer pecuniary loss occasioned by any default of a Participant or any employee
of a Participant, in the course of, or in connection with, the test trial, being a loss in relation to any money, securities or other property which was entrusted to, or received by, the Participant or an employee for, and on behalf of, the Participant.",
      status: 1
    })

    run_seeds_db(%EvaluationCriteria{
      name: "Test Plan",
      type: "ELIGIBILITY CRITERIA",
      section: 2,
      description:
        "The Participant must ensure that the proposed innovation is at an advanced stage ‘ready to test stage’, which may among other things entail;
(i)  having a detailed test plan outlining timelines for execution of key Sandbox stages / milestones;(ii) having a reporting framework; and
(iii) having clear and concise methods of testing and controls necessary during the test trial.",
      status: 1
    })

    run_seeds_db(%EvaluationCriteria{
      name: "Exit Plan",
      type: "ELIGIBILITY CRITERIA",
      section: 2,
      description:
        "The Participant must ensure that a detailed exit plan is
in place. The exit plan shall outline in detail, the future development or deployment of the proposed innovation commercially. In the event of an unsuccessful test trial, the exit plan shall outline in detail, how consumers will be protected from any resultant loss.",
      status: 1
    })

    run_seeds_db(%EvaluationCriteria{
      name: "Institutional Reputation",
      type: "ELIGIBILITY CRITERIA",
      section: 2,
      description:
        "The Participant must have a team with requisite skills, good ethical conduct and the firm must prove to be commercially viable.",
      status: 1
    })

    run_seeds_db(%EvaluationCriteria{
      name: "Resourcing",
      type: "ELIGIBILITY CRITERIA",
      section: 2,
      description:
        "The Participant must have adequate operational resourcing such as human and financial (among others) to warrant participation in the Sandbox.",
      status: 1
    })

    run_seeds_db(%EvaluationCriteria{
      name: "Data privacy and protection",
      type: "ELIGIBILITY CRITERIA",
      section: 2,
      description:
        "The Participant shall be required to have systems in place to ensure data such as client information, reports and correspondence with the Commission is stored in a secure manner and is well maintained.",
      status: 1
    })

    run_seeds_db(%EvaluationCriteria{
      name: "Data Privacy and Protection",
      type: "ELIGIBILITY CRITERIA",
      section: 2,
      description:
        "The Participant shall be required to have systems in place to ensure data such as client information, reports and correspondence with the Commission is stored in a secure manner and is well maintained.",
      status: 1
    })

    run_seeds_db(%EvaluationCriteria{
      name: "Application Fee",
      type: "ELIGIBILITY CRITERIA",
      section: 2,
      description:
        "The Applicant shall be required to pay a non- refundable application fee as prescribed. For Participants that successfully deploy their innovation, the application fee shall act as an advance payment towards any incumbent costs related to obtaining a full licence from the Commission. Where the Commission considers it appropriate in the exceptional circumstances of a particular case, the Commission may in its discretion waive payment of all or part of the fee which would otherwise be payable under this clause.",
      status: 1
    })
  end
end

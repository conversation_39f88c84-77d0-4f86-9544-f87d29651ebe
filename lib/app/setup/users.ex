defmodule App.SetUp.Users do
  @moduledoc false
  alias App.{Users, Accounts.Company, Repo}
  use AppWeb, :file_function

  defp run_seeds_db(schema), do: Repo.insert!(schema)

  def init(
        {developer, administrator, finance, debt_officer, manager, director, assistant,
         lc_committee_role, board_role, client, individual_inv, sec_ex, dealer, inv_adviser, rep, ros,
         license_admin, evaluator}
      ) do
    [
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "Probase",
        "last_name" => "Limited",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "MALE",
        "auto_password" => "N",
        "user_type" => "STAFF",
        "role_id" => developer.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "Admin",
        "last_name" => "Limited",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "MALE",
        "auto_password" => "N",
        "user_type" => "STAFF",
        "role_id" => administrator.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "Finance",
        "last_name" => "Limited",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "FEMALE",
        "auto_password" => "N",
        "user_type" => "STAFF",
        "role_id" => finance.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "Analyst",
        "last_name" => "Limited",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "FEMALE",
        "auto_password" => "N",
        "user_type" => "STAFF",
        "role_id" => debt_officer.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "Manager",
        "last_name" => "Limited",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "FEMALE",
        "auto_password" => "N",
        "user_type" => "STAFF",
        "role_id" => manager.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "TEST",
        "last_name" => "Limited",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "FEMALE",
        "auto_password" => "N",
        "user_type" => "CLIENT",
        "registration_type" => "BUSINESS",
        "role_id" => client.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "TEST 2",
        "last_name" => "Limited",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "MALE",
        "auto_password" => "N",
        "user_type" => "CLIENT",
        "registration_type" => "INDIVIDUAL",
        "role_id" => client.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "TEST 3",
        "last_name" => "Limited",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "MALE",
        "auto_password" => "N",
        "user_type" => "CLIENT",
        "registration_type" => "INDIVIDUAL",
        "role_id" => client.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "TEST 2",
        "last_name" => "Limited",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "MALE",
        "auto_password" => "N",
        "user_type" => "CLIENT",
        "registration_type" => "INDIVIDUAL",
        "role_id" => client.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "TEST 2",
        "last_name" => "Limited",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "MALE",
        "auto_password" => "N",
        "user_type" => "CLIENT",
        "registration_type" => "INDIVIDUAL",
        "role_id" => client.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "Assistant",
        "last_name" => "Director",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "MALE",
        "auto_password" => "N",
        "user_type" => "STAFF",
        "role_id" => assistant.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "Director",
        "last_name" => "Limited",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "MALE",
        "auto_password" => "N",
        "user_type" => "STAFF",
        "role_id" => director.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "LC",
        "last_name" => "Limited",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "MALE",
        "auto_password" => "N",
        "user_type" => "STAFF",
        "role_id" => lc_committee_role.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "Board",
        "last_name" => "Limited",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "MALE",
        "auto_password" => "N",
        "user_type" => "STAFF",
        "role_id" => board_role.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "License",
        "last_name" => "Admin",
        "other_name" => "ZM",
        "mobile" => "************",
        "status" => "A",
        "sex" => "MALE",
        "auto_password" => "N",
        "user_type" => "STAFF",
        "role_id" => license_admin.id
      },
      %{
        "count" => 1,
        "email" => "<EMAIL>",
        "password" => "Dev@1234",
        "first_name" => "Eva",
        "last_name" => "Evaluator",
        "other_name" => "ZM",
        "mobile" => "260776016064",
        "status" => "A",
        "sex" => "FEMALE",
        "auto_password" => "N",
        "user_type" => "EVALUATOR",
        "role_id" => evaluator.id
      }
    ]
    |> Enum.sort(&(&1["count"] <= &2["count"]))
    |> Enum.each(fn data ->
      if !Users.get_user_by_email(data["email"]) do
        Users.registration_seed_user(data)
      end
    end)

    # Create the company

    run_seeds_db(%Company{
      address: "123 Main St, Lusaka, Zambia",
      status: 1,
      name: "Client INC",
      email: "<EMAIL>",
      incorporation_date: ~D[2023-04-04],
      mobile: "260963042208",
      registration_number: "*********",
      tpin: "*********0",
      user_id: 6
    })

    {sec_ex, dealer, inv_adviser, individual_inv, rep, ros}
  end
end

defmodule App.SetUp.MessageDrafts do
  @moduledoc false
  alias Notification.Notification.MessageDraft
  alias App.Repo

  # App.SetUp.MessageDrafts.init()

  def init do
    [
      %{
        message: """
        <h4 class="ql-align-left"> Dear {{client_name}},
         your application has been Submitted.
         <br>
         <strong>Note: </strong>
         <br>
         1.Successful submission does not mean acceptance of application.
         <br>
         2.The application is only deemed complete and accepted upon approval by a Manager. </h4>

        """,
        service_type: "LICENCE_REGISTRATION"
      },
      %{
        message: """
        <h4 class="ql-align-left">Dear {{client_name}},
         Your License for: {{record_name}} has been declined.<br>
         <strong>Reason:</strong> {{reason}}.
         Kindly login to make the necessary corrections.</h4>
        """,
        service_type: "DECLINE_NOTIFICATION"
      },
      %{
        message: """
        <h4 class="ql-align-left">Dear {{client_name}},
         your license has been {{status}}</h4>
        """,
        service_type: "LICENCE_STATUS_UPDATE"
      },
      %{
        message: """
        <h4 class="ql-align-left">Dear {{client_name}},
        Your applicant, {{applicant_name}}, has made corrections to the fields you requested. Kindly log in to review them.</h4>
        """,
        service_type: "SEND_CORRECTION_EMAIL"
      },
      %{
        message: """
        <h4 class="ql-align-left">Dear {{client_name}},
        you have been invited to evaluate and score the sanbox application: {{record_name}}.

        Kindly use the link below to access the application and provide your evaluation.
        <br>
        {{url}}</h4>
        """,
        service_type: "EVALUATOR_INVITATION"
      },


       %{
        message: """
        <h4 class="ql-align-left">Dear {{client_name}},
        you have been assigned with a condition: {{condition_name}}.

        Kindly log in to review them.
        <br>
        </h4>
        """,
        service_type: "SEND_CONDITION_NOTIFICATION"
      },
    ]
    |> Enum.each(fn data ->
      Repo.insert!(%MessageDraft{
        message: data[:message],
        service_type: data[:service_type]
      })
    end)
  end
end

defmodule App.SetUp.Roles do
  @moduledoc """
  Seeds department roles and access roles with proper many-to-many relationships
  """
  alias App.{
    Repo,
    Roles,
    Roles.AccessRoles,
    Roles.DepartmentRoles,
    Roles.Permission
  }

  def init do
    # Get all permission codes mapped to IDs
    permission_map =
      Permission
      |> Repo.all()
      |> Enum.map(&{&1.code, &1.id})
      |> Map.new()

    # Helper function to get permission IDs from codes
    # get_permission_ids = fn codes ->
    #   Enum.map(codes, &Map.get(permission_map, &1))
    #   |> Enum.reject(&is_nil/1)
    # end

    # Create Department Roles
    dev =
      create_or_update_department_role(
        %{
          name: "Dev",
          description: "System Developers",
          editable: false,
          user_interface: false,
          system_gen: true
        },
        Map.keys(permission_map)
      )

    # All permissions

    admin =
      create_or_update_department_role(
        %{
          name: "Admin",
          description: "System Administrators",
          editable: false,
          user_interface: true,
          system_gen: true
        },
        Map.keys(permission_map)
      )

    # All permissions

    dmsd =
      create_or_update_department_role(
        %{
          name: "DMSD",
          description: "Debt and Market Surveillance Department",
          editable: true,
          user_interface: true,
          system_gen: true
        },
        [
          "applications-new_license-view",
          "applications-returned_applicant-view",
          "applications-submitted_to_supervisor-view",
          "applications-submitted_to_subordinate-view",
          "applications-returned_from_supervisor-view",
          "applications-submitted_to_lc-view",
          "applications-submitted_to_board-view",
          "applications-returned_from_supervisor-view",
          "applications-processed-view",
          "license_details-view",
          "review-application",
          "conditional_application-view",
          "issued_licenses-view",
          "steps_mapping-view",
          "conditional_tracking-view",
          "conditions-modify",
          "conditions-view",
          "conditions-status",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses",
          "client_user_view",
          "evaluators-view",
          "evaluators-upload",
          "evaluators-modify"
        ]
      )

    fnad =
      create_or_update_department_role(
        %{
          name: "FNAD",
          description: "Finance and Administration Department",
          editable: true,
          user_interface: true,
          system_gen: true
        },
        [
          "applications-new_license-view",
          "applications-submit_market_operations-view",
          "applications-returned_applicant-view",
          "license_details-view",
          "finance-view",
          "conditional_application-view",
          "license_issued-view",
          "steps_mapping-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses"
        ]
      )

    ceof =
      create_or_update_department_role(
        %{
          name: "CEOF",
          description: "CEO's Office",
          editable: true,
          user_interface: true,
          system_gen: true
        },
        [
          "applications-new_license-view",
          "license_details-view",
          "conditional_application-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses"
        ]
      )

    dfa =
      create_or_update_department_role(
        %{
          name: "DFA",
          description: "Department of Finance and Administration",
          editable: true,
          user_interface: true,
          system_gen: true
        },
        [
          "applications-new_license-view",
          "license_details-view",
          "conditional_application-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses"
        ]
      )

    dels =
      create_or_update_department_role(
        %{
          name: "DELS",
          description: "Department of Enforcement and Legal Services",
          editable: true,
          user_interface: true,
          system_gen: true
        },
        [
          "applications-new_license-view",
          "license_details-view",
          "conditional_application-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses"
        ]
      )

    licensee =
      create_or_update_department_role(
        %{
          name: "Licensee",
          description:
            "An individual or entity that holds a license to operate in the capital markets",
          editable: true,
          user_interface: true,
          system_gen: true
        },
        [
          "dashboard-license_registration",
          "license_registration",
          "settings-view",
          "client_applications-view",
          "conditional_tracking-view"
        ]
      )

    sandbox_committee =
      create_or_update_department_role(
        %{
          name: "Sandbox Committee",
          description: "Sandbox Committee Members",
          editable: false,
          user_interface: true,
          system_gen: true
        },
        [
          "applications-new_license-view",
          "license_details-view",
          "conditional_application-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "evaluators-view",
          "evaluators-upload",
          "evaluators-modify",
          "sandbox-evaluate"
        ]
      )

    # Create Access Roles
    all_permission_codes = Map.keys(permission_map)

    developer =
      create_or_update_access_role(
        %{
          name: "Developers",
          editable: false,
          description: "All rights on one role",
          system_gen: true,
          user_interface: false,
          department_role_id: dev.id
        },
        all_permission_codes
      )

    admin_permissions =
      Enum.filter(
        all_permission_codes,
        &(&1 not in [
            "finance-view",
            "api_logs-view",
            "error-view",
            "dashboard-health_checker",
            "dashboard-server_stats",
            "dashboard-traffic_trends"
          ])
      )

    administrator =
      create_or_update_access_role(
        %{
          name: "Admin",
          editable: true,
          description: "System administrator role",
          system_gen: true,
          user_interface: true,
          department_role_id: admin.id
        },
        admin_permissions
      )

    _ceo_office =
      create_or_update_access_role(
        %{
          name: "CEO",
          editable: true,
          description: "CEO office role",
          system_gen: true,
          user_interface: true,
          department_role_id: ceof.id
        },
        [
          "applications-new_license-view",
          "license_details-view",
          "conditional_application-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses"
        ]
      )

    debt_officer =
      create_or_update_access_role(
        %{
          name: "Analyst",
          editable: true,
          description: "DMSD Analyst role",
          system_gen: true,
          user_interface: true,
          department_role_id: dmsd.id
        },
        [
          "applications-new_license-view",
          "applications-submitted_to_supervisor-view",
          "applications-returned_applicant-view",
          "applications-returned_from_supervisor-view",
          "applications-processed-view",
          "license_details-view",
          "review-application",
          "conditional_application-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-view",
          "conditions-status",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses"
        ]
      )

    manager =
      create_or_update_access_role(
        %{
          name: "Manager",
          editable: true,
          description: "DMSD Manager role",
          system_gen: true,
          user_interface: true,
          department_role_id: dmsd.id
        },
        [
          "applications-new_license-view",
          "applications-submitted_to_supervisor-view",
          "applications-returned_from_supervisor-view",
          "applications-processed-view",
          "license_details-view",
          "review-application",
          "conditional_application-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses",
          "evaluators-view",
          "evaluators-upload",
          "evaluators-modify"
        ]
      )

    director =
      create_or_update_access_role(
        %{
          name: "Director",
          editable: true,
          description: "DMSD Director role",
          system_gen: true,
          user_interface: true,
          department_role_id: dmsd.id
        },
        [
          "applications-new_license-view",
          "license_details-view",
          "conditional_application-view",
          "applications-processed-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses"
        ]
      )

    assistant =
      create_or_update_access_role(
        %{
          name: "Assistant Director",
          editable: true,
          description: "DMSD Assistant Director role",
          system_gen: true,
          user_interface: true,
          department_role_id: dmsd.id
        },
        [
          "applications-new_license-view",
          "license_details-view",
          "conditional_application-view",
          "applications-processed-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses"
        ]
      )

    finance =
      create_or_update_access_role(
        %{
          name: "Finance",
          editable: true,
          description: "Finance role",
          system_gen: true,
          user_interface: true,
          department_role_id: fnad.id
        },
        [
          "applications-new_license-view",
          "applications-submit_market_operations-view",
          "applications-returned_applicant-view",
          "finance-view",
          "conditional_application-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses"
        ]
      )

    _dfa_role =
      create_or_update_access_role(
        %{
          name: "DFA",
          editable: true,
          description: "DFA role",
          system_gen: true,
          user_interface: true,
          department_role_id: dfa.id
        },
        [
          "applications-new_license-view",
          "license_details-view",
          "conditional_application-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses"
        ]
      )

    _dels_role =
      create_or_update_access_role(
        %{
          name: "DELS",
          editable: true,
          description: "DELS role",
          system_gen: true,
          user_interface: true,
          department_role_id: dels.id
        },
        [
          "applications-new_license-view",
          "license_details-view",
          "conditional_application-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses"
        ]
      )

    client =
      create_or_update_access_role(
        %{
          name: "Client",
          editable: true,
          description: "An individual or entity that has been assigned a role of Client",
          system_gen: true,
          user_interface: true,
          department_role_id: licensee.id
        },
        [
          "dashboard-license_registration",
          "license_registration",
          "settings-view",
          "client_applications-view",
          "conditional_tracking-view"
        ]
      )

    # Create empty access roles for licensee types
    sec_ex =
      create_or_update_access_role(
        %{
          name: "Securities Exchange",
          editable: true,
          description: "Securities Exchange role",
          system_gen: true,
          user_interface: true,
          department_role_id: licensee.id
        },
        [
          "dashboard-license_registration",
          "license_registration",
          "settings-view",
          "client_applications-view",
          "conditional_tracking-view"
        ]
      )

    dealer =
      create_or_update_access_role(
        %{
          name: "Dealer",
          editable: true,
          description: "Dealer role",
          system_gen: true,
          user_interface: true,
          department_role_id: licensee.id
        },
        [
          "dashboard-license_registration",
          "license_registration",
          "settings-view",
          "client_applications-view",
          "conditional_tracking-view"
        ]
      )

    individual_inv =
      create_or_update_access_role(
        %{
          name: "Individual Investment",
          editable: true,
          description:
            "An individual or entity that has been assigned a role of Individual Investment",
          system_gen: true,
          user_interface: true,
          department_role_id: licensee.id
        },
        []
      )

    inv_adviser =
      create_or_update_access_role(
        %{
          name: "Investment Adviser Licence",
          editable: true,
          description:
            "An individual or entity that has been assigned a role of Individual Investment",
          system_gen: true,
          user_interface: true,
          department_role_id: licensee.id
        },
        [
          "dashboard-license_registration",
          "license_registration",
          "settings-view",
          "client_applications-view",
          "conditional_tracking-view"
        ]
      )

    rep =
      create_or_update_access_role(
        %{
          name: "Representative",
          editable: true,
          description:
            "An individual or entity that has been assigned a role of Individual Investment",
          system_gen: true,
          user_interface: true,
          department_role_id: licensee.id
        },
        [
          "dashboard-license_registration",
          "license_registration",
          "settings-view",
          "client_applications-view",
          "conditional_tracking-view"
        ]
      )

    ros =
      create_or_update_access_role(
        %{
          name: "Record of Securities",
          editable: true,
          description:
            "An individual or entity that has been assigned a role of Individual Investment",
          system_gen: true,
          user_interface: true,
          department_role_id: licensee.id
        },
        [
          "dashboard-license_registration",
          "license_registration",
          "settings-view",
          "client_applications-view",
          "conditional_tracking-view"
        ]
      )

    # npr = create_or_update_access_role(
    #   %{
    #     name: "Notice of Place of Record",
    #     editable: true,
    #     description: "An individual or entity that has been assigned a role of Individual Investment",
    #     system_gen: true,
    #     user_interface: true,
    #     department_role_id: licensee.id
    #   },
    #   []
    # )

    # ncpb = create_or_update_access_role(
    #   %{
    #     name: "Notice of Change of Place of Business",
    #     editable: true,
    #     description: "An individual or entity that has been assigned a role of Individual Investment",
    #     system_gen: true,
    #     user_interface: true,
    #     department_role_id: licensee.id
    #   },
    #   []
    # )

    # ncb = create_or_update_access_role(
    #   %{
    #     name: "Notification of Cessation of Business",
    #     editable: true,
    #     description: "An individual or entity that has been assigned a role of Individual Investment",
    #     system_gen: true,
    #     user_interface: true,
    #     department_role_id: licensee.id
    #   },
    #   []
    # )

    # ncrp = create_or_update_access_role(
    #   %{
    #     name: "Notice of Change of Reps Principal",
    #     editable: true,
    #     description: "An individual or entity that has been assigned a role of Individual Investment",
    #     system_gen: true,
    #     user_interface: true,
    #     department_role_id: licensee.id
    #   },
    #   []
    # )

    lc_committee_role =
      create_or_update_access_role(
        %{
          name: "LC Admin",
          editable: true,
          description: "An individual that is a member of the LC Committee",
          system_gen: true,
          user_interface: true,
          department_role_id: dmsd.id
        },
        [
          "applications-new_license-view",
          "license_details-view",
          "conditional_application-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses"
        ]
      )

    board_role =
      create_or_update_access_role(
        %{
          name: "Board Admin",
          editable: true,
          description: "An individual that is a member of the board",
          system_gen: true,
          user_interface: true,
          department_role_id: dmsd.id
        },
        [
          "applications-new_license-view",
          "license_details-view",
          "conditional_application-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses"
        ]
      )

    _sandbox_committee_role =
      create_or_update_access_role(
        %{
          name: "Admin",
          editable: true,
          description: "An individual that is a member of the sandbox committee",
          system_gen: true,
          user_interface: true,
          department_role_id: sandbox_committee.id
        },
        [
          "applications-new_license-view",
          "license_details-view",
          "conditional_application-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses"
        ]
      )

    license_admin =
      create_or_update_access_role(
        %{
          name: "License Admin",
          editable: true,
          description: "An individual that is responsible for the license",
          system_gen: true,
          user_interface: true,
          department_role_id: admin.id
        },
        [
          "applications-new_license-view",
          "license_details-view",
          "conditional_application-view",
          "license_issued-view",
          "conditional_tracking-view",
          "conditions-modify",
          "settings-view",
          "dashboard-pending_applications",
          "dashboard-total_dealers",
          "dashboard-total_clients",
          "dashboard-total_licenses",
          "conditions-view",
          "conditions-status",
          "client_user_view"
        ]
      )

    evaluator =
      create_or_update_access_role(
        %{
          name: "Evaluator",
          editable: true,
          description: "An individual that is a member of the evaluation committee",
          system_gen: true,
          user_interface: true,
          department_role_id: sandbox_committee.id
        },
        [
          "applications-new_license-view",
          "license_details-view",
          "conditional_application-view",
          "conditional_tracking-view",
          "settings-view",
          "sandbox-evaluate"
        ]
      )

    {developer, administrator, finance, debt_officer, manager, director, assistant,
     lc_committee_role, board_role, client, sec_ex, dealer, inv_adviser, individual_inv, rep, ros,
     license_admin, evaluator}
  end

  defp create_or_update_department_role(attrs, permission_codes) do
    # Find or create the department role
    department_role =
      case Repo.get_by(DepartmentRoles, name: attrs.name) do
        nil ->
          {:ok, role} = Roles.create_department_role(attrs)
          role

        existing ->
          {:ok, role} = Roles.update_department_role(existing, attrs)
          role
      end

    # Get permission IDs from codes
    permission_ids =
      Permission
      |> Repo.all()
      |> Enum.filter(&(&1.code in permission_codes))
      |> Enum.map(& &1.id)

    # Update permissions using the context function
    Roles.update_department_role_permissions(department_role, permission_ids)
  end

  defp create_or_update_access_role(attrs, permission_codes) do
    # Find or create the access role
    access_role =
      case Repo.get_by(AccessRoles,
             name: attrs.name,
             department_role_id: attrs.department_role_id
           ) do
        nil ->
          {:ok, role} = Roles.create_access_role(attrs)
          role

        existing ->
          {:ok, role} = Roles.update_access_role(existing, attrs)
          role
      end

    # Get permission IDs from codes
    permission_ids =
      Permission
      |> Repo.all()
      |> Enum.filter(&(&1.code in permission_codes))
      |> Enum.map(& &1.id)

    # Update permissions using the context function
    Roles.update_access_role_permissions(access_role, permission_ids)
  end
end

defmodule App.SetUp.ApprovalLevels do
  alias App.Licenses.ApprovalLevels
  alias App.Repo

  defp run_seeds_db(schema), do: Repo.insert!(schema)

  def init do
    # LICENSING
    run_seeds_db(%ApprovalLevels{
      approval_status: 1,
      status: 1,
      department_id: 4,
      role_id: 8,
      categories_id: 3
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 2,
      status: 1,
      department_id: 3,
      role_id: 4,
      count_down: 1,
      genarate_summary: 1,
      categories_id: 3
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 3,
      status: 1,
      department_id: 3,
      role_id: 5,
      categories_id: 3
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 4,
      status: 1,
      department_id: 3,
      role_id: 7,
      categories_id: 3
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 5,
      status: 1,
      department_id: 3,
      role_id: 6,
      categories_id: 3
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 6,
      status: 1,
      department_id: 3,
      role_id: 18,
      categories_id: 3,
      condition_tracking: 1
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 7,
      status: 1,
      department_id: 3,
      role_id: 19,
      categories_id: 3
    })

    # SANDBOX

    run_seeds_db(%ApprovalLevels{
      approval_status: 1,
      status: 1,
      department_id: 4,
      role_id: 8,
      categories_id: 5
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 2,
      status: 1,
      department_id: 3,
      role_id: 4,
      categories_id: 5
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 3,
      status: 1,
      department_id: 3,
      count_down: 1,
      genarate_summary: 1,
      evaluation: 1,
      role_id: 5,
      categories_id: 5
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 4,
      status: 1,
      department_id: 9,
      role_id: 22,
      categories_id: 5
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 5,
      status: 1,
      department_id: 3,
      role_id: 5,
      categories_id: 5
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 6,
      status: 1,
      department_id: 3,
      role_id: 6,
      categories_id: 5
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 7,
      status: 1,
      department_id: 3,
      role_id: 18,
      categories_id: 5,
      condition_tracking: 1
    })
  end
end

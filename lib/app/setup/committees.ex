defmodule App.SetUp.Committees do
  alias App.Maintenance.Committees
  alias App.Repo

  defp run_seeds_db(schema), do: Repo.insert!(schema)

  def init do
    run_seeds_db(%Committees{
      name: "Licencing Committee",
      description: "Licencing Committee (LC)",
      status: 1
    })

    run_seeds_db(%Committees{
      name: "Marketting Transaction Committee",
      description: "Marketting Transaction Committee (MTC)",
      status: 1
    })
  end
end

defmodule App.Http.ResponseFormat do
  def normal(response) do
    case response do
      {:ok, %HTTPoison.Response{body: body, status_code: status_code}} ->
        case status_code do
          200 ->
            {:ok, <PERSON>.decode!(body)}

          201 ->
            {:ok, <PERSON>.decode!(body)}

          _ ->
            {:error, "Failed with the status code #{status_code}"}
        end

      {:error, %HTTPoison.Error{reason: :etimedout}} ->
        {:error, "Service is currently unavailable"}

      {:error, %HTTPoison.Error{reason: message}} ->
        {:error, message}
    end
  end

  def any(response) do
    case response do
      {:ok, _} ->
        :ok

      {:error, _} ->
        :error
    end
  end

  def pbs_bills(response) do
    case response do
      {:ok, %HTTPoison.Response{body: body, status_code: status_code}} ->
        case status_code do
          200 ->
            {:ok, Jason.decode!(body)}

          202 ->
            {:ok, Jason.decode!(body)}

          _ ->
            {:error, "Failed with the status code #{status_code}"}
        end

      {:error, %HTTPoison.Error{reason: :etimedout}} ->
        {:error, "Service is currently unavailable"}

      {:error, %HTTPoison.Error{reason: message}} ->
        {:error, message}
    end
  end

  def pbs_gateway(response) do
    case response do
      {:ok, %HTTPoison.Response{body: body, status_code: status_code}} ->
        case status_code do
          200 ->
            {:ok, Jason.decode!(body)}

          201 ->
            {:ok, Jason.decode!(body)}

          401 ->
            {:ok, Jason.decode!(body)}

          _ ->
            {:error, "Failed with the status code #{status_code}"}
        end

      {:error, %HTTPoison.Error{reason: :etimedout}} ->
        {:error, "Service is currently unavailable", %{}}

      {:error, %HTTPoison.Error{reason: message}} ->
        {:error, message, %{}}
    end
  end

  def normal_no_jason(response) do
    case response do
      {:ok, %HTTPoison.Response{body: body, status_code: status_code}} ->
        case status_code do
          200 -> {:ok, body}
          _ -> {:error, "Something went wrong, please try again later"}
        end

      {:error, %HTTPoison.Error{reason: :etimedout}} ->
        {:error, "Endpoint is currently unavailable"}

      {:error, %HTTPoison.Error{reason: message}} ->
        {:error, message}
    end
  end
end

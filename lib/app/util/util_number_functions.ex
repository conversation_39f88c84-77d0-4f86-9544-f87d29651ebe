defmodule App.Util.NumberFunctions do
  @moduledoc false

  def currency(value, unit \\ "", precision \\ 2) do
    if to_string(value) == "N/A" or value == nil do
      value
    else
      to_string(value)
      |> Number.Currency.number_to_currency(unit: unit, precision: precision)
    end
  end

  def convert_to_int(alpha) do
    {nun, _} = Integer.parse(alpha)
    nun
  end

  def convert_to_float(alpha) do
    case String.trim(alpha) do
      "" ->
        %{Message: "Float Needed"}

      _else ->
        {nun, _} = Float.parse(alpha)
        nun
    end
  end

  def convert_to_float1(alpha) do
    {nun, _} = Float.parse(alpha)
    nun
  end

  def convert_to_boolean(alpha) do
    target = String.downcase(alpha)

    cond do
      target == "true" || target == "yes" || target == "on" -> true
      target == "false" || target == "no" || target == "off" -> false
      true -> nil
    end
  end
end

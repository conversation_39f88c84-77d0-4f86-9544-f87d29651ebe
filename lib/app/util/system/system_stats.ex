defmodule SystemStats do
  def get_system_stats do
    case :os.type() do
      {:unix, :linux} ->
        StatsLinux.memory_stats()
        |> Map.merge(StatsLinux.cpu_stats())
        |> Map.merge(StatsLinux.storage_stats())
        |> Map.merge(%{
          os: StatsLinux.os_info()
        })

      {:win32, _} ->
        StatsWindows.memory_stats()
        |> Map.merge(StatsWindows.cpu_stats())
        |> Map.merge(StatsWindows.storage_stats())
        |> Map.merge(%{
          os: StatsWindows.os_info()
        })

      _ ->
        {:error, "Unsupported OS"}
    end
  end

  def bytes_to_unit(bytes, unit) do
    case unit do
      "TB" -> Float.round(bytes / (1024 * 1024 * 1024 * 1024), 2)
      "GB" -> Float.round(bytes / (1024 * 1024 * 1024), 2)
      "MB" -> Float.round(bytes / (1024 * 1024), 2)
      "KB" -> Float.round(bytes / 1024, 2)
      "B" -> bytes
      _ -> 0
    end
  end
end

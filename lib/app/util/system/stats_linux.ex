defmodule StatsLinux do
  def memory_stats do
    with {:ok, meminfo} <- File.read("/proc/meminfo") do
      # Extract memory details
      total_memory = extract_value(meminfo, "MemTotal")
      free_memory = extract_value(meminfo, "MemFree")
      buffers = extract_value(meminfo, "Buffers")
      cached = extract_value(meminfo, "Cached")

      # Calculate used memory
      used_memory = total_memory - (free_memory + buffers + cached)

      %{
        total_memory: round_to_gb(total_memory),
        used_memory: round_to_gb(used_memory)
      }
    else
      _ -> {:error, "Failed to read memory stats"}
    end
  end

  defp extract_value(meminfo, key) do
    meminfo
    |> String.split("\n")
    |> Enum.find(fn line -> String.starts_with?(line, key) end)
    |> case do
      nil ->
        0

      line ->
        line
        |> String.split()
        |> Enum.at(1)
        |> String.to_integer()
    end
  end

  # Convert KB to GB and round to 1 decimal place
  defp round_to_gb(kb) do
    # 1 GB = 1024 * 1024 KB
    gb = kb / 1_048_576
    Float.round(gb, 1)
  end

  def cpu_stats do
    with {:ok, stat_data} <- File.read("/proc/stat") do
      cpu_line =
        stat_data
        |> String.split("\n")
        |> Enum.find(fn line -> String.starts_with?(line, "cpu ") end)

      case cpu_line do
        nil ->
          %{
            total_cpu_cores: 0,
            used_cpu_cores: 0
          }

        line ->
          cpu_times =
            line
            |> String.split()
            |> Enum.drop(1)
            |> Enum.map(&String.to_integer(&1))

          # Total time (sum of all CPU times)
          total_time = Enum.sum(cpu_times)

          # Idle time (time the CPU is idle)
          idle_time = Enum.at(cpu_times, 3)

          # Calculate CPU usage as a percentage
          used_time = total_time - idle_time
          cpu_usage = round_to_percentage(used_time, total_time)

          %{
            total_cpu_cores: :erlang.system_info(:logical_processors),
            used_cpu_cores: cpu_usage
          }
      end
    else
      _ ->
        %{
          total_cpu_cores: 0,
          used_cpu_cores: 0
        }
    end
  end

  # Convert CPU usage to percentage
  defp round_to_percentage(used_time, total_time) do
    usage = used_time / total_time * 100
    Float.round(usage, 1)
  end

  def storage_stats do
    case System.cmd("df", ["-h", "--total"]) do
      {output, 0} ->
        case output
             |> String.split("\n")
             |> Enum.at(-2) do
          nil -> default_storage_stats()
          line -> parse_storage_data(line)
        end

      _ ->
        default_storage_stats()
    end
  end

  defp format_bytes(bytes) do
    cond do
      # TB
      bytes > 1024 * 1024 * 1024 * 1024 ->
        {Float.round(bytes / (1024 * 1024 * 1024 * 1024), 2), "TB"}

      # GB
      bytes > 1024 * 1024 * 1024 ->
        {Float.round(bytes / (1024 * 1024 * 1024), 2), "GB"}

      # MB
      bytes > 1024 * 1024 ->
        {Float.round(bytes / (1024 * 1024), 2), "MB"}

      # KB
      bytes > 1024 ->
        {Float.round(bytes / 1024, 2), "KB"}

      true ->
        {bytes, "B"}
    end
  end

  defp parse_storage_data(storage_line) do
    case String.split(storage_line) do
      [_, used, available, total, _percent, _mount] ->
        # Get initial values
        {used_value, used_unit} = parse_size(used)
        {total_value, total_unit} = parse_size(total)

        # Convert both to the same unit (using the larger unit)
        {final_used, final_total, final_unit} =
          normalize_units(used_value, used_unit, total_value, total_unit)

        %{
          used_storage: final_used,
          used_unit: final_unit,
          total_storage: final_total,
          total_unit: final_unit,
          storage_usage_percentage: parse_usage_percent(available, total)
        }

      _ ->
        default_storage_stats()
    end
  end

  defp default_storage_stats do
    %{
      used_storage: 0,
      used_unit: "B",
      total_storage: 0,
      total_unit: "B",
      storage_usage_percentage: 0
    }
  end

  defp parse_size(size) do
    # This regex captures decimal or whole numbers and unit (e.g., "98.5G" or "949G")
    case Regex.run(~r/([\d.]+)([A-Za-z]+)/, size) do
      [_, value, unit] ->
        value =
          case String.contains?(value, ".") do
            true -> String.to_float(value)
            false -> String.to_integer(value)
          end

        # Convert to bytes first
        bytes =
          case unit do
            "K" -> value * 1024
            "M" -> value * 1024 * 1024
            "G" -> value * 1024 * 1024 * 1024
            "T" -> value * 1024 * 1024 * 1024 * 1024
            _ -> 0
          end

        format_bytes(bytes)

      _ ->
        {0, "B"}
    end
  end

  defp normalize_units(used, used_unit, total, total_unit) do
    # Convert both values to bytes
    used_bytes = to_bytes(used, used_unit)
    total_bytes = to_bytes(total, total_unit)

    # Determine the best unit based on the larger value
    {_larger_value, unit} = format_bytes(max(used_bytes, total_bytes))

    # Convert both values to the chosen unit
    used_converted = SystemStats.bytes_to_unit(used_bytes, unit)
    total_converted = SystemStats.bytes_to_unit(total_bytes, unit)

    {used_converted, total_converted, unit}
  end

  defp parse_usage_percent(available, total) do
    with [_, available_value, _] <- Regex.run(~r/([\d.]+)([A-Za-z]+)/, available),
         [_, total_value, _] <- Regex.run(~r/([\d.]+)([A-Za-z]+)/, total),
         {available_float, _} <- Float.parse(available_value),
         {total_float, _} <- Float.parse(total_value),
         true <- total_float > 0 do
      used_percentage = 100 - available_float * 100 / total_float
      Float.round(used_percentage, 1)
    else
      _ -> 0.0
    end
  end

  defp to_bytes(value, unit) do
    case unit do
      "TB" -> value * 1024 * 1024 * 1024 * 1024
      "GB" -> value * 1024 * 1024 * 1024
      "MB" -> value * 1024 * 1024
      "KB" -> value * 1024
      "B" -> value
      _ -> 0
    end
  end

  # Linux OS info using /etc/os-release
  def os_info do
    case File.read("/etc/os-release") do
      {:ok, content} ->
        os_name =
          content
          |> String.split("\n")
          |> Enum.find(fn line -> String.starts_with?(line, "PRETTY_NAME") end)
          |> String.split("=")
          |> List.last()
          |> String.trim("\"")

        os_name

      _ ->
        "Unknown"
    end
  end
end

defmodule App.Util.DateCounter do
  @moduledoc false

  def get_month_words_plus_year_full(month, year) do
    [
      "January #{year}",
      "February #{year}",
      "March #{year}",
      "April #{year}",
      "May #{year}",
      "June #{year}",
      "July #{year}",
      "August #{year}",
      "September #{year}",
      "October #{year}",
      "November #{year}",
      "December #{year}"
    ]
    |> Enum.at(month - 1)
  end

  def get_month_words(month) do
    [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December"
    ]
    |> Enum.at(month - 1)
  end

  def get_month_words_plus_year(month, year) do
    [
      "Jan #{year}",
      "Feb #{year}",
      "Mar #{year}",
      "Apr #{year}",
      "May #{year}",
      "Jun #{year}",
      "Jul #{year}",
      "Aug #{year}",
      "Sep #{year}",
      "Oct #{year}",
      "Nov #{year}",
      "Dec #{year}"
    ]
    |> Enum.at(month - 1)
  end

  def get_day_of_the_month(day) do
    [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday"
    ]
    |> Enum.at(day - 1)
  end

  def dates_in_a_week(date \\ Timex.now()) do
    Date.range(Date.beginning_of_week(date, :sunday), Date.end_of_week(date, :sunday))
    |> Enum.uniq_by(& &1.day)
  end

  def dates_in_a_month(date \\ Timex.now()) do
    Date.range(Date.beginning_of_month(date), Date.end_of_month(date))
    |> Enum.uniq_by(& &1.day)
  end

  def months_in_half_a_year do
    Date.beginning_of_month(Timex.now())
    |> Date.add(-151)
    |> Date.range(Timex.now())
    |> Enum.uniq_by(& &1.month)
  end

  def months_in_full_a_year do
    Date.beginning_of_month(Timex.now())
    |> Date.add(-360)
    |> Date.range(Timex.now())
    |> Enum.uniq_by(& &1.month)
  end

  def time_in_a_day() do
    [
      {~T[00:00:00], ~T[03:00:59]},
      {~T[03:01:00], ~T[06:00:59]},
      {~T[06:01:00], ~T[09:00:59]},
      {~T[09:01:00], ~T[12:00:59]},
      {~T[12:01:00], ~T[15:00:59]},
      {~T[15:01:00], ~T[18:00:59]},
      {~T[18:01:00], ~T[21:00:59]},
      {~T[21:01:00], ~T[23:59:59]}
    ]
  end

  def to_two_places(var) do
    to_string(var)
    |> String.pad_leading(2, "0")
  end

  def today_time_with_day do
    {{year, month, day}, {hour, minute, second}} = :calendar.local_time()
    get_month_words_plus_year_full(month, year)

    "#{get_today_day_in_words()} #{day} #{get_month_words_plus_year(month, year)} #{to_two_places(hour)}:#{to_two_places(minute)}:#{to_two_places(second)}"
  end

  def convert_map_to_string_map(map),
    do:
      Enum.map(map, fn {k, v} -> {to_string(k), v} end)
      |> Enum.into(%{})

  def convert_map_to_atomic_map(map),
    do:
      Enum.map(map, fn {k, v} -> {String.to_atom(k), v} end)
      |> Enum.into(%{})

  def get_today_day_in_words do
    {{year, month, day}, _} = :calendar.local_time()

    :calendar.day_of_the_week(year, month, day)
    |> get_day_of_the_month()
  end

  def seconds_to_time(second) do
    try do
      {_h, m, s} = :calendar.seconds_to_time(second)
      "#{String.pad_leading("#{m}", 2, "0")}:#{String.pad_leading("#{s}", 2, "0")}"
    rescue
      _ -> "00:00"
    end
  end

  def get_by_filter(categories, filter) do
    case filter do
      "daily" ->
        categories
        |> Enum.map(
          &"#{get_day_of_the_month(:calendar.day_of_the_week(&1.year, &1.month, &1.day))} #{&1.day}"
        )

      "weekly" ->
        categories
        |> Enum.map(fn date ->
          {year, week} = :calendar.iso_week_number({date.year, date.month, date.day})
          "Week #{week} - (#{year})"
        end)

      "monthly" ->
        categories
        |> Enum.map(&get_month_words_plus_year(&1.month, &1.year))
    end
  end

  def add_interval_to_next_data(session_validity_in_days, period) do
    now_time = NaiveDateTime.utc_now()

    case period do
      "second" -> NaiveDateTime.add(now_time, -session_validity_in_days)
      "minute" -> NaiveDateTime.add(now_time, -session_validity_in_days * 60)
      "hour" -> NaiveDateTime.add(now_time, -session_validity_in_days * 3600)
      "day" -> NaiveDateTime.add(now_time, -session_validity_in_days * 86400)
      "week" -> NaiveDateTime.add(now_time, -session_validity_in_days * 604_800)
    end
  end
end

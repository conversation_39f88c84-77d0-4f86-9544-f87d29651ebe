defmodule ParamValidator do
  @moduledoc false
  import Plug.Conn
  alias <PERSON><PERSON><PERSON>
  alias App.Notifications.Messages

  def validate(params, schema) do
    params
    |> Skooma.valid?(schema)
    |> case do
      {:error, message} ->
        {:error, Messages.error_message(Enum.join(message, " "), :os.system_time())}

      :ok ->
        :ok
    end
  end

  def validate(conn, params, schema) do
    params
    |> Skooma.valid?(schema)
    |> case do
      {:error, message} ->
        {:error, conn |> put_status(:bad_request),
         Messages.error_message(message, :os.system_time())}

      :ok ->
        {:ok, conn}
    end
  end
end

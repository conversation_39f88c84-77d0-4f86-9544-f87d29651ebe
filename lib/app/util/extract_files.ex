defmodule App.Util.Extract do
  def validate_excel_csv_file(file) do
    case Path.extname(file) do
      ext when ext in ~w(.xlsx .XLSX .xls .XLS) -> :excel
      ext when ext in ~w(.csv .CSV) -> :csv
      _ -> :error
    end
  end

  def extract_csv(path) do
    {
      :ok,
      File.stream!(path)
      |> CSV.decode!(headers: false)
      |> Enum.map(fn b ->
        Enum.with_index(b, 1)
        |> Enum.reduce(%{}, fn {data, index}, new_map ->
          Map.merge(new_map, %{"col#{index}" => to_string(data)})
        end)
      end)
    }
  end

  def extract_xlsx_details(path) do
    case Xlsxir.multi_extract(path, 0, false, extract_to: :memory) do
      {:ok, id} ->
        {
          :ok,
          Xlsxir.get_list(id)
          |> Enum.reject(&Enum.empty?/1)
          |> Enum.map(fn x ->
            Enum.with_index(x, 1)
            |> Enum.reduce(%{}, fn {data, index}, new_map ->
              Map.merge(new_map, %{"col#{index}" => james_found(data)})
            end)
          end)
        }

      {:error, _reason} ->
        {:error, "Valid file format"}
    end
  end

  defp james_found(item) when is_tuple(item), do: Date.from_erl!(item)

  defp james_found(item), do: to_string(item)

  def xlsx_count(path) do
    case Xlsxir.multi_extract(path, 0, false, extract_to: :memory) do
      {:ok, id} -> Xlsxir.get_info(id, :rows)
      {:error, _reason} -> {:error, "Valid file format"}
    end
  end

  def xlsx_count_cols(path) do
    case Xlsxir.multi_extract(path, 0, false, extract_to: :memory) do
      {:ok, id} -> Xlsxir.get_info(id, :cols)
      {:error, _reason} -> {:error, "Invalid file format"}
    end
  end

  def add_index(list) do
    Enum.with_index(list)
    |> List.flatten()
    |> Enum.map(fn {data, index} ->
      Map.merge(data, %{"key" => index})
    end)
  end

  def nil_filter(cards) do
    Enum.map(cards, fn x ->
      if x["col1"] == nil do
        nil
      else
        Enum.reduce(x, %{}, fn {col, value}, new_map ->
          Map.merge(new_map, %{col => value})
        end)
      end
    end)
    |> Enum.filter(&(!is_nil(&1)))
  end

  def prepend_zeros_to_pan(list, params, index \\ 1) do
    case params["prepend_zeros"] == "true" do
      true ->
        Enum.map(list, fn item ->
          Map.merge(item, %{"col#{index}" => String.pad_leading(item["col#{index}"], 16, "0")})
        end)

      false ->
        Enum.filter(list, &(!is_nil(&1)))
    end
  end

  def validate_excel_and_csv(path) do
    validate_excel_csv_file(path)
    |> case do
      :csv ->
        file = extract_csv(path)

        {
          :ok,
          with {:ok, file2} <- file do
            Enum.at(file2, 0)
            |> Map.to_list()
            |> Enum.count()
          end,
          file
        }

      :excel ->
        {
          :ok,
          xlsx_count_cols(path),
          extract_xlsx_details(path)
        }

      :error ->
        {:error, :start, "Invalid file format. Ensure that your file is valid Spreadsheet."}
    end
  end

  def finalize_process(check, _path, success_message \\ "success", failed_message \\ "failed") do
    total_count = Enum.count(check)

    success_list =
      Enum.map(check, fn x ->
        if x.status == 1 do
          x
        end
      end)
      |> Enum.filter(&(!is_nil(&1)))

    fail_list =
      Enum.map(check, fn x ->
        if x.status == 0 do
          x
        end
      end)
      |> Enum.filter(&(!is_nil(&1)))

    success_rows = Enum.count(check, fn x -> x.status == 1 end)
    fail_rows = Enum.count(check, fn x -> x.status == 0 end)

    case success_rows == total_count do
      true ->
        {:ok, :info,
         %{
           success: success_rows,
           failed: fail_rows,
           total: total_count,
           success_list: Jason.encode!(success_list),
           fail_list: Jason.encode!(fail_list)
         }, success_message}

      false ->
        {:ok, :error,
         %{
           success: success_rows,
           failed: fail_rows,
           total: total_count,
           success_list: Jason.encode!(success_list),
           fail_list: Jason.encode!(fail_list)
         }, failed_message}
    end
  end
end

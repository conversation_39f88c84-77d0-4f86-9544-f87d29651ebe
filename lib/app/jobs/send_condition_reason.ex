defmodule App.Jobs.JobConditionSendReason do
  import Ecto.Query, warn: false
  # App.Jobs.JobConditionSendReason.get_application_id(1)


  # App.Jobs.JobConditionTracking.get_condition_id(1)
  alias App.{
    Repo,
    Licenses.LicenseMapping,
    LicenseConditions,
    Licenses.LicenseConditionsMapping
    }

    alias  App.Accounts.User


  def query_license_mapping_by_id(id) do
    LicenseConditionsMapping
    |> join(:left, [a], b in User, on: a.added_by_id == b.id)
    |> join(:left, [a,b], c in LicenseMapping, on: b.id == c.license_id)
    |> where([a], a.application_id == ^id)
    |> select([a,b,c], %{
      condition: a,
      license: b,
      usermapping: c
    })
    |> limit(1)
    |> Repo.one()
  end

  def get_application_id(id) do

    LicenseConditionsMapping
    |> where([a], a.application_id == ^id.condition.application_id)
    |> limit(1)
    |> Repo.one()

 end

def init do
  LicenseConditions.get_license_expiring_reason()
  |> Enum.map(fn map ->
    case map.comments do
      nil -> nil
      _->   send_license_expiring_reason(map.application_id, map.expiring_date)

    end
   end)

  end


  def send_license_expiring_reason(license_id, _expiring_date) do
    license_id = query_license_mapping_by_id(license_id)
    application_id = get_application_id(license_id)
    Ecto.Multi.new()
    |> Ecto.Multi.update(:update, LicenseConditionsMapping.changeset(application_id, %{
      expiring_status: "REASON_SENT",
    }))
    |> Repo.transaction()
    |> case do
      {:ok, _records} ->
        Task.start(fn ->
          App.Send.Email.client_send_email_to_admin(license_id.license.email, license_id.license.first_name <> " " <>  license_id.license.last_name, license_id.condition.expiring_date, license_id.condition.responsibility, license_id.condition.comments, license_id.usermapping.record_name)
        end)
        {:ok, "License condition updated successfully"}

      {:error, _reason} ->
        {:error, "Failed to update license condition"}

      {:error, _, error, _} ->
        {:error, error}
    end


  end




  end

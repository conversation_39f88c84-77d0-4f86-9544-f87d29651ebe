defmodule App.Jobs.LicenceApprovals do
  alias App.Licenses

  def init do
    main_license_list = Licenses.get_licenses_w_association!()
    _license_list = Licenses.get_pending_licenses_without_assoc!()

    Licenses.get_pending_licences!()
    |> Enum.map(fn application ->
      cond do
        is_nil(application.associated_license_id) and
          is_nil(application.license.associated_license_id) and
            application.license_id not in main_license_list ->
          Licenses.process_license_update(application)

        # !is_nil(application.associated_license_id) and
        #     application.associated_license_id not in license_list ->
        #   IO.inspect(:runnig_cond_2)
        #   Licenses.process_license_update(application)

        Map.has_key?(application.data, "representative_number") and
            String.to_integer(application.data["representative_number"]) ==
              Licenses.count_associated_applications(
                application.id,
                application.status
              ) ->
          Licenses.process_license_update(application)

        !is_nil(application.associated_license_id) ->
          dependency_resolved(application)

        true ->
          :pending
      end
    end)
  end

  defp dependency_resolved(application) do
    if Licenses.check_dependency(
         application.associated_license_id,
         application.status
       ) do
      Licenses.process_license_update(application)
    else
      :pending
    end
  end
end

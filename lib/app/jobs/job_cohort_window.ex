defmodule App.Jobs.JobCohortWindow do
  import Ecto.Query, warn: false

  alias App.Licenses.License

  alias App.{
    Repo,
    Licenses
  }

  def init do
    # Fetch all licenses that require cohort and have a cohort window
    Licenses.get_open_cohort_licenses!()
    |> Enum.filter(fn license ->
      # Only process licenses where the end date has passed
      case license.window_end_date do
        nil -> false
        end_date -> Date.compare(end_date, Date.utc_today()) == :lt
      end
    end)
    |> Enum.each(fn license ->
      license
      |> License.changeset(%{
        cohort_window: false,
        window_start_date: nil,
        window_end_date: nil
      })
      |> Repo.update()
      |> case do
        {:ok, _} ->
          :ok

        {:error, _error} ->
          :error
      end
    end)
  end
end

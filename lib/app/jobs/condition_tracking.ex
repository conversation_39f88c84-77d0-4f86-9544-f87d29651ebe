defmodule App.Jobs.JobConditionTracking do
  import Ecto.Query, warn: false
  # App.Jobs.JobConditionTracking.get_application_id(1)
  # App.Jobs.JobConditionTracking.get_condition_id(1)
  alias App.{
    Repo,
    Licenses.LicenseMapping,
    LicenseConditions,
    Licenses.LicenseConditionsMapping
  }

  def get_application_id(license_id) do
    LicenseMapping
    |> where([a], a.id == ^license_id)
    |> preload([:user])
    |> limit(1)
    |> Repo.one()
  end

  def get_condition_id(license_id) do
    LicenseConditionsMapping
    |> where(
      [a],
      (a.application_id == ^license_id and a.expiring_status == ^"SET") or
        a.expiring_status == "EXPIRED"
    )
    |> preload([:condition])
    |> limit(1)
    |> Repo.one()
  end

  def init do
    LicenseConditions.get_license_expiring()
    |> Enum.map(fn map ->
      case map.expiring_status do
        "SET" -> update_license_expiring_condition(map.application_id, map.expiring_date)
        "EXPIRED" -> send_expired_condition_notification(map.application_id, map.expiring_date)
        _ -> nil
      end
    end)
  end

  def send_expired_condition_notification(license_id, expiring_date) do
    expire = Date.diff(Date.from_iso8601!(expiring_date), Date.utc_today())

    if expire != 0 do
      # Waiting for expiration
    else
      license = get_application_id(license_id)
      application_id = get_condition_id(license_id)

      Ecto.Multi.new()
      |> Ecto.Multi.update(
        :updating_user_mapping,
        LicenseMapping.changeset_draft(license, %{condition_tracking: true})
      )
      |> Ecto.Multi.update(
        :update,
        LicenseConditionsMapping.changeset(application_id, %{
          expiring_status: "EXPIRED_CONDITION"
        })
      )
      |> Repo.transaction()
      |> case do
        {:ok, _records} ->
          Task.start(fn ->
            App.Send.Email.client_send_email(
              license.user.email,
              license.user.first_name <> " " <> license.user.last_name,
              expiring_date,
              application_id.condition.name
            )
          end)

          {:ok, "License condition updated successfully"}

        {:error, _reason} ->
          {:error, "Failed to update license condition"}

        {:error, _, error, _} ->
          {:error, error}
      end
    end
  end

  def update_license_expiring_condition(license_id, expiring_date) do
    expire = Date.diff(Date.from_iso8601!(expiring_date), Date.utc_today())

    if expire != 0 do
      # Waiting for expiration
    else
      license = get_application_id(license_id)
      application_id = get_condition_id(license_id)

      Ecto.Multi.new()
      |> Ecto.Multi.update(
        :updating_user_mapping,
        LicenseMapping.changeset_draft(license, %{condition_tracking: true})
      )
      |> Ecto.Multi.update(
        :update,
        LicenseConditionsMapping.changeset(application_id, %{
          expiring_status: "EXPIRED"
        })
      )
      |> Repo.transaction()
      |> case do
        {:ok, _records} ->
          #   Task.start(fn ->
          #     App.Send.Email.client_send_email_notification_expiring_date(license.user.email, license.user.first_name <> " " <> license.user.last_name, expiring_date, application_id.condition.name)
          #   end)

          {:ok, "License condition updated successfully"}

        {:error, _reason} ->
          {:error, "Failed to update license condition"}

        {:error, _, error, _} ->
          {:error, error}
      end
    end
  end
end

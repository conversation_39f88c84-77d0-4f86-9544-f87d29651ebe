defmodule App.Service.FileHandler do
  @moduledoc false
  alias AppWeb.LiveFunctions

  def read_file(path) do
    new_path = LiveFunctions.priv_path(path)

    if File.exists?(new_path) do
      {:ok, new_path}
    else
      :error
    end
  end

  def remove_file(path) do
    new_path = LiveFunctions.priv_path(path)

    if File.exists?(new_path) do
      File.rm_rf(new_path)
    else
      :error
    end
  end
end

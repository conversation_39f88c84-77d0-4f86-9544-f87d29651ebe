defmodule App.Service.Export.CsvSystemLogService do
  @moduledoc false
  alias App.Service.Logs.System

  @headers [
    "DATE",
    "USERNAME",
    "SERVICE",
    "ACTION",
    "IP ADDRESS",
    "DESCRIPTION"
  ]

  def index(payload) do
    System.export(payload)
    |> Stream.map(
      &[
        &1.inserted_at,
        &1.user,
        &1.service,
        &1.action,
        &1.ip_address,
        &1.narration
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["System Audit Logs"],
              ["", "", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end

defmodule App.Service.Export.CsvArchieveSmsLogsServices do
  @moduledoc false
  alias Notification.Service.Logs.LogsSmsArchive

  @headers [
    "MOBILE",
    "SOURCE",
    "SENDER ID",
    "CLIENT",
    "FILENAME",
    "COURIER",
    "STATUS",
    "MSG COUNT",
    "DATE RECIEVED"
  ]

  def index(assigns, payload) do
    LogsSmsArchive.export(assigns, payload)
    |> Stream.map(
      &[
        &1.mobile,
        &1.source,
        &1.sender_id,
        &1.client,
        &1.client,
        &1.courier,
        &1.status,
        &1.count,
        &1.updated_at
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Sms Logs Archieve"],
              ["", "", "", ""],
              @headers,
              ["", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end

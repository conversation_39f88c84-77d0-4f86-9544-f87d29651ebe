defmodule App.Service.Export.CsvApiLogsService do
  @moduledoc false
  alias App.Service.Logs.ApiLogs

  @headers [
    "DATE",
    "SERVICE",
    "REFERNCE",
    "IP ADDRESS",
    "EXTERNAL REFERENCE",
    "ENDPOINT ACCESSED",
    "REQUEST PARAMETER",
    "RESPONSE BODY"
  ]

  def index(payload) do
    ApiLogs.export(payload)
    |> Stream.map(
      &[
        &1.inserted_at,
        &1.service,
        &1.reference,
        &1.ip_address,
        &1.external_reference,
        &1.endpoint,
        &1.request,
        &1.response
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["API Logs Management"],
              ["", "", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end

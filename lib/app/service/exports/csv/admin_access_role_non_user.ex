defmodule App.Service.Export.CsvAdminAccessNoneUserService do
  @moduledoc false
  alias App.Service.Table.AddRoleUsers
  alias App.Service.Export.Functions

  @headers [
    "EMAIL",
    "FIRST NAME",
    "LAST NAME",
    "STATUS",
    "DEPARTMENT ROLE",
    "ACCESS ROLE"
  ]

  def index(payload) do
    AddRoleUsers.export(payload)
    |> Stream.map(
      &[
        &1.email,
        &1.first_name,
        &1.first_name,
        Functions.table_numeric_status(&1.status),
        &1.system_role,
        &1.access_role
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Admin Access Roles Management for None Role User"],
              ["", "", "", "", ""],
              @headers,
              ["", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end

defmodule App.Service.Export.CsvPaymentPlanServices do
  @moduledoc false
  alias App.Service.Table.ServicePlans
  alias App.Service.Export.Functions

  @headers [
    "NAME",
    "LOWER LIMIT",
    "UPPER LIMIT",
    "UNIT PRICE",
    "AMOUNT",
    "STATUS",
    "CREATED BY",
    "DATE CREATED"
  ]

  def index(assigns, payload) do
    ServicePlans.export(assigns, payload)
    |> Stream.map(
      &[
        &1.name,
        &1.lower_limit,
        &1.total_bundle_load,
        NumberF.currency(&1.unit_price),
        NumberF.currency(&1.txb_amount),
        Functions.table_numeric_status(&1.status),
        &1.created_by,
        NaiveDateTime.to_string(&1.inserted_at)
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Payment Plans"],
              ["", "", "", ""],
              @headers,
              ["", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end

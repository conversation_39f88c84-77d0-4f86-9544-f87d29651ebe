defmodule App.Service.Export.CsvClientStatisticsService do
  @moduledoc false
  alias App.Service.Table.ClientReports

  @headers [
    "CLIENT NAME",
    "SENT",
    "DELIVERED",
    "FAILED",
    "PERIOD"
  ]

  def index(_assigns, payload) do
    ClientReports.export(payload)
    |> Stream.map(
      &[
        &1.client,
        &1.sent,
        &1.delivered,
        &1.failed,
        &1.period
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Client Statistics"],
              ["", "", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end

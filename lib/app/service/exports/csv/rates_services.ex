defmodule App.Service.Export.CsvRatesServices do
  @moduledoc false
  alias App.Service.Table.ServiceRates

  @headers [
    "CLIENT NAME",
    "LOWER LIMIT",
    "UPPER LIMIT",
    "UNIT PRICE",
    "CREATED BY",
    "DATE CREATED",
    "LAST MODIFIED"
  ]

  def index(assigns, payload) do
    ServiceRates.export(assigns, payload)
    |> Stream.map(
      &[
        &1.client,
        &1.lower_limit,
        &1.upper_limit,
        NumberF.currency(&1.rate),
        &1.created_by,
        &1.inserted_at,
        &1.updated_at
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Pricing Rates"],
              ["", "", "", ""],
              @headers,
              ["", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end

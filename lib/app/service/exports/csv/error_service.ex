defmodule App.Service.Export.CsvErrorService do
  alias App.Service.Table.ServiceErrors

  @headers [
    "ERROR CODE",
    "ERROR DESCRIPTION",
    "CREATED BY",
    "CREATED AT",
    "LAST MODIFIED"
  ]

  def index(assigns, payload) do
    ServiceErrors.export(assigns, payload)
    |> Stream.map(
      &[
        &1.code,
        &1.error_desc,
        &1.created_by,
        &1.inserted_at,
        &1.updated_at
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Messaging Errors"],
              ["", "", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end

defmodule App.Service.Export.CsvPostpaidClientStatementServices do
  @moduledoc false
  alias App.Service.Table.ServicePostPaid

  @headers [
    "CLIENT NAME",
    "ACCOUNT ID",
    "DATE CREATED"
  ]

  def index(assigns, payload) do
    ServicePostPaid.export(assigns, payload)
    |> Stream.map(
      &[
        &1.client_name,
        &1.client_id,
        NaiveDateTime.to_string(&1.inserted_at)
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Postpaid Client Statement"],
              ["", "", "", ""],
              @headers,
              ["", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end

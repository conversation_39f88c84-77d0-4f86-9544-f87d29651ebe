defmodule App.Service.Export.CsvPurchasesService do
  alias App.Service.Table.ServicePurchases

  @headers [
    "CLIENT NAME",
    "NARRATION",
    "AMOUNT",
    "VAT",
    "SUBTOTAL",
    "UNIT PRICE",
    "CURRENT BUNDLE",
    "BUNDLE",
    "TXN TYPE",
    "CREATED BY",
    "TXN DATE"
  ]

  def index(assigns, payload) do
    ServicePurchases.export(assigns, payload)
    |> Stream.map(
      &[
        &1.client,
        &1.desc,
        &1.txb_amount,
        &1.total_vat,
        &1.amount_aft_vat,
        &1.unit_price,
        &1.bundle,
        &1.total_bundle_load,
        &1.trn_type,
        &1.created_by,
        &1.inserted_at
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Bundle Purchases"],
              ["", "", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end

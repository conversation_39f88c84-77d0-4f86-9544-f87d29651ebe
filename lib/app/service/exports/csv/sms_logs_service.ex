defmodule App.Service.Export.CsvSmsLogsServices do
  @moduledoc false
  alias Notification.Service.Logs.LogsSms

  @headers [
    "MO<PERSON><PERSON>",
    "SOURCE",
    "SENDER ID",
    "CLIENT",
    "FILENAME",
    "COURIER",
    "STATUS",
    "MSG COUNT",
    "DATE RECIEVED"
  ]

  def index(assigns, payload) do
    LogsSms.export(assigns, payload)
    |> Stream.map(
      &[
        &1.mobile,
        &1.source,
        &1.sender_id,
        &1.client,
        &1.client,
        &1.courier,
        &1.status,
        &1.count,
        &1.updated_at
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Sms Logs Service"],
              ["", "", "", ""],
              @headers,
              ["", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end

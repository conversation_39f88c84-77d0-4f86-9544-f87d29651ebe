defmodule App.Service.Export.CsvServiceProviderReports do
  @moduledoc false
  alias App.Service.Table.ProviderReports

  @headers [
    "SERVICE PROVIDER",
    "SENT",
    "DELIVERED",
    "FAILED",
    "PERIOD"
  ]

  def index(_assigns, payload) do
    ProviderReports.export(payload)
    |> Stream.map(
      &[
        &1.provider,
        &1.sent,
        &1.delivered,
        &1.failed,
        &1.period
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Service Provider Report"],
              ["", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end

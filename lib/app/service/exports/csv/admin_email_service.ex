defmodule App.Service.Export.CsvAdminEmailService do
  @moduledoc false
  alias App.Service.Table.Maintenance.ServiceAdminEmail
  alias App.Service.Export.Functions

  @headers [
    "DATE",
    "NAME",
    "EMAIL",
    "STATUS"
  ]

  def index(payload) do
    ServiceAdminEmail.export(payload)
    |> Stream.map(
      &[
        &1.inserted_at,
        &1.name,
        &1.email,
        Functions.table_numeric_status(&1.status)
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Admin Emails"],
              ["", "", "", ""],
              @headers,
              ["", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end

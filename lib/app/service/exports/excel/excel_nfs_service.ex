defmodule App.Service.Export.ExcelNfsService do
  @moduledoc false

  alias Elixlsx.{Workbook, Sheet}

  alias App.Service.Table.NFS

  alias App.Service.Export.{
    ExtraData,
    Functions
  }

  def index(payload) do
    data_call =
      NFS.export(payload)
      |> List.flatten()

    dates =
      Enum.map(data_call, & &1.inserted_at)
      |> Enum.filter(&(!is_nil(&1)))

    start_date =
      Functions.empty_check?(payload["start_date"]) ||
        try do
          Enum.max(dates)
        rescue
          _ -> ""
        end

    end_date =
      Functions.empty_check?(payload["end_date"]) ||
        try do
          Enum.min(dates)
        rescue
          _ -> ""
        end

    reports(data_call, start_date, end_date)
    |> content()
  end

  def reports(posts, start_date, end_date) do
    rows =
      posts
      |> Enum.map(&ExtraData.nfs_service_row/1)

    %Workbook{
      sheets: [
        %Sheet{
          name: "Nfs",
          rows:
            [
              [["Nfs", align_horizontal: :center, bold: true]],
              [["FROM #{start_date} TO #{end_date}", align_horizontal: :center, bold: true]],
              ["", "", "", "", "", "", ""],
              ExtraData.nfs_service_header()
            ] ++ rows,
          merge_cells: [{"A1", "D1"}, {"A2", "D2"}]
        }
      ]
    }
  end

  def content(data) do
    Elixlsx.write_to_memory(data, "Nfs_service_#{:os.system_time()}.xlsx")
    |> elem(1)
    |> elem(1)
  end
end

defmodule App.Service.Export.PdfApiLogService do
  @moduledoc false

  alias App.Service.Logs.ApiLogs

  def index(payload) do
    results =
      ApiLogs.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "inserted_at" => to_string(data.inserted_at),
          "service" => data.service,
          "reference" => data.reference,
          "ip_address" => data.ip_address,
          "external_reference" => data.external_reference,
          "endpoint" => data.endpoint,
          "request" => data.request,
          "response" => data.response
        }
      end)
      |> Enum.map(fn data ->
        """
        <tr>
            <td style="text-align: center;">{{ inserted_at }}</td>
            <td style="text-align: center;">{{ service }}</td>
            <td style="text-align: center;">{{ reference }}</td>
            <td style="text-align: center;">{{ ip_address }}</td>
            <td style="text-align: center;">{{ external_reference }}</td>
            <td style="text-align: center;">{{ endpoint }}</td>
            <td style="text-align: center;">{{ request }}</td>
            <td style="text-align: center;">{{ response }}</td>



        </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/api_log_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "API Logs Management",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end

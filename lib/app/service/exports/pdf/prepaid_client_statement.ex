defmodule App.Service.Export.PrepaidClientStatementServicePdf do
  @moduledoc false

  alias App.Service.Table.ServicePrePaid

  def index(assigns, payload) do
    IO.inspect(payload)

    results =
      ServicePrePaid.export(assigns, payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "client_name" => data.client_name,
          "client_id" => to_string(data.client_id),
          "inserted_at" => NaiveDateTime.to_string(data.inserted_at)
        }
      end)
      |> Enum.map(fn data ->
        """
          <tr>
            # <td style="text-align: center;">{{ client_name }}</td>
            <td style="text-align: center;">{{ client_id }}</td>
            <td style="text-align: center;">{{ inserted_at }}</td>

          </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/prepaid_client_statement_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Prepaid Client Statement",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end

defmodule App.Service.Export.NfsServicePdf do
  @moduledoc false

  alias App.Service.Table.NFS

  alias App.Service.Export.{
    Functions
  }

  def index(payload) do
    results =
      NFS.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "inserted_at" => Calendar.strftime(data.inserted_at, "%d/%m/%Y"),
          "name" => data.name,
          "inst_code" => data.inst_code,
          "type" => data.type,
          "status" => Functions.table_numeric_status(data.status)
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td style="text-align: center;">{{ inserted_at }}</td>
            <td style="text-align: center;">{{ name }}</td>
            <td style="text-align: center;">{{ inst_code }}</td>
            <td style="text-align: center;">{{ type }}</td>
            <td style="text-align: center;">{{ status }}</td>
         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/nfs_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Payment Provider",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end

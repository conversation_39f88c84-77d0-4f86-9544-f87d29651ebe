defmodule App.Service.Export.AccessRolesPdf do
  @moduledoc false

  alias App.Services.Table.AccessRoles

  alias App.Service.Export.{
    Functions
  }

  def index(payload) do
    results =
      AccessRoles.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "inserted_at" => NaiveDateTime.to_string(data.inserted_at),
          "name" => data.name,
          "description" => data.description,
          "user" => data.user,
          "status" => Functions.department_roles_management_status(data.status)
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td style="text-align: center;">{{ inserted_at }}</td>
            <td style="text-align: center;">{{ name }}</td>
            <td style="text-align: center;">{{ description }}</td>
            <td style="text-align: center;">{{ user }}</td>
            <td style="text-align: center;">{{ status }}</td>
         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/access_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Access Roles Management",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end

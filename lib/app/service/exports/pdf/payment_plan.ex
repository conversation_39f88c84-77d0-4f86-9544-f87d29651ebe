defmodule App.Service.Export.PaymentPlansServicePdf do
  @moduledoc false

  alias App.Service.Table.ServicePlans

  alias App.Service.Export.{
    Functions
  }

  def index(assigns, payload) do
    results =
      ServicePlans.export(assigns, payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "name" => data.name,
          "lower_limit" => to_string(data.lower_limit),
          "total_bundle_load" => to_string(data.total_bundle_load),
          "unit_price" => NumberF.currency(data.unit_price, ""),
          "txb_amount" => NumberF.currency(data.txb_amount, ""),
          "status" => Functions.table_numeric_status(data.status),
          "created_by" => data.created_by,
          "inserted_at" => NaiveDateTime.to_string(data.inserted_at)
        }
      end)
      |> Enum.map(fn data ->
        """
          <tr>
            # <td style="text-align: center;">{{ name }}</td>
            <td style="text-align: center;">{{ lower_limit }}</td>
            <td style="text-align: center;">{{ total_bundle_load }}</td>
            <td style="text-align: center;">{{ unit_price }}</td>
            <td style="text-align: center;">{{ txb_amount }}</td>
            <td style="text-align: center;">{{ status }}</td>
            <td style="text-align: center;">{{ created_by }}</td>
            <td style="text-align: center;">{{ inserted_at }}</td>

          </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/payment_plans_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Payment Plans",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end

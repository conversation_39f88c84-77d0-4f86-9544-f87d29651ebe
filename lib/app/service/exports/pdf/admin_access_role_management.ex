defmodule App.Service.Export.AdminRoleAccessPdf do
  @moduledoc false

  alias App.Service.Table.RoleUsers


  def index(payload) do
    results =
      RoleUsers.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "email" => data.email,
          "first_name" => data.first_name,
          "last_name" => data.last_name,
          "status" => data.status,
          "last_login_date" => NaiveDateTime.to_string(data.last_login_date)
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td style="text-align: center;">{{ email }}</td>
            <td style="text-align: center;">{{ first_name }}</td>
            <td style="text-align: center;">{{ last_name }}</td>
            <td style="text-align: center;">{{ status }}</td>
            <td style="text-align: center;">{{ last_login_date }}</td>

         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/admin_access_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Admin Access Role Management",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end

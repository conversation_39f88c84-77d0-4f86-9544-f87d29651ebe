defmodule App.Service.Export.RatesServicePdf do
  @moduledoc false

  alias App.Service.Table.ServiceRates

  def index(assigns, payload) do
    results =
      ServiceRates.export(assigns, payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "client" => data.client,
          "lower_limit" => data.lower_limit,
          "upper_limit" => data.upper_limit,
          "rate" => NumberF.currency(data.rate, ""),
          "created_by" => data.created_by,
          "inserted_at" => data.inserted_at,
          "updated_at" => data.updated_at
        }
      end)
      |> Enum.map(fn data ->
        """
          <tr>
            <td style="text-align: center;">{{ client }}</td>
            <td style="text-align: center;">{{ lower_limit }}</td>
            <td style="text-align: center;">{{ upper_limit }}</td>
            <td style="text-align: center;">{{ rate }}</td>
            <td style="text-align: center;">{{ created_by }}</td>
            <td style="text-align: center;">{{ inserted_at }}</td>
            <td style="text-align: center;">{{ updated_at }}</td>
          </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/rates_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Pricing Rates",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end

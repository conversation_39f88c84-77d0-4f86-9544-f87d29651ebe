defmodule App.Service.Export.ClientStatisticsReportsPdf do
  @moduledoc false

  alias App.Service.Table.ClientReports

  def index(_assign, payload) do
    results =
      ClientReports.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "client" => data.client,
          "sent" => data.sent,
          "delivered" => data.delivered,
          "failed" => data.failed,
          "period" => data.period
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td style="text-align: center;">{{ client }}</td>
            <td style="text-align: center;">{{ sent }}</td>
            <td style="text-align: center;">{{ delivered }}</td>
            <td style="text-align: center;">{{ failed }}</td>
            <td style="text-align: center;">{{ period }}</td>
         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/client_statistics_report.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Client Statistics",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end

defmodule App.Service.Export.DeviceServicePdf do
  @moduledoc false

  alias App.Service.Table.Maintenance.ServiceDevices

  alias App.Service.Export.{
    Functions
  }

  def index(payload) do
    results =
      ServiceDevices.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "inserted_at" => Calendar.strftime(data.inserted_at, "%d/%m/%Y"),
          "code" => data.code,
          "api_key" => data.api_key,
          "status" => Functions.table_numeric_status(data.status)
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td style="text-align: center;">{{ inserted_at }}</td>
            <td style="text-align: center;">{{ code }}</td>
            <td style="text-align: center;">{{ api_key }}</td>
            <td style="text-align: center;">{{ status }}</td>

         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/device_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Device",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end

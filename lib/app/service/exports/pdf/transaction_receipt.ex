defmodule TransactionReceiptComponent do
  use Phoenix.LiveComponent

  def index(assigns) do
    ~H"""
    <!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />

        <title>Transaction Receipt</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { width: 100%; max-width: 600px; margin: 0 auto; padding: 20px; }
          h1 { color: #2c3e50; text-align: center; }
          .receipt { border: 1px solid #ddd; padding: 20px; margin-top: 20px; }
          .receipt-item { margin-bottom: 10px; }
          .receipt-item strong { font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="flex flex-shrink-0 items-center">
            <img class="w-full" src="/images/logo-black-text.png" alt="logo" />
          </div>
          <h1>Transaction Receipt</h1>
          <div class="receipt">
            <div class="receipt-item"><strong>Reference:</strong> <%= @transaction.reference %></div>
            <div class="receipt-item">
              <strong>Date:</strong> <%= Calendar.strftime(
                @transaction.inserted_at,
                "%Y-%m-%d %H:%M:%S"
              ) %>
            </div>
            <div class="receipt-item">
              <strong>Amount:</strong> K<%= Decimal.to_string(@transaction.face_value, :normal) %>
            </div>
            <%!-- <div class="receipt-item"><strong>Recipient:</strong> <%= @transaction.account_number %></div> --%>
            <div class="receipt-item"><strong>Service:</strong> <%= @transaction.service %></div>
            <div class="receipt-item"><strong>Status:</strong> <%= @transaction.status %></div>
            <div class="receipt-item">
              <strong>Description:</strong> <%= @transaction.narration %>
            </div>
          </div>
        </div>
      </body>
    </html>
    """
  end
end

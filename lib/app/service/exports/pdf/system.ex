defmodule App.Service.Export.SystemPdf do
  @moduledoc false

  alias App.Service.Logs.SystemLogs

  def system_service(payload) do
    results =
      SystemLogs.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "inserted_at" => Calendar.strftime(data.inserted_at, "%d/%m/%Y"),
          "service" => data.service,
          "action" => data.action,
          "ip_address" => data.ip_address,
          "description" => data.description
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td style="text-align: center;">{{ inserted_at }}</td>
            <td style="text-align: center;">{{ service }}</td>
            <td style="text-align: center;">{{ action }}</td>
            <td style="text-align: center;">{{ ip_address }}</td>
            <td style="text-align: center;">{{ description }}</td>
         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/system.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "System Service",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end

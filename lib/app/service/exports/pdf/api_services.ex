defmodule App.Service.Export.ApiServicesServicePdf do
  @moduledoc false

  alias App.Service.Table.ApiServices

  alias App.Service.Export.{
    Functions
  }

  def index(assigns, payload) do
    results =
      ApiServices.export(assigns, payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "name" => data.name,
          "regex" => to_string(data.regex),
          "type" => data.type,
          "status" => Functions.table_numeric_status(data.status)
        }
      end)
      |> Enum.map(fn data ->
        """
          <tr>
            # <td style="text-align: center;">{{ name }}</td>
            <td style="text-align: center;">{{ regex }}</td>
            <td style="text-align: center;">{{ type }}</td>
            <td style="text-align: center;">{{ status}}</td>
          </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/api_services_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Api Services",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end

defmodule App.Service.Export.ExtraData do
  @moduledoc false
  alias App.Service.Export.Functions

  @header_style [bg_color: "#2A185B", bold: true, color: "#ffffff"]

  def annual_report_service_header do
    [
      ["MONTH", @header_style],
      ["SENT", @header_style],
      ["DELIVERED", @header_style],
      ["INVALID", @header_style],
      ["FAILED", @header_style],
      ["TOTAL", @header_style]
    ]
  end

  def annual_report_service_row(post) do
    [
      post.month,
      post.sent,
      post.delivered,
      post.failed,
      post.invalid,
      post.total
    ]
  end

  def transaction_reports_service_header do
    [
      ["TXN DATE", @header_style],
      ["REFERENCE", @header_style],
      ["AMOUNT", @header_style],
      ["NARRATION", @header_style],
      ["CLIENT", @header_style],
      ["USER", @header_style],
      ["STATUS", @header_style]
    ]
  end

  def transaction_reports_service_row(post) do
    [
      format_date(post.txn_date),
      post.reference,
      post.txb_amount,
      post.narration,
      post.client,
      post.transacting_user,
      post.status
    ]
  end

  def sms_logs_archieve_header do
    [
      ["MOBILE", @header_style],
      ["SOURCE", @header_style],
      ["SENDER ID", @header_style],
      ["CLIENT", @header_style],
      ["FILENAME", @header_style],
      ["COURIER", @header_style],
      ["STATUS", @header_style],
      ["MSG COUNT", @header_style],
      ["DATE RECIEVED", @header_style]
    ]
  end

  def sms_logs_archieve_row(post) do
    [
      post.mobile,
      post.source,
      post.sender_id,
      post.client,
      post.client,
      post.courier,
      post.status,
      post.count,
      format_date(post.updated_at)
    ]
  end

  def client_statistics_reports_header do
    [
      ["CLIENT NAME", @header_style],
      ["SENT", @header_style],
      ["DELIVERED", @header_style],
      ["FAILED", @header_style],
      ["PERIOD", @header_style]
    ]
  end

  def client_statistics_reports_row(post) do
    [
      post.client,
      post.sent,
      post.delivered,
      post.failed,
      post.period
    ]
  end

  def sms_logs_service_header do
    [
      ["MOBILE", @header_style],
      ["SOURCE", @header_style],
      ["SENDER ID", @header_style],
      ["CLIENT", @header_style],
      ["FILENAME", @header_style],
      ["COURIER", @header_style],
      ["STATUS", @header_style],
      ["MSG COUNT", @header_style],
      ["DATE RECIEVED", @header_style]
    ]
  end

  def sms_logs_service_row(post) do
    [
      post.mobile,
      post.source,
      post.sender_id,
      post.client,
      post.client,
      post.courier,
      post.status,
      post.count,
      format_date(post.updated_at)
    ]
  end

  def service_provider_reports_heard do
    [
      ["SERVICE PROVIDER", @header_style],
      ["SENT", @header_style],
      ["DELIVERED", @header_style],
      ["FAILED", @header_style],
      ["PERIOD", @header_style]
    ]
  end

  def service_provider_reports_row(post) do
    [
      post.provider,
      post.sent,
      post.delivered,
      post.failed,
      post.period
    ]
  end

  def admin_access_role_none_role_user_service_row(post) do
    [
      post.email,
      post.first_name,
      post.first_name,
      Functions.table_numeric_status(post.status),
      post.system_role,
      post.access_role
    ]
  end

  def admin_access_role_none_role_user_service_heard do
    [
      ["EMAIL", @header_style],
      ["FIRST NAME", @header_style],
      ["LAST NAME", @header_style],
      ["STATUS", @header_style],
      ["DEPARTMENT ROLE", @header_style],
      ["ACCESS ROLE", @header_style]
    ]
  end

  def access_roles_service_heard do
    [
      ["EMAIL", @header_style],
      ["FIRST NAME", @header_style],
      ["LAST NAME", @header_style],
      ["STATUS", @header_style],
      ["LAST LOGIN DATE", @header_style]
    ]
  end

  def admin_access_roles_service_row(post) do
    [
      NaiveDateTime.to_string(post.inserted_at),
      post.name,
      post.description,
      post.user,
      post.status,
      format_date(post.last_login_date)
    ]
  end

  def admin_access_role_service_heard do
    [
      ["DATE", @header_style],
      ["NAME", @header_style],
      ["DESCRIPTION", @header_style],
      ["USER COUNT", @header_style],
      ["STATUS", @header_style]
    ]
  end

  def access_roles_service_row(post) do
    [
      NaiveDateTime.to_string(post.inserted_at),
      post.name,
      post.description,
      post.user,
      Functions.department_roles_management_status(post.status)
    ]
  end

  def department_roles_management_service_row(post) do
    [
      NaiveDateTime.to_string(post.inserted_at),
      post.name,
      post.description,
      post.access_roles,
      Functions.department_roles_management_status(post.status)
    ]
  end

  def department_roles_management_service_hearder do
    [
      ["DATE", @header_style],
      ["NAME", @header_style],
      ["DESCRIPTION", @header_style],
      ["ACCESS ROLE COUNT", @header_style],
      ["STATUS", @header_style]
    ]
  end

  def smpp_services_service_row(post) do
    [
      post.service_name,
      post.host,
      post.system_id,
      post.mobile_regex,
      Functions.table_numeric_status(post.status)
    ]
  end

  def smpp_services_service_heard do
    [
      ["SERVICE NAME", @header_style],
      ["HOST", @header_style],
      ["SYSTEM ID", @header_style],
      ["MOBILE REGEX", @header_style],
      ["STATUS", @header_style]
    ]
  end

  def api_services_service_row(post) do
    [
      post.name,
      post.regex,
      post.type,
      Functions.table_numeric_status(post.status)
    ]
  end

  def api_services_service_heard do
    [
      ["NAME", @header_style],
      ["REGEX", @header_style],
      ["TYPE", @header_style],
      ["STATUS", @header_style]
    ]
  end

  def prepaid_client_statement_service_row(post) do
    [
      post.client_name,
      post.client_id,
      format_date(post.inserted_at)
    ]
  end

  def prepaid_client_statement_service_heard do
    [
      ["CLIENT NAME", @header_style],
      ["ACCOUNT ID", @header_style],
      ["DATE CREATED", @header_style]
    ]
  end

  def postpaid_client_statement_service_row(post) do
    [
      post.client_name,
      post.client_id,
      format_date(post.inserted_at)
    ]
  end

  def postpaid_client_statement_service_heard do
    [
      ["CLIENT NAME", @header_style],
      ["ACCOUNT ID", @header_style],
      ["DATE CREATED", @header_style]
    ]
  end

  def payment_plans_service_header do
    [
      ["NAME", @header_style],
      ["LOWER LIMIT", @header_style],
      ["UPPER LIMIT", @header_style],
      ["UNIT PRICE", @header_style],
      ["AMOUNT", @header_style],
      ["STATUS", @header_style],
      ["CREATED BY", @header_style],
      ["DATE CREATED", @header_style]
    ]
  end

  def payment_plans_service_row(post) do
    [
      post.name,
      to_string(post.lower_limit),
      to_string(post.total_bundle_load),
      NumberF.currency(post.unit_price, ""),
      NumberF.currency(post.txb_amount, ""),
      Functions.table_numeric_status(post.status),
      post.created_by,
      NaiveDateTime.to_string(post.inserted_at)
    ]
  end

  def self_registration_applications_header do
    [
      ["REGISTRATION DATE", @header_style],
      ["COMPANY NAME", @header_style],
      ["CONTACT NAME", @header_style],
      ["EMAIL", @header_style],
      ["MOBILE NUMBER", @header_style],
      ["STATUS", @header_style]
    ]
  end

  def self_registration_applications_row(post) do
    [
      format_date(post.inserted_at),
      post.company_name,
      post.full_name,
      post.email,
      post.mobile_number,
      Functions.self_registration_status(post.status)
    ]
  end

  def monthly_report_service_header do
    [
      ["DATE", @header_style],
      ["SENT", @header_style],
      ["DELIVERED", @header_style],
      ["INVALID", @header_style],
      ["FAILED", @header_style],
      ["TOTAL", @header_style]
    ]
  end

  def monthly_report_service_row(post) do
    [
      post.date,
      post.sent,
      post.delivered,
      post.failed,
      post.invalid,
      post.total
    ]
  end

  def system_logs_service_header do
    [
      ["DATE", @header_style],
      ["USERNAME", @header_style],
      ["SERVICE", @header_style],
      ["ACTION", @header_style],
      ["IP ADDRESS", @header_style],
      ["DESCRIPTION", @header_style]
    ]
  end

  def system_logs_service_row(post) do
    [
      format_date(post.inserted_at),
      post.user,
      post.service,
      post.action,
      post.ip_address,
      post.narration
    ]
  end

  def session_logs_service_header do
    [
      ["DATE", @header_style],
      ["USERNAME", @header_style],
      ["PORTAL", @header_style],
      ["SESSION", @header_style],
      ["DESCRIPTION", @header_style]
    ]
  end

  def session_logs_service_row(post) do
    [
      format_date(post.inserted_at),
      post.user_id,
      post.portal,
      Functions.table_boolean_status(post.status),
      post.description
    ]
  end

  def api_logs_service_header do
    [
      ["DATE", @header_style],
      ["SERVICE", @header_style],
      ["REFERENCE", @header_style],
      ["IP ADDRESS", @header_style],
      ["EXTERNAL REFERENCE", @header_style],
      ["ENDPOINT ACCESSED", @header_style],
      ["REQUEST PARAMETER", @header_style],
      ["RESPONSE BODY", @header_style]
    ]
  end

  def api_logs_service_row(post) do
    [
      format_date(post.inserted_at),
      post.service,
      post.reference,
      post.ip_address,
      post.external_reference,
      post.endpoint,
      post.request,
      post.response
    ]
  end

  def error_service_header do
    [
      ["ERROR CODE", @header_style],
      ["ERROR DECRIPTION", @header_style],
      ["CREATED BY", @header_style],
      ["CREATED AT", @header_style],
      ["LAST MODIFIED", @header_style]
    ]
  end

  def error_service_row(post) do
    [
      post.code,
      post.error_desc,
      post.created_by,
      format_date(post.inserted_at),
      format_date(post.updated_at)
    ]
  end

  def rates_service_header do
    [
      ["CLIENT NAME", @header_style],
      ["LOWER LIMIT", @header_style],
      ["UPPER LIMIT", @header_style],
      ["UNIT PRICE", @header_style],
      ["CREATED BY", @header_style],
      ["DATE CREATED", @header_style],
      ["LAST MODIFIED", @header_style]
    ]
  end

  def rates_service_row(post) do
    [
      post.client,
      post.lower_limit,
      post.upper_limit,
      NumberF.currency(post.rate, ""),
      post.created_by,
      format_date(post.inserted_at),
      format_date(post.updated_at)
    ]
  end

  def purchase_service_header do
    [
      ["CLIENT NAME", @header_style],
      ["NARRATION", @header_style],
      ["AMOUNT", @header_style],
      ["VAT", @header_style],
      ["SUBTOTAL", @header_style],
      ["UNIT PRICE", @header_style],
      ["CURRENT BUNDLE", @header_style],
      ["BUNDLE", @header_style],
      ["TXN TYPE", @header_style],
      ["CREATED BY", @header_style],
      ["TXN DATE", @header_style]
    ]
  end

  def purchase_service_row(post) do
    [
      post.client,
      post.desc,
      NumberF.currency(post.txb_amount, ""),
      NumberF.currency(post.total_vat, ""),
      NumberF.currency(post.amount_aft_vat, ""),
      NumberF.currency(post.unit_price, ""),
      to_string(post.bundle),
      to_string(post.total_bundle_load),
      post.trn_type,
      post.created_by,
      format_date(post.inserted_at)
    ]
  end

  def sender_service_header do
    [
      ["SENDER", @header_style],
      ["CLIENT NAME", @header_style],
      ["CREATED BY", @header_style],
      ["CREATED AT", @header_style],
      ["LAST MODIFIED", @header_style]
    ]
  end

  def sender_service_row(post) do
    [
      post.sender,
      post.client_name,
      post.created_by,
      format_date(post.inserted_at),
      format_date(post.updated_at)
    ]
  end

  def client_profile_service_header do
    [
      ["Created at", @header_style],
      ["Email", @header_style],
      ["First Name", @header_style],
      ["Last Name", @header_style],
      ["Mobile Number", @header_style],
      ["Status", @header_style]
    ]
  end

  def client_profile_service_row(post) do
    [
      post.inserted_at,
      post.email,
      post.first_name,
      post.last_name,
      post.mobile,
      Functions.table_numeric_status(post.status)
    ]
  end

  def contact_person_service_header do
    [
      ["FIRST NAME", @header_style],
      ["LAST NAME", @header_style],
      ["MOBILE NUMBER", @header_style],
      ["EMAIL", @header_style],
      ["CLIENT", @header_style],
      ["CREATED AT", @header_style],
      ["LAST MODIFIED", @header_style],
      ["STATUS", @header_style]
    ]
  end

  def contact_person_service_row(post) do
    [
      post.first_name,
      post.last_name,
      post.mobile,
      post.email,
      post.client_id,
      format_date(post.inserted_at),
      format_date(post.updated_at),
      Functions.table_numeric_status(post.status)
    ]
  end

  def users_service_header do
    [
      ["CREATED AT", @header_style],
      ["EMAIL", @header_style],
      ["FIRST NAME", @header_style],
      ["LAST NAME", @header_style],
      ["MOBILE NUMBER", @header_style],
      ["STATUS", @header_style]
    ]
  end

  def users_service_row(post) do
    [
      format_date(post.inserted_at),
      post.email,
      post.first_name,
      post.last_name,
      post.mobile,
      Functions.table_numeric_status(post.status)
    ]
  end

  defp format_date(nil), do: "N/A"

  defp format_date(inserted_at) do
    case parse_datetime(inserted_at) do
      {:ok, naive_datetime} ->
        naive_datetime
        |> NaiveDateTime.add(7200, :second)
        |> NaiveDateTime.to_iso8601()
        |> String.replace("T", " ")
        |> convert_to_dd_mm_yyyy()

      _ ->
        "N/A"
    end
  end

  defp parse_datetime(%NaiveDateTime{} = datetime), do: {:ok, datetime}

  defp parse_datetime(datetime_string) when is_binary(datetime_string),
    do: parse_custom_datetime(datetime_string)

  defp parse_datetime(_), do: :error

  defp convert_to_dd_mm_yyyy(datetime_string) do
    [date, time] = String.split(datetime_string, " ")
    [year, month, day] = String.split(date, "-")
    "#{day}-#{month}-#{year} #{time}"
  end

  defp parse_custom_datetime(datetime_string) do
    case Regex.run(~r/^(\d{1,2}) (\w{3}) (\d{4}), (\d{2}):(\d{2}):(\d{2})$/, datetime_string) do
      [_, day, month, year, hour, minute, second] ->
        with {:ok, month_number} <- month_to_number(month),
             {:ok, naive_datetime} <-
               NaiveDateTime.new(
                 String.to_integer(year),
                 month_number,
                 String.to_integer(day),
                 String.to_integer(hour),
                 String.to_integer(minute),
                 String.to_integer(second)
               ) do
          {:ok, naive_datetime}
        else
          _ -> :error
        end

      _ ->
        :error
    end
  end

  defp month_to_number(month) do
    months = %{
      "JAN" => 1,
      "FEB" => 2,
      "MAR" => 3,
      "APR" => 4,
      "MAY" => 5,
      "JUN" => 6,
      "JUL" => 7,
      "AUG" => 8,
      "SEP" => 9,
      "OCT" => 10,
      "NOV" => 11,
      "DEC" => 12
    }

    Map.fetch(months, String.upcase(month))
  end
end

defmodule App.Service.ServiceContactPersonMaintenance.Functions do
  alias App.Accounts

  def index(socket, attrs, function \\ "change_status") do
    user = Accounts.get_contact_person!(attrs["id"])

    cond do
      function == "change_status" ->
        Accounts.change_contact_status(socket, attrs, user)

      function == "reset_user_password" ->
        Accounts.reset_contact_person(socket, attrs, user)
    end
  end

  def create(socket, attrs) do
    Accounts.create_contact_person(socket, attrs)
  end

  def update(socket, user, attrs) do
    Accounts.update_contact_person(socket, user, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end
end

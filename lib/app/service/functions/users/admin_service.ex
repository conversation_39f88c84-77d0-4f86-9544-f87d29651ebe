defmodule App.Service.ServiceAdminMaintenance.Functions do
  alias App.Accounts

  def index(socket, attrs, function \\ "change_status") do
    user = Accounts.get_admin!(attrs["id"])

    cond do
      function == "change_status" ->
        Accounts.change_admin_user_status(socket, attrs, user)

      function == "reset_user_password" ->
        Accounts.reset_admin_password(socket, attrs, user)
    end
  end

  def create(socket, attrs) do
    Accounts.create_admin_user(socket, attrs)
  end

  def update(socket, user, attrs) do
    Accounts.update_admin_user(socket, user, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end
end

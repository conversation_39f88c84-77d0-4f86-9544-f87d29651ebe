defmodule App.Service.ServiceCommittee.Functions do
  alias App.Utilities

  def index(socket, attrs, function \\ "change_status") do
    record = Utilities.get_committee!(attrs["id"])

    cond do
      function == "change_status" ->
        Utilities.change_committee_status(socket, attrs, record)
    end
  end

  def create(socket, attrs) do
    Utilities.create_committee(socket, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  def update(socket, record, attrs) do
    Utilities.update_committee(socket, record, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end
end

defmodule App.Service.Dashboard.Stats do
  @moduledoc """
  Provides dashboard statistics functions for license applications.

  This module contains functions to retrieve various statistics for the dashboard,
  including counts for new applications, market operations, supervisor submissions,
  and returned applications based on user roles and departments.
  """

  alias App.Licenses

  def new(socket) do
    Licenses.get_license_approval_status_list(
      socket.assigns.role_department,
      socket.assigns.current_user.role_id
    )
    |> Enum.filter(&(!is_nil(&1)))
    |> Enum.uniq()
    |> Licenses.get_new_applications_count() || 0
  end

    def all(socket) do
    Licenses.get_license_approval_status_list(
      socket.assigns.role_department,
      socket.assigns.current_user.role_id
    )
    |> Enum.filter(&(!is_nil(&1)))
    |> Enum.uniq()
    |> Licenses.get_all_applications_count() || 0
  end

  def market_operations(socket) do
    Licenses.get_license_approval_status(
      socket.assigns.role_department,
      socket.assigns.current_user.role_id
    )
    |> Licenses.get_market_operations(socket.assigns.current_user.id) ||
      0
  end

  def supervisor(socket) do
    Licenses.get_license_approval_status_list(
      socket.assigns.role_department,
      socket.assigns.current_user.role_id
    )
    |> Licenses.get_submitted_to_supervisor(socket.assigns.current_user.id) ||
      0
  end

  def summary(socket) do
    Licenses.get_license_approval_status_list(
      socket.assigns.role_department,
      socket.assigns.current_user.role_id
    )
    |> Licenses.get_application_summary(socket.assigns.current_user.id) ||
      0
  end

  def returned_to_application(socket),
    do:
      Licenses.get_returned_to_applicant_mappings(socket.assigns.current_user.id) ||
        0
end

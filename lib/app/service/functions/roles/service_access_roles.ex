defmodule App.Service.Functions.AccessRoleLive do
  @moduledoc false
  alias App.{
    Roles,
    # Audit,
    # Authorization,
    CustomContext,
    Settings,
    Enumerable.SystemStatus
  }

  def create_access_roles(socket, attrs) do
    Roles.register_access_role(socket, attrs)
    |> case do
      {:ok, _} -> {:ok, "Access Role created successfully", 0}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  def register_make_process(socket, attrs) do
    current_user = socket.assigns.current_user

    MakerCheckerContext.insert_maker_checker(
      socket.assigns.remote_ip,
      Map.delete(attrs, "display")
      |> Map.delete("remarks"),
      Map.delete(attrs["display"], "remarks"),
      current_user.id,
      "CREATE_ACCESS_ROLE",
      attrs["remarks"],
      "Elixir.LraPayments.Database.AccessRoles",
      "Elixir.LraPayments.RolesContext",
      nil,
      "CREATE",
      "register_access_role_multi"
    )
    |> LogsContext.system_log_session_live_multi(
      socket,
      "Create New Access Role [#{attrs["name"]} #{attrs["code"]}] submitted for approval",
      "CREATE",
      attrs,
      "Access Role Maintenance"
    )
    |> CustomContext.process_transaction()
    |> MakerCheckerContext.notify_subs("maker_checker")
    |> case do
      {:ok, %{maker: maker}} -> {:ok, "Successfully Submitted the request", maker.id}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  def update_access_roles(socket, system_role, attrs) do
    Roles.update_access_role(socket, system_role, attrs)
    |> case do
      {:ok, _} -> {:ok, "Access Role updated successfully", 0}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end


  def status_changer(socket, access_role, attrs) do
    if Settings.check_setting_status("maker_checker") do
      update_make_process_status(socket, access_role, attrs)
    else
      Roles.change_access_status(socket, access_role, attrs)
      |> case do
        {:ok, %{update: update}} -> {:ok, "#{update.name} status changed successfully", 0}
        {:error, error} -> {:error, error}
        {:error, _, error, _} -> {:error, error}
      end
    end
  end

  defp update_make_process_status(socket, agreements, attrs) do
    current_user = socket.assigns.current_user

    MakerCheckerContext.insert_maker_checker(
      socket.assigns.remote_ip,
      Map.delete(attrs, "display")
      |> Map.delete("remarks")
      |> Map.delete("action"),
      %{
        "name" => agreements.name,
        "new_status" => SystemStatus.get_status(attrs["status"]),
        "old_status" => SystemStatus.get_status(agreements.status)
      },
      current_user.id,
      "UPDATE_ACCESS_ROLE",
      attrs["remarks"],
      "Elixir.LraPayments.Database.AccessRoles",
      "Elixir.LraPayments.RolesContext",
      agreements.status,
      "UPDATE",
      "update_access_role_multi"
    )
    |> LogsContext.system_log_session_live_multi(
      socket,
      "Update Access Role [#{agreements.name}] submitted for approval",
      "UPDATE",
      attrs,
      "Access Role Maintenance"
    )
    |> Roles.disable_access_role_for_maker_checker(agreements)
    |> CustomContext.process_transaction()
    |> MakerCheckerContext.notify_subs("maker_checker")
    |> case do
      {:ok, %{maker: maker}} ->
        {:ok, "#{agreements.name} was successfully Submitted the request", maker.id}

      {:error, error} ->
        {:error, error}

      {:error, _, error, _} ->
        {:error, error}
    end
  end
end

defmodule App.Service.SystemRoleLive.Function do
  @moduledoc false
  alias App.{
    Accounts,
    Audit,
    Authorization,
    CustomContext,
    # Settings,
    # Roles.DepartmentRoles,
    # Service.SystemRoleLive.Functions,
    Roles
  }

  def status_changer(socket, department_role, attrs) do
    current_user = socket.assigns.current_user
    # if Settings.check_setting_status("maker_checker") do
    #   update_make_process_status(socket, department_role, attrs)
    # else
    Roles.change_department_role_status(
      socket,
      attrs,
      attrs["status"],
      department_role,
      current_user.id
    )
    |> case do
      {:ok, %{update: update}} -> {:ok, "#{update.name} status changed successfully", 0}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end

    # end
  end

  def create_system_roles(socket, attrs) do
    Roles.create_department_roles(socket, attrs)
    |> case do
      {:ok, _} -> {:ok, "Successfully created new department role", false}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  def register_role_make_process(socket, attrs) do
    current_user = socket.assigns.current_user

    Authorization.insert_maker_checker(
      socket.assigns.remote_ip,
      {Map.put(attrs, "status", 1), current_user.id, "CREATE_USER"},
      {
        "Elixir.OneZambiaLotto.Accounts.User",
        "Elixir.OneZambiaLotto.Accounts",
        "Elixir.OneZambiaLotto.Roles",
        "register_user_multi"
      },
      {nil, "CREATE", nil}
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "Create New User [#{attrs["username"]} #{attrs["first_name"]} - #{attrs["last_name"]}] submitted for approval",
      "CREATE",
      attrs,
      "Admin User Management"
    )
    |> CustomContext.process_transaction()
    |> CustomContext.notify_subs("maker_checker", :update, Authorization)
    |> case do
      {:ok, %{maker: maker}} -> {:ok, "Successfully submitted the request", maker.id}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  def update_department_roles(socket, record, attrs) do
    Roles.update_department_roles(socket, record, attrs)
    |> case do
      {:ok, _} -> {:ok, "Successfully updated department role", false}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  def update_user_make_process(socket, record, attrs) do
    current_user = socket.assigns.current_user

    Authorization.insert_maker_checker(
      socket.assigns.remote_ip,
      {
        Map.put(attrs, "id", record.id),
        current_user.id,
        "UPDATE_USER"
      },
      {
        "Elixir.OneZambiaLotto.Accounts.User",
        "Elixir.OneZambiaLotto.Accounts",
        "update_user_multi"
      },
      {record.status, "UPDATE", nil}
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "Update User [#{record.username} #{record.first_name} - #{record.last_name}] submitted for approval",
      "UPDATE",
      attrs,
      "Admin User Management"
    )
    |> Accounts.disable_user_for_maker_checker(record, current_user)
    |> CustomContext.notify_subs("maker_checker", :update, Authorization)
    |> case do
      {:ok, %{maker: maker}} -> {:ok, "Successfully submitted the request", maker.id}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end
end

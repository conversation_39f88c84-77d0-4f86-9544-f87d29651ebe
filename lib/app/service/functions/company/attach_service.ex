defmodule App.Service.ServiceCompanyAttach.Functions do
  alias App.Companies

  def index(socket, attrs, function \\ "change_status") do
    record = Companies.get_notice_of_change!(attrs["id"])

    cond do
      function == "approve" ->
        Companies.approve_request(socket, attrs, record)

      function == "decline" ->
        Companies.decline_request(socket, attrs, record)
    end
  end
end

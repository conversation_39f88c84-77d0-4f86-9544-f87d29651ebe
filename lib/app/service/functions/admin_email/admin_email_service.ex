defmodule App.Service.ServiceAdminEmailMaintenance.Functions do
  alias App.Maintenance

  def index(socket, attrs, function \\ "change_status") do
    branch = Maintenance.get_admin_email!(attrs["id"])

    cond do
      function == "change_status" ->
        Maintenance.change_admin_email_status(socket, attrs, branch)
    end
  end

  def create(socket, attrs) do
    Maintenance.create_admin_email(socket, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  def update(socket, branch, attrs) do
    Maintenance.update_admin_email(socket, branch, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end
end

defmodule App.Service.ServiceError.Functions do
  alias App.Utilities

  def index(socket, attrs, function \\ "change_status") do
    record = Utilities.get_error_code!(attrs["id"])

    cond do
      function == "change_status" ->
        Utilities.delete_error(socket, attrs, record)
    end
  end

  def create(socket, attrs) do
    Utilities.create_error(socket, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  def update(socket, record, attrs) do
    Utilities.update_error(socket, record, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end
end

defmodule App.Service.ServicMappingConditions.Functions do
  alias App.LicenseConditions

  def index(socket, attrs, function \\ "change_status") do
    # record = LicenseConditions.get_condition_mapping!(attrs["id"])

    cond do
      function == "confirm" ->
        LicenseConditions.confirm_condition(socket, attrs)

      function == "decline" ->
        LicenseConditions.decline_condition(socket, attrs)
    end
  end

  def create(socket, attrs) do
    LicenseConditions.create_condition(socket, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  def update(socket, record, attrs) do
    LicenseConditions.update_condition(socket, record, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end
end

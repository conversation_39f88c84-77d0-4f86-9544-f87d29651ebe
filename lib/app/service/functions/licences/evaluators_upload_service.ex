defmodule App.Service.Functions.EvaluatorsUpload do
  use AppWeb, :file_function

  alias App.{Licenses, Users}
  alias App.Database.CustomDB

  def execute(_socket, path, params) do
    Extract.validate_excel_and_csv(path)
    |> case do
      {:ok, col_count, file} ->
        cond do
          col_count == 0 ->
            {:error, :start, "No records found in the uploaded file."}

          col_count < 4 ->
            {:error, :start, "System expecting 4 columns but only got #{col_count}."}

          col_count > 4 ->
            {:error, :start, "System expecting 4 columns but got #{col_count}."}

          true ->
            file
            |> case do
              {:ok, txn} ->
                extraction_process(path, txn, params)

              {:error, message} ->
                {:error, :start, message}
            end
        end

      error ->
        error
    end
  end

  defp extraction_process(path, txn, params) do
    txn
    |> Extract.add_index()
    |> Enum.map(fn record ->
      get_user = Users.get_user_by_email(record["col3"])

      check_user =
        if is_nil(get_user) do
          false
        else
          Licenses.check_if_user_exists(get_user.id, params["application_id"])
        end

      cond do
        String.trim(record["col1"]) == "" ->
          message(0, "First Name cannot be blank", record)

        String.trim(record["col2"]) == "" ->
          message(0, "Last Name cannot be blank", record)

        String.trim(record["col3"]) == "" ->
          message(0, "Email cannot be blank", record)

        !Regex.match?(
          ~r/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/,
          String.trim(record["col3"])
        ) ->
          message(0, "Invalid email format", record)

        String.trim(record["col4"]) == "" ->
          message(0, "Mobile Number cannot be blank", record)

        !validate_mobile_number(to_string(record["col4"])) ->
          message(0, "Invalid Phone Number", record)

        check_user ->
          message(0, "Evaluator Already Assigned", record)

        true ->
          message(1, "Valid record", record)
      end
    end)
    |> Extract.finalize_process(
      path,
      # successful message, encase the process is a success
      "All Records in the file are valid, proceed to update them.",
      # failed message, encase the process failed
      "Records from the uploaded file are invalid. Please review and clean up the file, or proceed with the invalid records included."
    )
  end

  def process_file(socket, application, attrs) do
    entries = Jason.decode!(attrs["entries"])

    Licenses.bulk_evaluator_upload(socket, application, entries)
    |> case do
      {:ok, txn} ->
        {:ok, "Successfully added #{Enum.count(entries)} messages", txn}

      {:error, message} ->
        {:error, message}
    end
  end

  def message(status, message, params) do
    %{
      status: status,
      col1: params["col1"],
      col2: params["col2"],
      col3: params["col3"],
      col4: params["col4"],
      key: params["key"] + 1,
      message: message
    }
  end

  defp validate_mobile_number(mobile) do
    CustomDB.validate_mobile_call(mobile)
    |> case do
      {:error, _} -> false
      {:ok, _} -> true
    end
  end
end

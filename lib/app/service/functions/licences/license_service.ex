defmodule App.Service.ServiceLicenseMaintenance.Functions do
  alias App.Accounts

  def index(socket, attrs, function \\ "change_status") do
    user = App.Licenses.get_license!(attrs["id"])

    cond do
      function == "change_status" ->
        App.Licenses.change_license_status(socket, attrs, user)

      function == "reset_user_password" ->
        Accounts.reset_admin(socket, attrs, user)
    end
  end

  def create(socket, attrs) do
    App.Licenses.create_license(socket, attrs)
  end

  def update(socket, license, attrs) do
    App.Licenses.update_license(socket, license, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
    end
  end
end

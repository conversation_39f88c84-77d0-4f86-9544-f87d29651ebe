defmodule App.Service.APIManagementLive.Functions do
  @moduledoc false

  alias App.{
    Management
  }

  def update_api_management(api_management, socket, attrs) do
    Management.update_api(socket, api_management, attrs)
    |> case do
      {:ok, %{entry: entry}} -> {:ok, "#{entry.name} updated successfully", 0}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  #  def update_make_process(socket, record, attrs) do
  #    current_user = socket.assigns.current_user
  #
  #    display =
  #      Map.delete(attrs, "display")
  #      |> Map.delete("action")
  #      |> Map.delete("data")
  #      |> Map.merge(Poison.decode!(attrs["data"]))
  #      |> Map.delete("action")
  #      |> Map.delete("remarks")
  #
  #    Authorization.insert_maker_checker(
  #      socket.assigns.remote_ip,
  #      {
  #        Map.merge(attrs, %{"id" => record.id, "display" => display}),
  #        current_user.id,
  #        "UPDATE_USER"
  #      },
  #      {
  #        "Elixir.OneZambiaLotto.Management.ApiManagement",
  #        "Elixir.OneZambiaLotto.Management",
  #        "update_api_management_multi"
  #      },
  #      {record.status, "UPDATE", nil}
  #    )
  #    |> Audit.create_system_log_session_live_multi(
  #         socket, "Update API Maintenance [#{record.name}] submitted for approval",
  #         "UPDATE", attrs, "API Maintenance")
  #    |> Management.disable_agreements_for_maker_checker(record)
  #    |> CustomContext.notify_subs("maker_checker", :update, Authorization)
  #    |> case do
  #         {:ok, %{maker: maker}} -> {:ok, "Successfully submitted the request", maker.id}
  #         {:error, error} -> {:error, error}
  #         {:error, _, error, _} -> {:error, error}
  #       end
  #  end
end

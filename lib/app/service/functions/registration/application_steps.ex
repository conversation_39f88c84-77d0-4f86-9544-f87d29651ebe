defmodule App.Service.Table.Registration.ApplicationSteps.Functions do
  alias App.Registration

  def index(socket, attrs, function \\ "change_status") do
    record = Registration.get_application_steps!(attrs["id"])

    cond do
      function == "change_status" ->
        Registration.change_application_steps_status(socket, attrs, record)
    end
  end

  def create_application_steps(socket, attrs) do
  case Registration.create_application_steps(socket, attrs) do
    {:ok, %{create: data}} -> {:ok, data}
    {:ok, %{create: data, system_logs: _log}} -> {:ok, data}
    {:error, :create, error, _changes} -> {:error, error}
    {:error, error} -> {:error, error}
    other -> other
  end
end

end

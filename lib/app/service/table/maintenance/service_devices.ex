defmodule App.Service.Table.Maintenance.ServiceDevices do
  @moduledoc false
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false
  alias App.Accounts.{Devices}
  alias App.Repo

  @pagination [page_size: 10]

  def index(assigns, params) do
    compose_query(assigns, params)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(assigns, params) do
    compose_query(assigns, params)
    |> Repo.all()
  end

  defp compose_query(_assigns, params) do
    Devices
    |> join(:left, [a], b in assoc(a, :user))
    |> where([a], a.status != 0)
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  # defp check_user(query, assigns)do
  #   if assigns.role_department in [1, 2, 3] do
  #     query
  #   else
  #       if assigns.role_department == 4 do
  #         where(query, [a], a.merchant_id == ^assigns.current_user.merchant_id)
  #       else
  #         where(query, [a], a.user_id == ^assigns.current_user.id)
  #       end
  #   end

  # end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"device_id", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.device_id, ^value))

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a, b], %{
      id: a.id,
      name: a.name,
      platform: a.platform,
      operating_system: a.operating_system,
      last_activity: a.last_activity,
      status: a.status,
      inserted_at: a.inserted_at,
      user: b.email
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b],
      fragment("lower(?) LIKE lower(?)", a.name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.platform, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.operating_system, ^search_term) or
        fragment("lower(?) LIKE lower(?)", b.email, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end

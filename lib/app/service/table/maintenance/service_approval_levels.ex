defmodule App.Service.Table.ApprovalLevels do
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false

  alias App.Licenses.ApprovalLevels
  alias App.Repo

  @pagination [page_size: 10]

  def index(assigns, params) do
    compose_query(params, assigns)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(assigns, params) do
    compose_query(params, assigns)
    |> Repo.all()
  end

  def compose_query(params, assigns) do
    ApprovalLevels
    |> join(:left, [a], b in assoc(a, :department))
    |> join(:left, [a], c in assoc(a, :role))
    |> join(:left, [a], d in assoc(a, :categories))
    |> where([a], a.categories_id == ^assigns.category_id)
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"department", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, b],
          fragment("lower(?) LIKE lower(?)", b.name, ^sanitize_term(value))
        )

      {"approval_status", value}, query when byte_size(value) > 0 ->
        where(query, [a], a.approval_status == type(^value, :integer))

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a, b, c, d], %{
      id: a.id,
      department: b.name,
      category: d.name,
      role: c.name,
      approval_status: a.approval_status,
      status: a.status,
      inserted_at: a.inserted_at,
      updated_at: a.updated_at
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("lower(?) LIKE lower(?)", b.name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", c.name, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end

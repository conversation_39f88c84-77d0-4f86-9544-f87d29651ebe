defmodule App.Service.Table.PendingAttachments do
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false

  alias App.Licenses.NoticeOfChange
  alias App.Repo

  @pagination [page_size: 10]

  def index(assigns, params) do
    compose_query(params, assigns)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(assigns, params) do
    compose_query(params, assigns)
    |> Repo.all()
  end

  def compose_query(params, _assigns) do
    NoticeOfChange
    |> join(:left, [a], b in assoc(a, :user))
    |> join(:left, [a], c in assoc(a, :license))
    |> join(:left, [a], d in assoc(a, :company))
    |> where([a], a.company_id == ^params["company_id"] and a.status == 0)
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"record_name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.record_name, ^sanitize_term(value))
        )

      {"status", value}, query when byte_size(value) > 0 ->
        where(query, [a], a.status == type(^value, :integer))

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a, b, c, d], %{
      id: a.id,
      user_name: fragment("concat(?, ' ', ?)", b.first_name, b.last_name),
      email: b.email,
      licence_name: c.name,
      company: d.name,
      status: a.status,
      inserted_at: a.inserted_at,
      updated_at: a.updated_at
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("lower(?) LIKE lower(?)", b.email, ^search_term) or
        fragment("lower(?) LIKE lower(?)", b.first_name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", c.name, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end

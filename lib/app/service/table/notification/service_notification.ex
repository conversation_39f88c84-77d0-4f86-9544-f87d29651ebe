defmodule App.Service.Table.Notifications do
  import Ecto.Query, warn: false
  alias App.{Notification.UserNotifications, Repo}

  @pagination [page_size: 10]

  def index(params, assigns) do
    compose_query(params, assigns)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def compose_query(_params, assigns) do
    UserNotifications
    |> order_by(asc: :status)
    |> check_user_role(assigns)
  end

  defp check_user_role(query, assigns) do
    if !is_nil(assigns.current_user) do
      cond do
        assigns.role_department == 8 ->
          query |> where([a], a.user_id == ^assigns.current_user.id)

        assigns.current_user.role_id not in [1, 11] ->
          query |> where([a], a.role_id == ^assigns.role_department)

        assigns.current_user.role_id in [1] ->
          query
      end
    end
  end
end

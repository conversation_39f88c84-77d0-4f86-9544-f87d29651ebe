defmodule App.Service.Table.LicenceApplications do
  @moduledoc """
  Service module for handling licence application table operations.

  This module provides functionality for querying, filtering, and paginating
  licence applications with various status filters and search capabilities.
  """

  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false

  alias App.{
    Licenses,
    Licenses.LicenseMapping
  }

  alias App.{Repo, Utilities}

  @pagination [page_size: 10]

  def index(assigns, params) do
    compose_query(params, assigns)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(assigns, params) do
    compose_query(params, assigns)
    |> Repo.all()
  end

  def compose_query(params, assigns) do
    LicenseMapping
    |> join(:left, [a], b in assoc(a, :license))
    |> where(
      [a],
      a.approved == false and
        is_nil(a.associated_license_id) and a.categories_id == ^assigns.category_id
    )
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> check_status(assigns, params["path"])
    |> compose_select()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"record_name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.record_name, ^sanitize_term(value))
        )

      {"status", value}, query when byte_size(value) > 0 ->
        where(query, [a], a.status == type(^value, :integer))

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp check_status(query, assigns, status) do
    approve_level = get_approval_levels()
    apply_status_filter(query, assigns, status, approve_level)
  end

  defp get_approval_levels do
    case Utilities.approval_levels() do
      nil -> nil
      levels -> levels
    end
  end

  defp apply_status_filter(query, assigns, "all", _approve_level) do
    status =
      Licenses.get_license_approval_status_list1(
        assigns.role_department,
        assigns.current_user.role_id,
        assigns.category_id
      )
      |> Enum.filter(&(!is_nil(&1)))
      |> Enum.uniq()

    where(
      query,
      [a],
      a.approval_status == false or a.approval_status == true or a.approved == false or
        a.approved == true
    )
  end

  defp apply_status_filter(query, assigns, "New", _approve_level) do
    status =
      Licenses.get_license_approval_status_list1(
        assigns.role_department,
        assigns.current_user.role_id,
        assigns.category_id
      )
      |> Enum.filter(&(!is_nil(&1)))
      |> Enum.uniq()

    where(query, [a], a.status in ^status and a.approval_status == true)
  end

  defp apply_status_filter(query, assigns, "Market_Operations", _approve_level) do
    status =
      Licenses.get_license_approval_status_list1(
        assigns.role_department,
        assigns.current_user.role_id,
        assigns.category_id
      )

    where(query, [a], a.status > ^status)
  end

  defp apply_status_filter(query, assigns, "Returned_to_Applicant", _approve_level) do
    subquery = Licenses.get_returned_to_applicant_query(assigns.current_user.id)
    where(query, [a], a.id in subquery(subquery) and a.status == ^(-1))
  end

  defp apply_status_filter(query, _assigns, "conditional", _approve_level) do
    where(query, [a], a.approval_status == true and a.condition_tracking == true)
  end

  defp apply_status_filter(query, assigns, "Submitted_to_Supervisor", _approve_level) do
    status_list =
      Licenses.get_license_approval_status_list1(
        assigns.role_department,
        assigns.current_user.role_id,
        assigns.category_id
      )

    IO.inspect(status_list, label: "Status List")

    subquery = Licenses.get_approved_application_query(assigns.current_user.id)
    new_status = Enum.map(status_list, &(&1 + 1))

    IO.inspect(new_status, label: "New Status")

    where(
      query,
      [a],
      a.id in subquery(subquery) and a.status in ^new_status
    )

    # where(
    #   query,
    #   [a],
    #   a.id in subquery(subquery) and a.status in ^new_status
    # )
  end

  defp apply_status_filter(query, assigns, "summary", _approve_level) do
    status_list =
      Licenses.get_license_approval_status_list1(
        assigns.role_department,
        assigns.current_user.role_id,
        assigns.category_id
      )

    subquery = Licenses.get_approved_application_query_summary(assigns.current_user.id)
    new_status = Enum.map(status_list, &(&1 + 1))

    where(
      query,
      [a],
      #  a.id in subquery(subquery) and a.status in ^new_status
      a.id in subquery(subquery) and a.status == 3
    )
  end

  defp apply_status_filter(query, _assigns, "Subordinate", _approve_level) do
    where(query, [a], a.approval_status == true and not is_nil(a.count_down_start_date))
  end

  defp apply_status_filter(query, _assigns, "LC", approve_level) do
    where(query, [a], a.status == 8 and 6 in ^approve_level)
  end

  defp apply_status_filter(query, _assigns, "Board", approve_level) do
    where(query, [a], a.status == 9 and 17 in ^approve_level)
  end

  defp apply_status_filter(query, _assigns, "Returned_from_Supervisor", _approve_level) do
    where(query, [a], a.status >= 8)
  end

  defp apply_status_filter(query, _assigns, "Processed", _approve_level) do
    where(query, [a], a.approved == true)
  end

  defp apply_status_filter(query, _assigns, _status, _approve_level) do
    query
  end

  defp compose_select(query) do
    select(query, [a, b], %{
      id: a.id,
      license_id: a.license_id,
      categories_id: b.categories_id,
      data: a.data,
      licence_name: b.name,
      certificate_id: b.certificate_id,
      associated_licenses:
        fragment(
          """
          select count(id)
          from user_license_mapping
          where associated_license_id = ?
          """,
          a.id
        ),
      record_name: a.record_name,
      revoked: a.revoked,
      show_summary: a.show_summary,
      show_evaluators: a.show_evaluators,
      status: a.status,
      condition_tracking: a.condition_tracking,
      approved: a.approved,
      inserted_at: a.inserted_at,
      updated_at: a.updated_at
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b],
      fragment("lower(?) LIKE lower(?)", a.record_name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", b.name, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end

defmodule App.Services.Table.Licenses.CertificateDraftService do
  @moduledoc false
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false
  alias App.Repo
  alias App.Licenses.LicenseCertificates

  @pagination [page_size: 10]

  def index(params) do
    compose_query(params)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(params) do
    compose_query(params)
    |> Repo.all()
  end

  defp compose_query(params) do
    LicenseCertificates
    |> filter(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp filter(query, nil), do: query

  defp filter(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_DATE(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_DATE(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a], %{
      data: a.template,
      name: a.name,
      type: a.inserted_at,
      status: fragment("true"),
      id: a.id
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a],
      fragment("lower(?) LIKE lower(?)", a.name, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end

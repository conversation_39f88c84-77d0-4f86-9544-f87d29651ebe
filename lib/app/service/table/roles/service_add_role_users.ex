defmodule App.Service.Table.AddRoleUsers do
  @moduledoc false
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false
  alias App.{Accounts.User, Repo}

  @pagination [page_size: 10]

  def index(params) do
    compose_query(params)
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(params) do
    compose_query(params["filter"])
    |> Repo.all()
  end

  defp compose_query(params) do
    User
    |> join(:left, [a], b in assoc(a, :role))
    |> join(:left, [a, b], c in assoc(b, :department))
    |> where([a, _, c], a.role_id != ^params["role_id"] and c.id != ^5)
    |> compose_filter_query(params["filter"])
    |> compose_select()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a, b, c], %{
      email: a.email,
      first_name: a.first_name,
      last_name: a.last_name,
      access_role: b.name,
      access_id: b.id,
      inserted_at: a.inserted_at,
      system_role: c.name,
      system_id: c.id,
      id: a.id,
      status: a.status,
      last: fragment("TO_CHAR(?, 'DD MON YYYY HH24:MI:SS')", a.last_logon)
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("lower(?) LIKE lower(?)", a.email, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.last_name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.first_name, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end

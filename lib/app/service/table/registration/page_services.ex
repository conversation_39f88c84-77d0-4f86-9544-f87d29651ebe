defmodule App.Table.Registration.Page.Service do
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false
  # alias App.Licenses.License
  alias App.Registration.Pages
  alias App.Repo

  @pagination [page_size: 10]
  # nfs_participates

  def index(params) do
    compose_query(params)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end
  def export(params) do
    compose_query(params)
    |> Repo.all()
  end

  @spec compose_query(nil | maybe_improper_list() | map()) :: Ecto.Query.t()
  def compose_query(params) do
     Pages
    # |> join(:left, [a], b in assoc(a, :license))
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp compose_filter_query(query, nil), do: query
  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"status", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.status, ^sanitize_term(value)))

      {"name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.name, ^sanitize_term(value))
        )

      {"url", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.url, ^sanitize_term(value))
        )

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a,b], %{
      id: a.id,
      status: a.status,
      name: a.name,
      icon: a.icon,
      # license_color: b.color,
      url: a.url,
      inserted_at: fragment("TO_CHAR (?, 'DD MON YYYY, HH24:MI:SS')", a.inserted_at)
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a],
      fragment("lower(?) LIKE lower(?)", a.name, ^search_term) or
      fragment("lower(cast(? as text)) LIKE lower(?)", a.status, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end

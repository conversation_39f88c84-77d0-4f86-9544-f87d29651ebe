defmodule Statistics.Logs.SenderMonthlyStatistic do
  use Ecto.Schema
  import Ecto.Changeset

  schema "tbl_sender_monthly_statistics" do
    field(:count, :integer)
    field(:year, :integer)
    field(:month, :integer)
    field(:status, :string)
    field(:source, :string)

    belongs_to(:sender, App.Accounts.SenderId, foreign_key: :sender_id, type: :id)
    belongs_to(:client, App.Accounts.Client, foreign_key: :client_id, type: :id)

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(monthly_statistic, attrs) do
    monthly_statistic
    |> cast(attrs, [:count, :year, :month, :status, :source, :sender_id, :client_id])
    |> validate_required([:count, :year, :month, :status, :sender_id, :client_id])
    |> unique_constraint(:count,
      name: :tbl_monthly_statistics_status_year_month_source_sender_id_index,
      message: "Already exists"
    )
  end
end

defmodule App.Service.Table.ProviderReports do
  import Ecto.Query, warn: false
  alias Reports.Context

  def index(_socket, params) do
    page_number = String.to_integer(Map.get(params, "page", "1"))

    results = compose_query(params)

    %Scrivener.Page{
      page_number: page_number,
      page_size: 10,
      total_entries: length(results),
      total_pages: div(length(results) + 10 - 1, 10),
      entries: paginate_results(results, page_number, 10)
    }
  end

  def export(params) do
    compose_query(params)
  end

  defp compose_query(params) do
    prepare_courier_stats(params)
    |> compose_filter_query(params["filter"])
  end

  defp paginate_results(results, page_number, page_size) do
    start_index = (page_number - 1) * page_size

    results
    |> Enum.slice(start_index, page_size)
    |> Enum.map(fn entry ->
      %{
        courier: entry.courier,
        failed: entry.failed,
        sent: entry.sent,
        delivered: entry.delivered,
        period: entry.period
      }
    end)
  end

  defp gen_provider_stats_report(attrs) do
    results = Enum.group_by(attrs, & &1.courier)

    for {key, values} <- results do
      %{
        courier: key,
        failed: Context.get_status_value(values, "FAILED"),
        sent: Context.get_status_value(values, "SENT"),
        delivered: Context.get_status_value(values, "DELIVERED"),
        period: List.first(attrs).period
      }
    end
  end

  defp prepare_courier_stats(%{"filter" => filter}) do
    results = check_provider_params(filter)

    with true <- length(results) > 0 do
      results
      |> gen_provider_stats_report()
    else
      _ -> []
    end
  end

  defp check_provider_params(%{"year" => year, "month" => month}) do
    Context.statistics_by_provider(String.to_integer(year), String.to_integer(month))
  end

  defp check_provider_params(_),
    do: Context.statistics_by_provider(Timex.today().year, Timex.today().month)

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, value)

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp isearch_filter(data, search_term) do
    Enum.filter(data, fn %{courier: courier} ->
      String.contains?(String.downcase(to_string(courier)), String.downcase(search_term))
    end)
  end
end

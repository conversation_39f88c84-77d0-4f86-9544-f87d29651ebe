defmodule App.Service.Table.YearlyReports do
  import Ecto.Query, warn: false
  alias Reports.Context

  def index(socket, params) do
    page_number = String.to_integer(Map.get(params, "page", "1"))

    {summary, sub_totals, total, year} = compose_query(socket, params)

    %Scrivener.Page{
      page_number: page_number,
      page_size: 10,
      total_entries: length(summary),
      total_pages: div(length(summary) + 10 - 1, 10),
      entries: paginate_results(year, summary, sub_totals, total, page_number, 10)
    }
  end

  def export(socket, params) do
    socket =
      if Map.has_key?(socket.assigns, :client) do
        socket
      else
        %{socket | assigns: Map.put(socket.assigns, :client, nil)}
      end

    page_number = String.to_integer(Map.get(params, "page", "1"))

    {summary, sub_totals, total, year} = compose_query(socket, params)
    paginate_results(year, summary, sub_totals, total, page_number, 10)
  end

  defp paginate_results(year, summary, sub_totals, total, page_number, page_size) do
    start_index = (page_number - 1) * page_size

    summary
    |> Enum.slice(start_index, page_size)
    |> Enum.map(fn entry ->
      %{
        month: entry["month"],
        failed: entry["failed"],
        sent: entry["sent"],
        invalid: entry["invalid"],
        delivered: entry["delivered"],
        total: entry["failed"] + entry["sent"] + entry["invalid"] + entry["delivered"],
        sub_totals: sub_totals,
        overall_total: total,
        year: year
      }
    end)
  end

  defp compose_query(socket, %{"filter" => filter}) do
    query_by_user =
      if is_nil(socket.assigns.client),
        do: prepare_yearly_stats(filter),
        else: prepare_yearly_stats(socket.assigns.current_user)

    {year, results} = query_by_user

    results =
      results
      |> Enum.reject(&(&1["status"] == "PENDING"))
      |> Enum.reject(&(&1["status"] == "SYSTEM_FAILURE"))

    process_yearly_stats(results, year)
  end

  defp process_yearly_stats([], year), do: {[], %{}, 0, year}

  defp process_yearly_stats(attrs, year) do
    results = gen_yearly_stats(attrs) |> Enum.sort_by(& &1["month_num"])
    total = calcu_total_yearly_stats(results)
    {results, calcu_totals(results), total, year}
  end

  defp gen_yearly_stats(results) do
    results = Enum.group_by(results, & &1["month"])

    for {key, values} <- results do
      %{
        "month" => key,
        "failed" => get_yearly_status_value(values, "FAILED"),
        "sent" => get_yearly_status_value(values, "SENT"),
        "delivered" => get_yearly_status_value(values, "DELIVERED"),
        "invalid" => get_yearly_status_value(values, "Invalid phone number"),
        "month_num" => List.first(values)["month_num"]
      }
    end
  end

  defp calcu_total_yearly_stats(results) do
    Enum.map(~w(delivered failed invalid sent), fn key ->
      Enum.reduce(results, &%{&1 | key => &1[key] + &2[key]})[key]
    end)
    |> Enum.sum()
  end

  defp prepare_yearly_stats(%{"id" => id, "year" => year}) when id not in ["", "0"] do
    {year, Context.traffic_by_year(id, String.to_integer(year))}
  end

  defp prepare_yearly_stats(%{"year" => year}) do
    {year, Context.traffic_by_year(String.to_integer(year))}
  end

  defp prepare_yearly_stats(%{"id" => id}) do
    year = Date.utc_today().year
    {year, Context.traffic_by_year(id, year)}
  end

  defp prepare_yearly_stats(_) do
    {Timex.today().year, Context.traffic_by_year(Timex.today().year)}
  end


  defp calcu_totals(results) do
    ~w(sent delivered failed invalid)
    |> Enum.map(fn status ->
      {status, Enum.reduce(results, &%{&1 | status => &1[status] + &2[status]})[status]}
    end)
    |> Enum.into(%{})
  end

  defp get_yearly_status_value(values, status) do
    result = Enum.filter(values, &(&1["status"] == status or is_nil(&1["status"])))

    with false <- Enum.empty?(result) do
      Enum.reduce(result, &%{&1 | "count" => &1["count"] + &2["count"]})["count"]
    else
      _ -> 0
    end
  end
end

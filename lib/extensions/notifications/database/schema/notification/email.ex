defmodule Notification.Notification.Email do
  use AppWeb, :schema

  schema "emails" do
    field :body, :string
    field :date_sent, :date
    field :date_time, :time
    field :message_id, :string
    field :recipient, :string
    field :response, :string
    field :status, :string
    field :subject, :string

    timestamps()
  end

  @doc false
  def changeset(email, attrs) do
    email
    |> cast(attrs, [
      :recipient,
      :subject,
      :body,
      :date_sent,
      :date_time,
      :status,
      :message_id,
      :response
    ])
    |> validate_required([:recipient, :subject, :body])
  end
end

defmodule Notification.Notification.SmsArchive do
  use AppWeb, :schema

  @columns [
    :type,
    :country_code,
    :network_name,
    :country,
    :mobile,
    :message,
    :count,
    :filename,
    :status,
    :courier_type,
    :sender,
    :sender_id,
    :source,
    :refcode,
    :courier,
    :client_id,
    :initiator_id,
    :msg_ref_number,
    :remote_ip,
    :sent_at,
    :smsc_msg_id,
    :smsc_done_dt,
    :smsc_status,
    :error_code
  ]

  schema "tbl_sms_archive" do
    field(:message, :string)
    field(:mobile, :string)
    field(:status, :string)
    field(:type, :string)
    field(:source, :string)
    field(:sender, :string)
    field(:courier, :string)
    field(:courier_type, :string)
    field(:refcode, :string)
    field(:count, :integer)
    field(:filename, :string)
    field(:country_code, :string)
    field(:network_name, :string)
    field(:country, :string)
    field(:msg_ref_number, :string)
    field(:remote_ip, :string)
    field(:sent_at, :naive_datetime)
    field(:smsc_msg_id, :string)
    field(:smsc_done_dt, :string)
    field(:smsc_status, :string)
    field(:error_code, :string)

    belongs_to(:client, App.Accounts.Client)
    belongs_to(:initiator, App.Accounts.User)
    belongs_to(:sender_ida, App.Accounts.SenderId, foreign_key: :sender_id, type: :id)

    timestamps()
  end

  @doc false
  def changeset(sms_logs, attrs) do
    sms_logs
    |> cast(attrs, @columns)
    |> validate_required([:mobile, :message, :sender_id, :sender, :client_id])
  end
end

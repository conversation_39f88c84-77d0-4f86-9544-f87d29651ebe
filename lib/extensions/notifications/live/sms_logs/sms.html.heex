<div class="px-4 sm:px-6 lg:px-8">
  <div class="sm:flex sm:items-center mt-8">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">
        Sms Logs <span class="text-red-500">(today's traffic only)</span>
      </h1>
      <p class="mt-2 text-sm text-gray-700">Logs Center</p>
    </div>
  </div>
  <!-- FILTERS -->
  <%= if @showFilter do %>
    <div class="border overflow-hidden animate-slide-in-from-top transition-all ease-in-out duration-500 rounded-lg bg-white my-4 flex flex-col gap-4 items-start">
      <h1 class="px-4 py-2 bg-gray-50 border-b font-medium w-full">Filter SMS Logs</h1>
      <FormJ.filter_form
        for={@form}
        class="w-full px-4"
        id="filter"
        phx-change="filter_change"
        phx-submit="search"
      >
        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 mb-4">
          <FormJ.input_filter
            field={@form[:client_id]}
            type="select"
            options={@clients}
            prompt="--Select Client--"
            label="Client"
          />
          <FormJ.input_filter
            field={@form[:status]}
            type="select"
            label="status"
            prompt="ALL"
            options={[
              {"INVALID PHONE NUMBER", "INVALID PHONE NUMBER"},
              {"NETWORK FAILURE", "NETWORK FAILURE"},
              {"DELIVERED", "DELIVERED"},
              {"SENT", "SENT"},
              {"FAILED", "FAILED"},
              {"PENDING", "PENDING"},
              {"UNDELIVERABLE", "UNDELIVERABLE"},
              {"EXPIRED", "EXPIRED"},
              {"CANCELLED", "CANCELLED"}
            ]}
          />
          <FormJ.input_filter
            field={@form[:mobile]}
            type="tel"
            label="Mobile"
            placeholder="e.g 260962***"
          />
          <FormJ.input_filter
            field={@form[:message_id]}
            type="text"
            label="Message ID"
            placeholder="Message ID"
          />
          <FormJ.input_filter
            field={@form[:filename]}
            type="text"
            label="Filename"
            placeholder="e.g Probase_bulk_sms"
          />
          <FormJ.input_filter
            field={@form[:sender_id]}
            type="text"
            label="Sender ID"
            placeholder="e.g Probase"
          />
          <FormJ.input_filter
            field={@form[:courier]}
            type="text"
            label="Courier(MNO)"
            placeholder="e.g MTN"
          />
        </div>

        <div class="flex gap-4 items-center justify-start mt-4 p-4 w-full border-t font-medium">
          <%= if @data_loader do %>
            <.button class="btn btn-primary">
              <svg
                class="size-5 animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                viewBox="0 0 24 24"
              >
                <polyline points="1 4 1 10 7 10"></polyline>
                <polyline points="23 20 23 14 17 14"></polyline>
                <path d="M20.49 9A9 9 0 0 0 6.6 4.11L1 10M23 14l-5.59 5.89A9 9 0 0 1 3.51 15">
                </path>
              </svg>
            </.button>
          <% else %>
            <.button
              type="button"
              phx-click="reset_filter"
              class="cursor-pointer hover:text-brand-1 py-2"
            >
              Reset
            </.button>
            <.button type="submit" class="cursor-pointer hover:text-brand-1 py-2">Search</.button>
          <% end %>
        </div>
      </FormJ.filter_form>
    </div>
  <% end %>
  <!-- END OF FILTERS -->
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <Table.main_table id="sms" rows={@data} params={@params} data_loader={@data_loader}>
          <:col :let={james} label={table_link(@params, "Mobile", :mobile)}>
            <%= james.mobile %>
          </:col>
          <:col :let={james} label={table_link(@params, "Source", :source)}>
            <%= james.source %>
          </:col>
          <:col :let={james} label={table_link(@params, "Sender ID", :sender_id)}>
            <%= james.sender_id %>
          </:col>
          <:col :let={james} label={table_link(@params, "Client", :client)}>
            <%= james.client %>
          </:col>
          <:col :let={james} label={table_link(@params, "Filename", :client)}>
            <%= james.filename %>
          </:col>
          <:col :let={james} label={table_link(@params, "Courier", :courier)}>
            <%= james.courier %>
          </:col>

          <:col :let={james} label={table_link(@params, "Status", :status)}>
            <%= james.status %>
          </:col>

          <:col :let={james} label={table_link(@params, "MSG Count", :count)}>
            <%= james.count %>
          </:col>

          <:col :let={james} label={table_link(@params, "Date Received", :updated_at)}>
            <%= Calendar.strftime(
              NaiveDateTime.add(james.updated_at, 7200, :second),
              "%d %B %Y %H:%M:%S"
            ) %>
          </:col>
        </Table.main_table>
      </div>
    </div>
  </div>
</div>

<Model.small :if={@live_action in [:filter]} id="transaction-modal" show return_to="close_model">
  <.live_component
    module={AppWeb.Component.DateFilter}
    id={:filter}
    title={@page_title}
    params={@params}
  />
</Model.small>

<Model.small
  :if={@live_action in [:view]}
  id="user-modal"
  show
  return_to="close_model"
  title={@page_title}
>
  <.live_component
    module={SmsLogs.ViewComponent}
    id={@details.id || :new}
    title={@page_title}
    action={@live_action}
    details={@details}
    browser_info={@browser_info}
    current_user={@current_user}
  />
</Model.small>

defmodule App.ExceptionLogger do
  @behaviour :gen_event

  alias Logs.Audit.ExceptionErrorLogs
  alias Logs.LogRepo, as: Repo

  def init(_args) do
    {:ok, []}
  end

  def handle_event({level, _gl, {Logger, message, _timestamp, metadata}}, state)
      when level in [:error, :warn] do
    # Ensure metadata is a map and filter non-serializable values
    serializable_metadata =
      metadata
      |> Enum.into(%{})
      |> filter_non_serializable()

    # Capture error logs and stacktraces here

    stacktrace = get_stacktrace(serializable_metadata)

    # Create a log entry and save to the database
    log_entry =
      ExceptionErrorLogs.changeset(%ExceptionErrorLogs{}, %{
        level: to_string(level),
        message: to_string(message),
        stacktrace: stacktrace,
        metadata: crash_reason(Map.drop(serializable_metadata, [:stacktrace]))
      })

    Repo.insert(log_entry)

    {:ok, state}
  end

  def handle_event(_event, state), do: {:ok, state}

  defp crash_reason(metadata) do
    {error, _} = metadata[:crash_reason] || ""

    Map.delete(metadata, :crash_reason)
    |> Map.delete(:error_logger)
    |> Map.delete(:pid)
    |> Map.delete(:gl)
    |> Map.delete(:domain)
    |> Map.delete(:mfa)
    |> Map.delete(:erl_level)
    |> Map.delete(:module)
    |> Map.put(
      :crash_reason,
      try do
        error.message
      rescue
        _ -> ""
      end
    )
  end

  def handle_call(_msg, state), do: {:ok, :ok, state}

  def handle_info(_msg, state), do: {:ok, state}

  def terminate(_reason, _state), do: :ok

  # Helper function to filter out non-serializable values (e.g., functions)
  defp filter_non_serializable(metadata) do
    metadata
    |> Enum.filter(fn {_key, value} -> serializable?(value) end)
    |> Enum.into(%{})
  end

  # Check if a value is serializable by Jason (JSON encoder)
  defp serializable?(value) when is_function(value), do: false
  defp serializable?(_value), do: true

  # Helper function to extract stacktrace from metadata
  defp get_stacktrace(metadata) do
    case Map.get(metadata, :stacktrace) do
      nil -> ""
      stacktrace -> Exception.format_stacktrace(stacktrace)
    end
  end
end

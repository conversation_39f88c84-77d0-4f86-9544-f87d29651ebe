<div class="px-4 sm:px-6 lg:px-8">
  <div class="sm:flex sm:items-center mt-8">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">API Logs Management</h1>
      <p class="mt-2 text-sm text-gray-700">Logs Center</p>
    </div>
  </div>
  <!-- FILTERS -->
  <%= if @showFilter do %>
    <div class="border overflow-hidden animate-slide-in-from-top transition-all ease-in-out duration-500 rounded-lg bg-white my-4 flex flex-col gap-4 items-start">
      <h1 class="px-4 py-2 bg-gray-50 border-b font-medium w-full">Filter APIs</h1>
      <FormJ.filter_form
        for={@form}
        class="w-full px-4"
        id="filter"
        phx-change="filter_change"
        phx-submit="search"
      >
        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 mb-4">
          <FormJ.input_filter
            field={@form[:reference]}
            type="text"
            label="Reference"
            placeholder="Reference"
          />
          <FormJ.input_filter
            field={@form[:ip_address]}
            type="text"
            label="IP Address"
            placeholder="IP Address"
          />
          <FormJ.input_filter
            field={@form[:service]}
            type="text"
            label="Service"
            placeholder="Service"
          />
        </div>
        <p class="text-gray-500 font-medium">Date Filters</p>
        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 pt-2">
          <FormJ.input_filter field={@form[:start_date]} type="date" label="From" />
          <FormJ.input_filter field={@form[:end_date]} type="date" label="To" />
        </div>
        <div class="flex gap-4 items-center justify-start mt-4 p-4 w-full border-t font-medium">
          <%= if @data_loader do %>
            <.button class="btn btn-primary">
              <svg
                class="size-5 animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                viewBox="0 0 24 24"
              >
                <polyline points="1 4 1 10 7 10"></polyline>
                <polyline points="23 20 23 14 17 14"></polyline>
                <path d="M20.49 9A9 9 0 0 0 6.6 4.11L1 10M23 14l-5.59 5.89A9 9 0 0 1 3.51 15">
                </path>
              </svg>
            </.button>
          <% else %>
            <.button
              type="button"
              phx-click="reset_filter"
              class="cursor-pointer hover:text-brand-1 py-2"
            >
              Reset
            </.button>
            <.button type="submit" class="cursor-pointer hover:text-brand-1 py-2">Search</.button>
          <% end %>
        </div>
      </FormJ.filter_form>
    </div>
  <% end %>
  <!-- END OF FILTERS -->
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <Table.main_table id="adverts" rows={@data} params={@params} data_loader={@data_loader}>
          <:col :let={james} label={table_link(@params, "Date", :inserted_at)}>
            <%= Calendar.strftime(
              NaiveDateTime.add(james.inserted_at, 7200, :second),
              "%d %B %Y %H:%M:%S"
            ) %>
          </:col>
          <:col :let={james} label={table_link(@params, "Service", :service)}>
            <%= james.service %>
          </:col>
          <:col :let={james} label={table_link(@params, "Reference", :reference)}>
            <%= james.reference %>
          </:col>
          <:col :let={james} label={table_link(@params, "IP Address", :ip_address)}>
            <%= james.ip_address %>
          </:col>
          <:col
            :let={james}
            label={table_link(@params, "External Reference", :external_reference)}
          >
            <%= james.external_reference %>
          </:col>
          <:action :let={james}>
            <.button phx-click="view_logs" class="btn btn-primary" phx-value-id={james.id}>
              View Logs
            </.button>
          </:action>
        </Table.main_table>
      </div>
    </div>
  </div>
</div>

<Model.fullscreen
  :if={@live_action in [:view_logs]}
  id="transaction1-modal"
  show
  return_to="close_model"
  title={@page_title}
>
  <.live_component
    module={AppWeb.Auth.LogsLive.ApiComponent}
    id={:view_logs}
    title={@page_title}
    @click.outside="open = false"
    record={@record}
  />
</Model.fullscreen>

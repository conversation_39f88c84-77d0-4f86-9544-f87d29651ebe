defmodule AppWeb.Auth.LogsLive.ApiComponent do
  use AppWeb, :live_component
  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div>Endpoint Accessed</div>
      <article
        class="prose prose-img:rounded-xl prose-headings:underline prose-a:text-blue-600 w-full relative"
        style="max-width: 100%"
      >
        <button
          id="copy"
          data-to="#endpoint_accessed_id"
          phx-hook="CopyInnerHTML"
          class="absolute top-1 right-1 p-1 text-gray-700 rounded z-10"
        >
          📋
        </button>
        <pre id="endpoint_accessed_id"><%= String.trim(@record.endpoint || "") %></pre>
      </article>
      <br />
      <div class="grid grid-cols-2 gap-4">
        <div>
          <div>Request Parameter</div>
          <article class="prose prose-img:rounded-xl prose-headings:underline prose-a:text-blue-600 w-full max-w-4xl relative">
            <button
              id="copy"
              data-to="#request_parameter_id"
              phx-hook="CopyInnerHTML"
              class="absolute top-1 right-1 p-1 text-gray-700 rounded z-10"
            >
              📋
            </button>
            <pre
              id="request_parameter_id"
              x-data={"{ message: JSON.stringify(#{String.trim(@record.request || "")}, undefined, 2) }"}
              x-text="message"
            ></pre>
          </article>
        </div>
        <div>
          <div>Response Body</div>
          <article class="prose prose-img:rounded-xl prose-headings:underline prose-a:text-blue-600 w-full max-w-4xl relative">
            <button
              id="copy"
              data-to="#response_body_id"
              phx-hook="CopyInnerHTML"
              class="absolute top-1 right-1 p-1 text-gray-700 rounded z-10"
            >
              📋
            </button>
            <pre
              id="response_body_id"
              x-data={"{ message: JSON.stringify(#{String.trim(@record.response || "")}, undefined, 2) }"}
              x-text="message"
            ></pre>
          </article>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    {:ok, assign(socket, assigns)}
  end
end

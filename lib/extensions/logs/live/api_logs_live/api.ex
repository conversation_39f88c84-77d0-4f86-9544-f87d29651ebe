defmodule AppWeb.Auth.LogsLive.Api do
  @moduledoc false
  use AppWeb, :live_view
  alias App.Service.Logs.ApiLogs, as: Table<PERSON><PERSON>y

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("api_logs-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"API Logs", "Accessed API Logs Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(pre_modal_title: "")
        |> assign(pre_modal_button: "")
        |> assign(live_socket_id: session["live_socket_id"])
        |> assign(pre_code: "{}")
        |> assign(showFilter: false)
        |> assign(form: LiveFunctions.filter_form(params))
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"API Logs", "Accessed API Logs Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_params(params, url, socket) do
    socket.endpoint.broadcast_from!(
      self(),
      "nav:" <> socket.assigns.live_socket_id,
      "change_nav",
      %{uri_path: URI.parse(url).path}
    )

    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply,
     socket
     |> assign(:params, params)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "API Trails")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {CodeLive, _, _} ->
        {:noreply, assign(socket, :preview_modal, false)}

      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {:search_records, params} ->
        send(self(), {:get_list, %{"filter" => params}})
        noreply(socket)

      {AppWeb.Component.DateFilter, {:filtered, data}} ->
        send(self(), {:get_list, %{"filter" => data}})

        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> assign(:page_title, "Listing API Trails")
        }
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  defp handle_event_switch(target, value, socket) do
    case target do
      "view_data_r" ->
        view_data(socket, value, "API Request")

      "view_data_e" ->
        view_data(socket, value, "API Endpoint")

      "view_data_p" ->
        if value["data"] do
          view_data(socket, value, "API Response")
        else
          view_data(socket, %{"data" => ""}, "API Response")
        end

      "view_logs" ->
        socket
        |> assign(:page_title, "Logs Details")
        |> assign(:record, Audit.get_api_log_by_id(value["id"]))
        |> assign(:live_action, :view_logs)
        |> noreply()

      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

      "filter_change" ->
        assign(socket, form: LiveFunctions.filter_form(value["filter"]))
        |> noreply()

      "filter" ->
        value = if socket.assigns.showFilter == true, do: false, else: true

        assign(socket, :showFilter, value)
        |> noreply()

      "reset_filter" ->
        socket
        |> assign(form: LiveFunctions.filter_form(%{}))
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        assign(socket, :data_loader, true)
        |> noreply()

      "export" ->
        LiveFunctions.export_records(
          socket,
          value,
          "api_logs_service",
          socket.assigns.params
        )

      "close_model" ->
        socket =
          socket
          |> assign(:new_edit_modal, false)
          |> assign(:view_modal, false)
          |> assign(:live_action, :index)
          |> apply_action(:index, value)

        {:noreply, socket}

      _ ->
        {:noreply, socket}
    end
  end

  defp list(socket, params) do
    {
      :noreply,
      assign(socket, :data, TableQuery.index(LivePageControl.create_table_params(socket, params)))
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end

  def view_data(socket, %{"data" => data} = _attrs, pre_modal_title) do
    socket =
      assign(socket, :live_action, :code)
      |> assign(pre_modal_title: pre_modal_title)
      |> assign(pre_code: data)

    {:noreply, socket}
  end
end

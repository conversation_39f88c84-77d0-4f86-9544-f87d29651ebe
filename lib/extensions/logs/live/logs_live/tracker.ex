defmodule AppWeb.Helps.Tracking do
  @moduledoc """
  LiveView module for tracking user activity and managing session timeouts.
  Displays a countdown modal when a user is inactive and handles session renewal or termination.
  """
  use AppWeb, :live_view
  alias App.{Util.DateCounter, Settings, Accounts}
  alias AppWeb.Model.SessionCountDown
  require Logger

  # Define constants for configuration
  # 10 seconds for regular checks (optimized for performance)
  @check_interval 10_000
  # 1 second for modal updates (keeps countdown accurate)
  @modal_interval 1_000
  # How fast the modal countdown decreases
  @modal_decay_rate 1
  @debug_logging Mix.env() == :dev

  @impl true
  def render(assigns) do
    ~H"""
    <div
      phx-hook="ActivityTracker"
      id={"activity-tracker-#{:os.system_time(:millisecond)}"}
      phx-update="ignore"
    >
    </div>
    <%= if @activity_show_modal do %>
      <.live_component
        module={SessionCountDown}
        id="session-countdown"
        time={@activity_time_out}
        current={@activity_modal_current_time}
        max={@activity_modal_max_time}
        theme_color="#1C233A"
        warning_threshold={30}
        left_button={@activity_left_button}
        right_button={@activity_right_button}
        left_button_action={@activity_left_button_action}
        right_button_action={@activity_right_button_action}
        show={@modal_animated_state}
      />
    <% end %>
    """
  end

  @impl true
  def mount(_params, session, socket) do
    {inactivity_timeout, modal_timeout} = get_timeouts()

    # Only start the timer if the socket is connected
    if connected?(socket), do: schedule_timer(:check_activity)

    log_debug("Mounted tracking module with timeout: #{inactivity_timeout}")

    socket
    |> assign_initial_state(session["user_token"], inactivity_timeout, modal_timeout)
    |> ok()
  end

  @impl true
  def handle_info(message, socket), do: handle_info_by_type(message, socket)

  @impl true
  def handle_event("activity", params, socket) do
    if @debug_logging do
      timestamp = params["timestamp"] || :os.system_time(:millisecond)
      log_debug("Activity detected at #{timestamp}, resetting timer")
    end

    if socket.assigns.activity_check do
      # Update user activity timestamp in the database
      Accounts.update_where_session_token_by_token(socket.assigns.token)

      # Reset the inactivity timer
      reset_inactivity_timer(socket)
      |> noreply()
    else
      noreply(socket)
    end
  end

  # Private helper functions

  # Handle different types of info messages
  defp handle_info_by_type(:check_activity, socket), do: check_activity(socket)
  defp handle_info_by_type(:update_modal, socket), do: update_modal(socket)
  defp handle_info_by_type(:countdown_complete, socket), do: handle_countdown_complete(socket)
  defp handle_info_by_type(:log_out, socket), do: log_out(socket)

  # Handle messages from SessionCountDown component
  defp handle_info_by_type({SessionCountDown, :button_clicked, %{action: :drop}}, socket) do
    log_debug("User clicked Sign Out in modal")
    log_out(socket)
  end

  defp handle_info_by_type({SessionCountDown, :button_clicked, %{action: :continue}}, socket) do
    log_debug("User clicked Continue Working in modal")
    renew_session(socket)
  end

  # Fallback for unknown messages
  defp handle_info_by_type(unknown_message, socket) do
    log_debug("Received unknown message: #{inspect(unknown_message)}")
    noreply(socket)
  end

  # Assign initial state to the socket
  defp assign_initial_state(socket, token, inactivity_timeout, modal_timeout) do
    socket
    |> assign(:token, token)
    |> assign(:activity_time_out, format_time_remaining(modal_timeout))
    |> assign(:activity_left_button, "Sign Out")
    |> assign(:activity_right_button, "Continue Working")
    |> assign(:activity_check, true)
    |> assign(:activity_right_button_action, :continue)
    |> assign(:activity_left_button_action, :drop)
    |> assign(:activity_max_time, inactivity_timeout)
    |> assign(:activity_current_time, inactivity_timeout)
    |> assign(:activity_modal_timeout_time, modal_timeout)
    |> assign(:activity_modal_timeout_time_counter, modal_timeout)
    |> assign(:activity_modal_max_time, 630)
    |> assign(:activity_modal_current_time, 630)
    |> assign(:activity_show_modal, false)
    # Added for animation control
    |> assign(:modal_animated_state, true)
  end

  # Get timeout values from settings
  defp get_timeouts do
    {
      Settings.get_setting_configuration("user_inactive_session_notification")
      |> Settings.date_time_calculator(),
      Settings.get_setting_configuration("inactive_user_model_timeout")
      |> Settings.date_time_calculator()
    }
  end

  # Schedule the next timer check
  defp schedule_timer(timer_type, interval \\ @check_interval) do
    Process.send_after(self(), timer_type, interval)
  end

  # Reset the inactivity timer when user shows activity
  defp reset_inactivity_timer(socket) do
    assign(socket, :activity_current_time, socket.assigns.activity_max_time)
  end

  # Format time remaining in MM:SS format
  defp format_time_remaining(seconds) when is_integer(seconds) do
    DateCounter.seconds_to_time(seconds)
  end

  defp format_time_remaining(_), do: "00:00"

  # Calculate progress percentage for the modal
  defp calculate_progress_percentage(current, total) do
    (current / total * 100)
    |> max(0)
    |> min(100)
  end

  # Check if the user has been inactive
  defp check_activity(%{assigns: assigns} = socket) do
    if assigns.activity_check do
      new_time = max(0, assigns.activity_current_time - @check_interval / 1000)

      cond do
        new_time <= 0 ->
          # User has been inactive, show the modal
          log_debug("User inactive, showing countdown modal")
          schedule_timer(:update_modal, @modal_interval)

          socket
          |> assign(:activity_check, false)
          |> assign(:activity_show_modal, true)
          |> noreply()

        true ->
          # User is still active, continue checking
          schedule_timer(:check_activity)

          assign(socket, :activity_current_time, new_time)
          |> noreply()
      end
    else
      {:noreply, socket}
    end
  end

  # Update the countdown modal
  defp update_modal(%{assigns: assigns} = socket) do
    remaining_time = max(0, assigns.activity_modal_timeout_time_counter - @modal_decay_rate)

    # Calculate current percentage for progress visualization
    percentage =
      calculate_progress_percentage(remaining_time, assigns.activity_modal_timeout_time)

    current_time = percentage / 100 * assigns.activity_modal_max_time

    updated_socket =
      socket
      |> assign(:activity_time_out, format_time_remaining(remaining_time))
      |> assign(:activity_modal_current_time, current_time)
      |> assign(:activity_modal_timeout_time_counter, remaining_time)

    if remaining_time <= 0 do
      # Time's up, trigger logout
      log_debug("Countdown completed, logging out user")
      schedule_timer(:countdown_complete)
    else
      # Continue countdown
      schedule_timer(:update_modal, @modal_interval)
    end

    noreply(updated_socket)
  end

  # Handle countdown completion
  defp handle_countdown_complete(socket) do
    log_debug("Preparing for logout")
    # Short delay before logout
    schedule_timer(:log_out, 100)
    noreply(socket)
  end

  # Renew the user session
  defp renew_session(%{assigns: assigns} = socket) do
    # Update session token
    {:ok, %{user: _token}} = Accounts.update_where_session_token_by_token(assigns.token)

    log_debug("Session renewed, resetting timers")

    # Reset all timers and modal state
    updated_socket =
      socket
      |> assign(:activity_modal_timeout_time_counter, assigns.activity_modal_timeout_time)
      |> assign(:activity_show_modal, false)
      |> assign(:activity_check, true)
      |> assign(:modal_animated_state, false)
      |> assign(:activity_current_time, assigns.activity_max_time)

    # Restart activity checking
    schedule_timer(:check_activity)

    noreply(updated_socket)
  end

  # Log out the user
  defp log_out(socket) do
    log_debug("Logging out user")
    noreply(redirect(socket, to: ~p"/users/log_out"))
  end

  # Helper for debug logging
  defp log_debug(message) do
    if @debug_logging do
      Logger.debug("[ActivityTracking] #{message}")
    else
      :ok
    end
  end
end

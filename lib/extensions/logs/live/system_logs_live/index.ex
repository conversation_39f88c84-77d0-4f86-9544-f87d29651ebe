defmodule AppWeb.Auth.LogsLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias App.Service.Logs.System, as: TableAuditQuery

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("access_logs-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Audit Logs", "Accessed Audit Logs Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()
        |> assign(showFilter: false)
        |> assign(form: LiveFunctions.filter_form(params))
        |> assign(params: params)

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Audit Logs", "Accessed Audit Logs Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {AppWeb.UserLive.FormComponent, {:saved, _user}} ->
        after_db_change(data, socket)

      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {:search_records, params} ->
        send(self(), {:get_list, %{"filter" => params}})
        noreply(socket)

      {AppWeb.Component.DateFilter, {:filtered, data}} ->
        send(self(), {:get_list, %{"filter" => data}})

        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }

      {AppWeb.LiveFunctions, message} ->
        send(self(), {:get_list, %{}})

        {
          :noreply,
          socket
          |> put_flash(:info, message)
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }
    end
  end

  @impl true
  def handle_event(target, value, socket) do
    case target do
      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

      "filter_change" ->
        assign(socket, form: LiveFunctions.filter_form(value["filter"]))
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        assign(socket, :data_loader, true)
        |> noreply()

      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "export" ->
        LiveFunctions.export_records(
          socket,
          value,
          "system_audit_logs_service",
          socket.assigns.params
        )

      "filter" ->
        value = if socket.assigns.showFilter == true, do: false, else: true

        assign(socket, :showFilter, value)
        |> noreply()

      "reset_filter" ->
        socket
        |> assign(form: LiveFunctions.filter_form(%{}))
        |> noreply()

      "close_model" ->
        socket =
          socket
          |> assign(:new_edit_modal, false)
          |> assign(:view_modal, false)
          |> assign(:live_action, :index)
          |> apply_action(:index, value)

        {:noreply, socket}

      _ ->
        {:noreply, socket}
    end
  end

  def after_db_change({AppWeb.UserLive.FormComponent, {:saved, _user}}, socket) do
    send(self(), {:get_list, %{}})

    {:noreply,
     socket
     |> assign(:live_action, :index)
     |> assign(:page_title, "")}
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "Audit Management")
  end

  defp list(socket, params) do
    {
      :noreply,
      assign(
        socket,
        :data,
        TableAuditQuery.index(LivePageControl.create_table_params(socket, params))
      )
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end
end

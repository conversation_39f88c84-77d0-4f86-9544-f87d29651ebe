defmodule Logs.Health.HealthCheck do
  use Ecto.Schema
  import Ecto.Changeset
  alias App.Database.CustomDB

  schema "tbl_health_check" do
    field :endpoint, :string
    field :name, :string
    field :last_seen, :naive_datetime
    field :status, :integer
    field :endpoint_status, :integer

    timestamps()
  end

  @doc false
  def changeset(logs_api, attrs) do
    logs_api
    |> cast(attrs, [:endpoint, :name, :last_seen, :status, :endpoint_status])
    |> validate_required([:endpoint, :name])
    |> validate_u()
    |> CustomDB.trim_data(:endpoint, 600)
    |> CustomDB.trim_data(:name, 200)
  end

  @doc false
  def update_changeset(logs_api, attrs) do
    logs_api
    |> cast(attrs, [
      :endpoint,
      :name,
      :last_seen,
      :status,
      :endpoint_status
    ])
    |> CustomDB.trim_data(:endpoint, 600)
    |> CustomDB.trim_data(:name, 200)
  end

  defp validate_u(changeset) do
    changeset
    |> unsafe_validate_unique([:endpoint, :name], Logs.LogRepo)
    |> unique_constraint([:endpoint, :name])
  end
end

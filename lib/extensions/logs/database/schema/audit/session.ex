defmodule Logs.Audit.Session do
  use AppWeb, :schema

  schema "logs_session" do
    field :browser_details, :string
    field :description, :string
    field :device_type, :string
    field :device_uuid, :string
    field :full_browser_name, :string
    field :ip_address, :string
    field :known_browser, :boolean, default: false
    field :log_in_at, :naive_datetime
    field :log_out_at, :naive_datetime
    field :portal, :string
    field :session_id, :string
    field :status, :boolean, default: false
    field :system_platform_name, :string

    belongs_to :user, App.Accounts.User

    timestamps()
  end

  @doc false
  def changeset(session, attrs) do
    session
    |> cast(attrs, [
      :session_id,
      :portal,
      :description,
      :device_uuid,
      :ip_address,
      :full_browser_name,
      :browser_details,
      :system_platform_name,
      :device_type,
      :known_browser,
      :status,
      :log_out_at,
      :log_in_at,
      :user_id
    ])
  end
end

defmodule Logs.Audit.Api do
  use AppWeb, :schema
  alias App.Database.CustomDB

  schema "logs_api" do
    field :caller_id, :integer
    field :caller_type, :string
    field :ip_address, :string
    field :endpoint, :string
    field :external_reference, :string
    field :reference, :string
    field :request, :string
    field :response, :string
    field :service, :string

    timestamps()
  end

  @doc false
  def changeset(logs_api, attrs) do
    logs_api
    |> cast(attrs, [
      :reference,
      :endpoint,
      :ip_address,
      :external_reference,
      :service,
      :request,
      :response,
      :caller_id,
      :caller_type
    ])
    |> CustomDB.valid_reference_st(logs_api.reference || attrs[:reference])
    |> CustomDB.trim_data(:reference, 30)
    |> CustomDB.trim_data(:endpoint, 300)
    |> CustomDB.trim_data(:external_reference, 200)
    |> CustomDB.trim_data(:service, 150)
    |> CustomDB.trim_data(:request, 10_000)
    |> CustomDB.trim_data(:response, 600_000)
    |> CustomDB.trim_data(:caller_type, 30)
  end

  @doc false
  def update_changeset(logs_api, attrs) do
    logs_api
    |> cast(attrs, [
      :reference,
      :endpoint,
      :ip_address,
      :external_reference,
      :service,
      :request,
      :response,
      :caller_id,
      :caller_type
    ])
    |> CustomDB.trim_data(:reference, 30)
    |> CustomDB.trim_data(:endpoint, 300)
    |> CustomDB.trim_data(:external_reference, 200)
    |> CustomDB.trim_data(:service, 150)
    |> CustomDB.trim_data(:request, 10_000)
    |> CustomDB.trim_data(:response, 600_000)
    |> CustomDB.trim_data(:caller_type, 30)
  end
end

defmodule Logs.Audit.ExceptionErrorLogs do
  use AppWeb, :schema
  alias App.Database.CustomDB

  schema "exception_error_logs" do
    field :level, :string
    field :message, :string
    field :stacktrace, :string
    field :metadata, :map
    timestamps()
  end

  def changeset(log, attrs) do
    log
    |> cast(attrs, [:level, :message, :stacktrace, :metadata])
    |> validate_required([:level, :message])
    |> CustomDB.trim_data(:message, 4999)
  end
end

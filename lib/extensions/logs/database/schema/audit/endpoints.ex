defmodule Logs.Audit.Endpoints do
  use AppWeb, :schema

  schema "logs_endpoints" do
    field :alive, :string
    field :host, :string
    field :name, :string
    field :numeric_host, :string
    field :output, :string

    timestamps()
  end

  @doc false
  def changeset(endpoints, attrs) do
    endpoints
    |> cast(attrs, [:name, :host, :alive, :output, :numeric_host])
    |> validate_required([:name, :host, :alive, :output, :numeric_host])
  end
end

defmodule Logs.Audit do
  @moduledoc """
  The Audit context.
  """

  import Ecto.Query, warn: false

  alias Logs.{
    Audit.Access,
    Audit.Api,
    Audit.Session,
    Audit.System
  }

  alias App.Util.CustomTime

  alias Logs.LogRepo, as: Repo

  def get_api_log_response_by_reference(reference) do
    Api
    |> where([a], a.reference == ^reference)
    |> order_by(desc: :id)
    |> select([a], a.response)
    |> limit(1)
    |> Repo.one()
  end

  def create_session_on_login_multi(
        multi,
        {conn, session_id, portal, description, user_id, multi_name},
        status \\ false
      ) do
    Ecto.Multi.insert(
      multi,
      multi_name,
      change_session(
        %Session{},
        %{
          session_id: session_id,
          portal: portal,
          description: description,
          device_uuid: device_uuid(conn),
          full_browser_name: Browser.full_browser_name(conn),
          user_id: user_id,
          browser_details: Browser.full_display(conn),
          system_platform_name: Browser.full_platform_name(conn),
          device_type: to_string(Browser.device_type(conn)),
          known_browser: Browser.known?(conn),
          status: status,
          ip_address: ip_address(conn),
          log_in_at: CustomTime.local_datetime()
        }
      )
    )
  end

  def create_access(session, {page, description, route}, {page_access, user_id}) do
    change_access(
      %Access{},
      %{
        description: description,
        page: page,
        page_access: page_access,
        route: route,
        session_id: session["live_socket_id"],
        user_id: user_id
      }
    )
    |> Repo.insert!()
  end

  def page_access(page, permissions), do: Enum.member?(permissions, page)

  def find_and_update_session_id_log_out(
        session_id,
        user_id,
        message \\ "Session Force Close Successfully"
      ) do
    Session
    |> where([a], a.session_id == ^session_id and a.user_id == ^user_id)
    |> Repo.all()
    |> Enum.with_index()
    |> Enum.reduce(Ecto.Multi.new(), fn {session, idx}, multi ->
      Ecto.Multi.update(
        multi,
        {:session, idx},
        Session.changeset(session, %{
          status: false,
          description: message,
          log_out_at: NaiveDateTime.truncate(NaiveDateTime.utc_now(), :second)
        })
      )
    end)
    |> Repo.transaction()
  end

  def create_session_on_login(conn, {session_id, portal, description, user_id}, status \\ false) do
    %Session{}
    |> change_session(%{
      session_id: session_id,
      portal: portal,
      description: description,
      device_uuid: device_uuid(conn),
      full_browser_name: Browser.full_browser_name(conn),
      user_id: user_id,
      browser_details: Browser.full_display(conn),
      system_platform_name: Browser.full_platform_name(conn),
      device_type: to_string(Browser.device_type(conn)),
      known_browser: Browser.known?(conn),
      status: status,
      ip_address: ip_address(conn),
      log_in_at: CustomTime.local_datetime()
    })
    |> Repo.insert()
  end

  def create_system_log_session_live_multi(
        multi,
        %{assigns: assigns},
        narration,
        action,
        attrs,
        service
      ) do
    Ecto.Multi.run(multi, :system_logs, fn _, _ ->
      %System{}
      |> change_system(%{
        narration: narration,
        action: action,
        attrs: Jason.encode!(attrs),
        service: service,
        session_id: assigns[:live_socket_identifier],
        ip_address: assigns.browser_info["remote_ip"],
        device_uuid: assigns.browser_info["user_agent"],
        full_browser_name: assigns.browser_info["full_browser_name"],
        browser_details: assigns.browser_info["browser_details"],
        system_platform_name: assigns.browser_info["system_platform_name"],
        device_type: to_string(assigns.browser_info["device_type"]),
        known_browser: assigns.browser_info["known_browser"],
        user_id: if(assigns[:current_user], do: assigns.current_user.id)
      })
      |> Repo.insert()
      |> case do
        {:ok, sms_log} -> {:ok, sms_log}
        {:error, reason} -> {:error, reason.errors}
      end
    end)
  end

  def change_session(%Session{} = session, attrs \\ %{}) do
    Session.changeset(session, attrs)
  end

  def change_access(%Access{} = access, attrs \\ %{}) do
    Access.changeset(access, attrs)
  end

  def change_system(%System{} = system, attrs \\ %{}) do
    System.changeset(system, attrs)
  end

  def get_api_log_by_id(id) do
    Api
    |> where([a], a.id == ^id)
    |> Repo.one()
  end

  def api_log_insert_socket!(socket, service, request, endpoint, external_reference \\ "") do
    Api.changeset(
      %Api{},
      %{
        service: service,
        request: Jason.encode!(request),
        endpoint: endpoint,
        ip_address: socket.assigns.browser_info["remote_ip"],
        external_reference: external_reference
      }
    )
    |> Repo.insert!()
  end

  def api_log_insert!(conn, service, request, endpoint, external_reference \\ "") do
    Api.changeset(
      %Api{},
      %{
        service: service,
        request: Jason.encode!(request),
        endpoint: endpoint,
        ip_address: ip_address(conn),
        external_reference: external_reference
      }
    )
    |> Repo.insert!()
  end

  def api_log_insert(service, request, endpoint, reference \\ nil) do
    Api.changeset(
      %Api{},
      %{
        reference: reference,
        service: service,
        request: Jason.encode!(request),
        endpoint: endpoint
      }
    )
    |> Repo.insert!()
  end

  def api_log_insert_callback(service, request, endpoint, external_reference) do
    Api.changeset(
      %Api{},
      %{
        service: service,
        request: request,
        endpoint: endpoint,
        external_reference: external_reference
      }
    )
    |> Repo.insert!()
  end

  def api_log_update!(struct, response, reference \\ nil) do
    Api.update_changeset(
      struct,
      %{
        response: Jason.encode!(response),
        external_reference: reference
      }
    )
    |> Repo.update!()
  end

  def get_api_log_by_external_ref(reference) do
    Api
    |> where([a], a.external_reference == ^reference)
    |> Repo.one()
  end

  def api_log_update_by_external_reference(reference, attrs \\ %{}) do
    Api.changeset(
      get_api_log_by_external_ref(reference),
      %{response: Jason.encode!(attrs)}
    )
    |> Repo.update!()
  end

  def system_log_live(%{assigns: assigns}, narration, action, attrs, service, user_id) do
    System.changeset(
      %System{},
      %{
        narration: narration,
        action: action,
        attrs: Jason.encode!(attrs),
        service: service,
        session_id: assigns.live_socket_identifier,
        ip_address: assigns.remote_ip,
        device_uuid: assigns.user_agent,
        full_browser_name: assigns.browser_info["full_browser_name"],
        browser_details: assigns.browser_info["browser_details"],
        system_platform_name: assigns.browser_info["system_platform_name"],
        device_type: to_string(assigns.browser_info["device_type"]),
        known_browser: assigns.browser_info["known_browser"],
        user_id: user_id
      }
    )
    |> Repo.insert!()
  end

  def ip_address(conn, _live \\ false) do
    forwarded_for = List.first(Plug.Conn.get_req_header(conn, "x-forwarded-for"))

    if forwarded_for do
      String.split(forwarded_for, ",")
      |> Enum.map(&String.trim/1)
      |> List.first()
    else
      to_string(:inet_parse.ntoa(conn.remote_ip))
    end
  end

  def device_uuid(conn) do
    Plug.Conn.get_req_header(conn, "user-agent")
    |> List.first()
  end

  def create_log_socket_from_nothing(user \\ nil, browser_info \\ "Telegram") do
    %{
      assigns: %{
        user_agent: "Application",
        remote_ip: "127.0.0.1",
        browser_info: %{
          "known_browser" => true,
          "full_browser_name" => browser_info,
          "browser_details" => "Job",
          "system_platform_name" => "Job",
          "device_type" => "Job"
        },
        current_user: user
      }
    }
  end

  def create_log_socket_from_conn(conn) do
    %{
      assigns:
        Map.merge(conn.assigns, %{
          user_agent: "API Application",
          remote_ip: ip_address(conn),
          browser_info: %{
            "known_browser" => Browser.known?(conn),
            "full_browser_name" => Browser.full_browser_name(conn),
            "browser_details" => Browser.full_display(conn),
            "system_platform_name" => Browser.full_platform_name(conn),
            "device_type" => to_string(Browser.device_type(conn))
          }
        })
    }
  end
end

defmodule App.Service.Export.CsvApiLogs do
  @moduledoc false
  alias App.Service.Logs.ApiLogs

  @headers [
    "DATE",
    "SERVICE",
    "REFERENCE",
    "IP ADDRESS",
    "EXTERNAL REFERENCE",
    "ENDPOINT",
    "REQUEST",
    "RESPONSE"
  ]

  def index(payload) do
    ApiLogs.export(payload)
    |> Stream.map(
      &[
        Calendar.strftime(NaiveDateTime.add(&1.inserted_at, 7200, :second), "%d %B %Y %H:%M:%S"),
        &1.service,
        "'#{&1.reference}",
        &1.ip_address,
        &1.external_reference,
        &1.endpoint,
        &1.request,
        &1.response
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Api Logs"],
              ["", "", "", "", ""],
              @headers,
              ["", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end

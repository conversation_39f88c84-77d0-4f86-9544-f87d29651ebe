defmodule App.Service.Export.ApiLogsServicePdf do
  @moduledoc false

  alias App.Service.Logs.ApiLogs

  def index(payload) do
    results =
      ApiLogs.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "inserted_at" =>
            Calendar.strftime(
              NaiveDateTime.add(data.inserted_at, 7200, :second),
              "%d %B %Y %H:%M:%S"
            ),
          "service" => data.service || "",
          "reference" => data.reference || "",
          "ip_address" => data.ip_address || "",
          "external_reference" => data.external_reference || "",
          "endpoint" => data.endpoint || ""
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td>{{ inserted_at }}</td>
            <td style="text-align: center;">{{ service }}</td>
            <td>{{ reference }}</td>
            <td>{{ ip_address }}</td>
            <td>{{ external_reference }}</td>
            <td>{{ endpoint }}</td>
         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/logs/logs_api.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Batch Transaction",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end

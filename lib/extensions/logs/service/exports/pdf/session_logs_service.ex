defmodule App.Service.Export.SessionLogsServicePdf do
  @moduledoc false

  alias App.Service.Logs.LogsSession

  alias App.Service.Export.{
    Functions
  }

  def index(payload) do
    results =
      LogsSession.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "inserted_at" => Calendar.strftime(data.inserted_at, "%d/%m/%Y"),
          "user_id" => data.user_id,
          "portal" => data.portal,
          "status" => Functions.table_numeric_status(data.status),
          "description" => data.description
        }
      end)
      |> Enum.map(fn data ->
        """
        <tr>
            <td>{{ inserted_at }}</td>
            <td>{{ user_id }}</td>
            <td style="text-align: center;">{{ portal }}</td>
            <td style="text-align: center;">{{ status }}</td>
            <td>{{ description }}</td>
        </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/logs/session_logs_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Session Logs",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end

defmodule App.Service.Export.SystemAuditLogsServicePdf do
  @moduledoc false
  alias App.Service.Logs.System

  def index(payload) do
    results =
      System.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "inserted_at" => Calendar.strftime(data.inserted_at, "%d/%m/%Y"),
          "user" => data.user || "",
          "service" => data.service || "",
          "action" => data.action || "",
          "ip_address" => data.ip_address || "",
          "narration" => data.narration || ""
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td>{{ inserted_at }}</td>
            <td>{{ user }}</td>
            <td style="text-align: center;">{{ service }}</td>
            <td style="text-align: center;">{{ action }}</td>
            <td>{{ ip_address }}</td>
            <td>{{ narration }}</td>
         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/logs/system_audit_logs_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "System Audit Logs",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end

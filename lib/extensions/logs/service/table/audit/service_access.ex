defmodule App.Service.Logs.AccessLogs do
  @moduledoc false
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false
  alias App.Accounts
  alias Logs.LogRepo, as: Repo
  alias Logs.Audit.Access

  @pagination [page_size: 10]

  def index(params) do
    compose_query(params)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
    |> (fn %{entries: entries} = result ->
          Map.put(
            result,
            :entries,
            Enum.map(
              entries,
              &Map.merge(&1, %{user: Accounts.get_username_as_email_by_id(&1.user)})
            )
          )
        end).()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(String.trim(value)))

      {"user_id", value}, query when byte_size(value) > 0 ->
        where(query, [a, b], fragment("lower(?) LIKE lower(?)", b.username, ^value))

      {"start_date", value}, query when byte_size(value) > 0 ->
        naive_datetime = NaiveDateTime.from_iso8601!("#{value} 00:00:00")
        where(query, [s, c, d], s.inserted_at >= ^naive_datetime)

      {"end_date", value}, query when byte_size(value) > 0 ->
        naive_datetime = NaiveDateTime.from_iso8601!("#{value} 23:59:59")
        where(query, [s, c, d], s.inserted_at <= ^naive_datetime)

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_query(params) do
    Access
    |> where([a], a.session_id == ^params["session_id"])
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp compose_select(query) do
    select(query, [a, b], %{
      page: a.page,
      page_access: a.page_access,
      route: a.route,
      description: a.description,
      user: a.user_id,
      inserted_at: a.inserted_at
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b],
      fragment("lower(?) LIKE lower(?)", a.description, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end

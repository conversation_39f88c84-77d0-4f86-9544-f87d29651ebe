# SEC Client Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Environment
MIX_ENV=prod
RELEASE_NAME=sec_client_rel

# Database Configuration
# Format: ecto://username:password@hostname:port/database_name
DATABASE_URL=ecto://postgres:postgres@localhost:5432/sec_client_prod

# Phoenix Configuration
SECRET_KEY_BASE=your_very_long_secret_key_base_here_at_least_64_characters_long
PHX_HOST=localhost

# Migration Settings
RUN_MIGRATIONS=true

# Optional: Additional Phoenix Configuration
# PHX_SERVER=true
# PORT=4000

# Optional: Email Configuration (if using Swoosh)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_app_password

# Optional: External API Configuration
# API_KEY=your_api_key_here
# API_BASE_URL=https://api.example.com

# Optional: File Upload Configuration
# UPLOAD_PATH=/opt/sec_client/uploads
# MAX_UPLOAD_SIZE=10485760

# Optional: Logging Configuration
# LOG_LEVEL=info

image: elixir:1.14

# Pick zero or more services to be used on all builds.
services:
  - postgres:latest

variables:
  POSTGRES_DB: app_test
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  MIX_ENV: test
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  # Registry variables - adjust these according to your GitLab setup
  REGISTRY_IMAGE: $CI_REGISTRY_IMAGE
  IMAGE_TAG: $CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA

stages:
  - format
  - compile
  - test
  - build
  - deploy

before_script:
  - mix local.rebar --force
  - mix local.hex --force
  - mix deps.get

# Check code formatting
format:
  stage: format
  script:
    - mix format --check-formatted
  allow_failure: false
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# Compile with warnings as errors
compile:
  stage: compile
  script:
    - mix compile --warnings-as-errors
  allow_failure: false
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# Run tests
test:
  stage: test
  script:
    - mix test
  dependencies:
    - format
    - compile
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# Build Docker image
build:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - docker build -t $REGISTRY_IMAGE:$IMAGE_TAG .
    - docker build -t $REGISTRY_IMAGE:latest .
    - docker push $REGISTRY_IMAGE:$IMAGE_TAG
    - docker push $REGISTRY_IMAGE:latest
  rules:
    - if: $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# Deploy to staging
deploy_staging:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client bash
    - eval $(ssh-agent -s)
    - echo "$STAGING_SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $STAGING_HOST >> ~/.ssh/known_hosts
  script:
    - |
      ssh $STAGING_USER@$STAGING_HOST << 'EOF'
        cd /opt/sec_client
        echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
        docker pull $REGISTRY_IMAGE:$IMAGE_TAG
        docker stop sec_client_app || true
        docker rm sec_client_app || true
        docker run -d \
          --name sec_client_app \
          --restart always \
          -p 8500:80 \
          -e MIX_ENV=prod \
          -e RELEASE_NAME=sec_client_rel \
          -e RUN_MIGRATIONS=true \
          -e DATABASE_URL="$DATABASE_URL" \
          -e SECRET_KEY_BASE="$SECRET_KEY_BASE" \
          -e PHX_HOST="$PHX_HOST" \
          $REGISTRY_IMAGE:$IMAGE_TAG
        docker system prune -f
      EOF
  environment:
    name: staging
    url: http://$STAGING_HOST:8500
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
  when: manual

# Deploy to production
deploy_production:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client bash
    - eval $(ssh-agent -s)
    - echo "$PRODUCTION_SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $PRODUCTION_HOST >> ~/.ssh/known_hosts
  script:
    - |
      ssh $PRODUCTION_USER@$PRODUCTION_HOST << 'EOF'
        cd /opt/sec_client
        echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
        docker pull $REGISTRY_IMAGE:$IMAGE_TAG
        docker stop sec_client_app || true
        docker rm sec_client_app || true
        docker run -d \
          --name sec_client_app \
          --restart always \
          -p 8500:80 \
          -e MIX_ENV=prod \
          -e RELEASE_NAME=sec_client_rel \
          -e RUN_MIGRATIONS=true \
          -e DATABASE_URL="$DATABASE_URL" \
          -e SECRET_KEY_BASE="$SECRET_KEY_BASE" \
          -e PHX_HOST="$PHX_HOST" \
          $REGISTRY_IMAGE:$IMAGE_TAG
        docker system prune -f
      EOF
  environment:
    name: production
    url: http://$PRODUCTION_HOST:8500
  rules:
    - if: $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "master"
  when: manual

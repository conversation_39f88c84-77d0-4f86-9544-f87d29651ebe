#!/bin/bash

# SEC Client Setup Script
# This script helps set up the development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}      SEC Client Setup Script   ${NC}"
    echo -e "${BLUE}================================${NC}"
    echo
}

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependency() {
    if command -v $1 &> /dev/null; then
        print_status "$1 is installed ✓"
        return 0
    else
        print_error "$1 is not installed ✗"
        return 1
    fi
}

generate_secret() {
    if command -v openssl &> /dev/null; then
        openssl rand -base64 48
    elif command -v mix &> /dev/null; then
        mix phx.gen.secret
    else
        # Fallback method
        head -c 48 /dev/urandom | base64
    fi
}

print_header

# Check dependencies
print_status "Checking dependencies..."
echo

MISSING_DEPS=0

if ! check_dependency "elixir"; then
    echo "  Install Elixir: https://elixir-lang.org/install.html"
    MISSING_DEPS=1
fi

if ! check_dependency "mix"; then
    echo "  Mix should be installed with Elixir"
    MISSING_DEPS=1
fi

if ! check_dependency "node"; then
    echo "  Install Node.js: https://nodejs.org/"
    MISSING_DEPS=1
fi

if ! check_dependency "docker"; then
    echo "  Install Docker: https://docs.docker.com/get-docker/"
    MISSING_DEPS=1
fi

if ! check_dependency "git"; then
    echo "  Install Git: https://git-scm.com/downloads"
    MISSING_DEPS=1
fi

echo

if [ $MISSING_DEPS -eq 1 ]; then
    print_error "Please install missing dependencies before continuing."
    exit 1
fi

print_status "All dependencies are installed!"
echo

# Setup environment file
if [ ! -f ".env" ]; then
    print_status "Creating .env file from template..."
    cp .env.example .env
    
    # Generate a secret key
    print_status "Generating SECRET_KEY_BASE..."
    SECRET_KEY=$(generate_secret)
    
    # Replace the placeholder in .env file
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/your_very_long_secret_key_base_here_at_least_64_characters_long/$SECRET_KEY/" .env
    else
        # Linux
        sed -i "s/your_very_long_secret_key_base_here_at_least_64_characters_long/$SECRET_KEY/" .env
    fi
    
    print_status ".env file created with generated SECRET_KEY_BASE"
    print_warning "Please review and update the .env file with your specific configuration"
else
    print_warning ".env file already exists, skipping creation"
fi

echo

# Install Elixir dependencies
print_status "Installing Elixir dependencies..."
if mix deps.get; then
    print_status "Elixir dependencies installed successfully"
else
    print_error "Failed to install Elixir dependencies"
    exit 1
fi

echo

# Install Node.js dependencies
print_status "Installing Node.js dependencies..."
if cd assets && npm install && cd ..; then
    print_status "Node.js dependencies installed successfully"
else
    print_error "Failed to install Node.js dependencies"
    exit 1
fi

echo

# Setup database (if PostgreSQL is available)
if command -v psql &> /dev/null; then
    print_status "PostgreSQL detected. Setting up database..."
    read -p "Do you want to set up the database now? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if mix ecto.setup; then
            print_status "Database setup completed"
        else
            print_warning "Database setup failed. You may need to configure PostgreSQL first."
        fi
    else
        print_status "Skipping database setup"
    fi
else
    print_warning "PostgreSQL not detected. You'll need to install and configure it separately."
fi

echo

# Make scripts executable
print_status "Making scripts executable..."
chmod +x deploy.sh
chmod +x setup.sh

echo

print_status "Setup completed successfully!"
echo
echo -e "${BLUE}Next steps:${NC}"
echo "1. Review and update the .env file with your configuration"
echo "2. Ensure PostgreSQL is running and configured"
echo "3. Run 'mix phx.server' to start the development server"
echo "4. Or run './deploy.sh' to build and deploy with Docker"
echo
echo -e "${BLUE}For deployment:${NC}"
echo "1. Review DEPLOYMENT_GUIDE.md for detailed instructions"
echo "2. Configure GitLab CI/CD variables"
echo "3. Set up your staging and production servers"
echo
print_status "Happy coding! 🚀"

defmodule App.LicenseReviewsTest do
  use App.DataCase

  alias App.LicenseReviews

  describe "license_reviews" do
    alias App.LicenseReviews.LicenseReview

    import App.LicenseReviewsFixtures

    @invalid_attrs %{attention_field: nil, attention_status: nil, reason: nil}

    test "list_license_reviews/0 returns all license_reviews" do
      license_review = license_review_fixture()
      assert LicenseReviews.list_license_reviews() == [license_review]
    end

    test "get_license_review!/1 returns the license_review with given id" do
      license_review = license_review_fixture()
      assert LicenseReviews.get_license_review!(license_review.id) == license_review
    end

    test "create_license_review/1 with valid data creates a license_review" do
      valid_attrs = %{
        attention_field: "some attention_field",
        attention_status: "some attention_status",
        reason: "some reason"
      }

      assert {:ok, %LicenseReview{} = license_review} =
               LicenseReviews.create_license_review(valid_attrs)

      assert license_review.attention_field == "some attention_field"
      assert license_review.attention_status == "some attention_status"
      assert license_review.reason == "some reason"
    end

    test "create_license_review/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = LicenseReviews.create_license_review(@invalid_attrs)
    end

    test "update_license_review/2 with valid data updates the license_review" do
      license_review = license_review_fixture()

      update_attrs = %{
        attention_field: "some updated attention_field",
        attention_status: "some updated attention_status",
        reason: "some updated reason"
      }

      assert {:ok, %LicenseReview{} = license_review} =
               LicenseReviews.update_license_review(license_review, update_attrs)

      assert license_review.attention_field == "some updated attention_field"
      assert license_review.attention_status == "some updated attention_status"
      assert license_review.reason == "some updated reason"
    end

    test "update_license_review/2 with invalid data returns error changeset" do
      license_review = license_review_fixture()

      assert {:error, %Ecto.Changeset{}} =
               LicenseReviews.update_license_review(license_review, @invalid_attrs)

      assert license_review == LicenseReviews.get_license_review!(license_review.id)
    end

    test "delete_license_review/1 deletes the license_review" do
      license_review = license_review_fixture()
      assert {:ok, %LicenseReview{}} = LicenseReviews.delete_license_review(license_review)

      assert_raise Ecto.NoResultsError, fn ->
        LicenseReviews.get_license_review!(license_review.id)
      end
    end

    test "change_license_review/1 returns a license_review changeset" do
      license_review = license_review_fixture()
      assert %Ecto.Changeset{} = LicenseReviews.change_license_review(license_review)
    end
  end
end

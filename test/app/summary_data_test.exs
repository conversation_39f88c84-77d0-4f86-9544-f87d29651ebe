defmodule App.SummaryDataTest do
  use App.DataCase

  alias App.SummaryData

  describe "tbl_summarised_data" do
    alias App.SummaryData.SummarisedData

    import App.SummaryDataFixtures

    @invalid_attrs %{license_field_id: nil, license_id: nil, status: nil}

    test "list_tbl_summarised_data/0 returns all tbl_summarised_data" do
      summarised_data = summarised_data_fixture()
      assert SummaryData.list_tbl_summarised_data() == [summarised_data]
    end

    test "get_summarised_data!/1 returns the summarised_data with given id" do
      summarised_data = summarised_data_fixture()
      assert SummaryData.get_summarised_data!(summarised_data.id) == summarised_data
    end

    test "create_summarised_data/1 with valid data creates a summarised_data" do
      valid_attrs = %{license_field_id: 42, license_id: 42, status: 42}

      assert {:ok, %SummarisedData{} = summarised_data} =
               SummaryData.create_summarised_data(valid_attrs)

      assert summarised_data.license_field_id == 42
      assert summarised_data.license_id == 42
      assert summarised_data.status == 42
    end

    test "create_summarised_data/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = SummaryData.create_summarised_data(@invalid_attrs)
    end

    test "update_summarised_data/2 with valid data updates the summarised_data" do
      summarised_data = summarised_data_fixture()
      update_attrs = %{license_field_id: 43, license_id: 43, status: 43}

      assert {:ok, %SummarisedData{} = summarised_data} =
               SummaryData.update_summarised_data(summarised_data, update_attrs)

      assert summarised_data.license_field_id == 43
      assert summarised_data.license_id == 43
      assert summarised_data.status == 43
    end

    test "update_summarised_data/2 with invalid data returns error changeset" do
      summarised_data = summarised_data_fixture()

      assert {:error, %Ecto.Changeset{}} =
               SummaryData.update_summarised_data(summarised_data, @invalid_attrs)

      assert summarised_data == SummaryData.get_summarised_data!(summarised_data.id)
    end

    test "delete_summarised_data/1 deletes the summarised_data" do
      summarised_data = summarised_data_fixture()
      assert {:ok, %SummarisedData{}} = SummaryData.delete_summarised_data(summarised_data)

      assert_raise Ecto.NoResultsError, fn ->
        SummaryData.get_summarised_data!(summarised_data.id)
      end
    end

    test "change_summarised_data/1 returns a summarised_data changeset" do
      summarised_data = summarised_data_fixture()
      assert %Ecto.Changeset{} = SummaryData.change_summarised_data(summarised_data)
    end
  end
end

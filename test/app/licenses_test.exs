defmodule App.LicensesTest do
  use App.DataCase

  alias App.Licenses

  describe "licenses" do
    alias App.Licenses.License

    import App.LicensesFixtures

    @invalid_attrs %{form_number: nil, name: nil, status: nil}

    test "list_licenses/0 returns all licenses" do
      license = license_fixture()
      assert Licenses.list_licenses() == [license]
    end

    test "get_license!/1 returns the license with given id" do
      license = license_fixture()
      assert Licenses.get_license!(license.id) == license
    end

    test "create_license/1 with valid data creates a license" do
      valid_attrs = %{form_number: 42, name: "some name", status: 42}

      assert {:ok, %License{} = license} = Licenses.create_license(valid_attrs)
      assert license.form_number == 42
      assert license.name == "some name"
      assert license.status == 42
    end

    test "create_license/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Licenses.create_license(@invalid_attrs)
    end

    test "update_license/2 with valid data updates the license" do
      license = license_fixture()
      update_attrs = %{form_number: 43, name: "some updated name", status: 43}

      assert {:ok, %License{} = license} = Licenses.update_license(license, update_attrs)
      assert license.form_number == 43
      assert license.name == "some updated name"
      assert license.status == 43
    end

    test "update_license/2 with invalid data returns error changeset" do
      license = license_fixture()
      assert {:error, %Ecto.Changeset{}} = Licenses.update_license(license, @invalid_attrs)
      assert license == Licenses.get_license!(license.id)
    end

    test "delete_license/1 deletes the license" do
      license = license_fixture()
      assert {:ok, %License{}} = Licenses.delete_license(license)
      assert_raise Ecto.NoResultsError, fn -> Licenses.get_license!(license.id) end
    end

    test "change_license/1 returns a license changeset" do
      license = license_fixture()
      assert %Ecto.Changeset{} = Licenses.change_license(license)
    end
  end
end

defmodule App.MixProject do
  use Mix.Project

  def project do
    [
      app: :app,
      version: "0.1.0",
      elixir: "~> 1.14",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps(),
      releases: releases()
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {App.Application, []},
      extra_applications: [
        :logger,
        :runtime_tools,
        :pdf_generator,
        :os_mon
      ]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    [
      # {:bcrypt_elixir, "~> 0.12"},
      {:pbkdf2_elixir, "~> 2.1"},
      {:phoenix, "~> 1.7.11"},
      {:phoenix_ecto, "~> 4.4"},
      {:ecto_sql, "~> 3.10"},
      {:postgrex, ">= 0.0.0"},
      {:phoenix_html, "~> 4.0"},
      {:phoenix_live_reload, "~> 1.2", only: :dev},
      {:phoenix_live_view, "~> 0.20.2"},
      {:floki, ">= 0.30.0", only: :test},
      {:phoenix_live_dashboard, "~> 0.8.3"},
      {:esbuild, "~> 0.8", runtime: Mix.env() == :dev},
      {:tailwind, "~> 0.2", runtime: Mix.env() == :dev},
      {:heroicons,
       github: "tailwindlabs/heroicons",
       tag: "v2.1.1",
       sparse: "optimized",
       app: false,
       compile: false,
       depth: 1},
      {:swoosh, "~> 1.5"},
      {:uuid, "~> 1.1"},
      {:finch, "~> 0.13"},
      {:telemetry_metrics, "~> 0.6"},
      {:telemetry_poller, "~> 1.0"},
      {:gettext, "~> 0.20"},
      {:jason, "~> 1.2"},
      {:dns_cluster, "~> 0.1.1"},
      {:joken, "~> 2.0"},
      {:bandit, "~> 1.2"},
      {:cachex, "~> 3.4"},
      {:ex_phone_number, "~> 0.1"},
      {:phone, "~> 0.5.2"},
      {:httpoison, "~> 1.6"},
      {:elixlsx, "~> 0.4.2"},
      {:xlsxir, "~> 1.6.4"},
      {:csv, "~> 2.3"},
      {:quantum, "~> 3.0"},
      {:browser, "~> 0.4.4"},
      {:skooma, "~> 0.2.0"},
      {:bbmustache, "1.6.0"},
      {:pdf_generator, ">=0.6.0"},
      {:scrivener_ecto, "~> 2.7.0", override: true},
      {:number_f, "~> 0.1.6"},
      {:timex, "~> 3.5"},
      {:gen_smtp, "~> 1.1"},
      {:sms_part_counter, "~> 0.1.6"},
      {:countries, "~> 1.6"},
      {:live_select, "~> 1.5.4"},
      {:credo, "~> 1.7", only: [:dev, :test], runtime: false}
    ]
  end

  def releases() do
    [
      sec_client_rel: [
        version: "0.1.8",
        include_executables_for: [:unix],
        applications: [
          runtime_tools: :permanent
        ]
      ]
    ]
  end

  # Aliases are shortcuts or tasks specific to the current project.
  # For example, to install project dependencies and perform other setup tasks, run:
  #
  #     $ mix setup
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      npm: ["cmd npm install --prefix assets"],
      setup: ["deps.get", "ecto.setup", "assets.setup", "assets.build", "npm"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.create --quiet", "ecto.migrate --quiet", "test"],
      "assets.setup": ["tailwind.install --if-missing", "esbuild.install --if-missing"],
      "assets.build": ["tailwind app", "esbuild app"],
      "assets.deploy": [
        "tailwind app --minify",
        "esbuild app --minify",
        "phx.digest"
      ]
    ]
  end
end

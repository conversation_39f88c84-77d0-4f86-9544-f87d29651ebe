
17:12:06.696 [info] Loading 146 CA(s) from :otp store

17:12:06.721 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

17:12:09.418 [info] Loading 146 CA(s) from :otp store

17:12:09.474 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

17:12:12.751 [info] Loading 146 CA(s) from :otp store

17:12:12.809 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

17:12:14.995 [info] Loading 146 CA(s) from :otp store

17:12:15.018 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

17:12:17.049 [info] Loading 146 CA(s) from :otp store

17:12:17.099 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

10:00:11.082 [info] Loading 146 CA(s) from :otp store

10:00:11.118 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

10:00:15.390 [info] Loading 146 CA(s) from :otp store

10:00:15.481 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

10:00:17.931 [info] Loading 146 CA(s) from :otp store

10:00:18.015 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

10:00:20.100 [info] Loading 146 CA(s) from :otp store

10:00:20.134 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

10:00:23.528 [info] Loading 146 CA(s) from :otp store

10:00:23.576 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

12:27:29.429 [info] Loading 146 CA(s) from :otp store

12:27:29.477 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

12:27:31.796 [info] Loading 146 CA(s) from :otp store

12:27:31.864 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

12:27:34.303 [info] Loading 146 CA(s) from :otp store

12:27:34.372 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

12:27:36.833 [info] Loading 146 CA(s) from :otp store

12:27:36.888 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

12:27:39.064 [info] Loading 146 CA(s) from :otp store

12:27:39.106 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

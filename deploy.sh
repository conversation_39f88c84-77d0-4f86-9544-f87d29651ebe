#!/bin/bash

# SEC Client Deployment Script
# This script builds and deploys the SEC Client application using Docker

set -e  # Exit on any error

# Configuration
IMAGE_NAME="sec_client_app"
CONTAINER_NAME="sec_client_app"
DEFAULT_PORT=8500
CONTAINER_PORT=80

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Get port number from user or use default
read -p "Enter the port number (default is $DEFAULT_PORT): " PORT
HOST_PORT=${PORT:-$DEFAULT_PORT}

print_status "Using port $HOST_PORT for the application."

# Validate port number
if ! [[ "$HOST_PORT" =~ ^[0-9]+$ ]] || [ "$HOST_PORT" -lt 1 ] || [ "$HOST_PORT" -gt 65535 ]; then
    print_error "Invalid port number. Please enter a number between 1 and 65535."
    exit 1
fi

# Check if port is already in use
if lsof -Pi :$HOST_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_warning "Port $HOST_PORT is already in use."
    read -p "Do you want to continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled."
        exit 0
    fi
fi

# Build the Docker image
print_status "Building the Docker image..."
if docker build -t $IMAGE_NAME .; then
    print_status "Docker image built successfully."
else
    print_error "Failed to build Docker image."
    exit 1
fi

# Stop and remove existing container if it exists
EXISTING_CONTAINER=$(docker ps -aq -f name=$CONTAINER_NAME)

if [ -n "$EXISTING_CONTAINER" ]; then
    print_status "Found existing container. Stopping and removing..."
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
    print_status "Existing container removed."
fi

# Set up environment variables
print_status "Setting up environment variables..."

# Check if .env file exists
if [ -f ".env" ]; then
    print_status "Loading environment variables from .env file..."
    set -a
    source .env
    set +a
else
    print_warning "No .env file found. Using default environment variables."
fi

# Set default values if not provided
export MIX_ENV=${MIX_ENV:-prod}
export RELEASE_NAME=${RELEASE_NAME:-sec_client_rel}
export RUN_MIGRATIONS=${RUN_MIGRATIONS:-true}

# Run the Docker container
print_status "Starting the Docker container on port $HOST_PORT..."

docker run -d \
  --name $CONTAINER_NAME \
  -p $HOST_PORT:$CONTAINER_PORT \
  -e MIX_ENV="$MIX_ENV" \
  -e RELEASE_NAME="$RELEASE_NAME" \
  -e RUN_MIGRATIONS="$RUN_MIGRATIONS" \
  ${DATABASE_URL:+-e DATABASE_URL="$DATABASE_URL"} \
  ${SECRET_KEY_BASE:+-e SECRET_KEY_BASE="$SECRET_KEY_BASE"} \
  ${PHX_HOST:+-e PHX_HOST="$PHX_HOST"} \
  --restart always \
  $IMAGE_NAME

# Verify the container is running
sleep 5
if docker ps -f name=$CONTAINER_NAME --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -q $CONTAINER_NAME; then
    print_status "Deployment successful!"
    print_status "Container is running on port $HOST_PORT"
    print_status "Application should be accessible at: http://localhost:$HOST_PORT"
    
    # Show container logs
    print_status "Recent container logs:"
    docker logs --tail 20 $CONTAINER_NAME
else
    print_error "Container failed to start. Check the logs:"
    docker logs $CONTAINER_NAME
    exit 1
fi

# Clean up unused Docker images
print_status "Cleaning up unused Docker images..."
docker image prune -f > /dev/null 2>&1 || true

print_status "Deployment complete!"

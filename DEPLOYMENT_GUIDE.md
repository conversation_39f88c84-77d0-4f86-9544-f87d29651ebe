# SEC Client Deployment Guide

This guide explains how to set up and deploy the SEC Client application using GitLab CI/CD pipelines.

## Prerequisites

1. **GitLab Runner**: Ensure you have GitLab Runners configured with Docker support
2. **Docker Registry**: Access to GitLab Container Registry or another Docker registry
3. **Target Servers**: Staging and production servers with Docker installed
4. **SSH Access**: SSH keys configured for deployment servers

## GitLab CI/CD Variables

Configure the following variables in your GitLab project settings (`Settings > CI/CD > Variables`):

### Registry Variables (Auto-configured by GitLab)
- `CI_REGISTRY_IMAGE`: GitLab container registry image path
- `CI_REGISTRY_USER`: GitLab registry username
- `CI_REGISTRY_PASSWORD`: GitLab registry password

### Staging Environment Variables
- `STAGING_HOST`: Staging server hostname/IP
- `STAGING_USER`: SSH username for staging server
- `STAGING_SSH_PRIVATE_KEY`: SSH private key for staging deployment (Type: File)
- `DATABASE_URL`: Database connection string for staging
- `SECRET_KEY_BASE`: Phoenix secret key base for staging
- `PHX_HOST`: Phoenix host for staging (e.g., staging.yourdomain.com)

### Production Environment Variables
- `PRODUCTION_HOST`: Production server hostname/IP
- `PRODUCTION_USER`: SSH username for production server
- `PRODUCTION_SSH_PRIVATE_KEY`: SSH private key for production deployment (Type: File)
- `DATABASE_URL`: Database connection string for production
- `SECRET_KEY_BASE`: Phoenix secret key base for production
- `PHX_HOST`: Phoenix host for production (e.g., yourdomain.com)

## Pipeline Stages

### 1. Format Stage
- Runs `mix format --check-formatted`
- Ensures code follows Elixir formatting standards
- Runs on all pushes and merge requests

### 2. Compile Stage
- Runs `mix compile --warnings-as-errors`
- Ensures code compiles without warnings
- Runs on all pushes and merge requests

### 3. Test Stage
- Runs `mix test`
- Executes the test suite with PostgreSQL service
- Runs on all pushes and merge requests

### 4. Build Stage
- Builds Docker image using the project Dockerfile
- Pushes image to GitLab Container Registry
- Tags with commit SHA and 'latest'
- Runs on main/master, develop branches, and merge requests

### 5. Deploy Stage
- **Staging**: Deploys to staging environment from `develop` branch (manual trigger)
- **Production**: Deploys to production environment from `main`/`master` branch (manual trigger)

## Server Setup

### Prerequisites on Target Servers

1. **Install Docker**:
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install docker.io docker-compose
   sudo systemctl enable docker
   sudo systemctl start docker
   
   # Add user to docker group
   sudo usermod -aG docker $USER
   ```

2. **Create Application Directory**:
   ```bash
   sudo mkdir -p /opt/sec_client
   sudo chown $USER:$USER /opt/sec_client
   ```

3. **Configure SSH Access**:
   - Add your GitLab CI/CD public key to `~/.ssh/authorized_keys`
   - Ensure SSH service is running

### Environment Variables on Servers

Create environment files on your servers or use a secrets management system:

```bash
# /opt/sec_client/.env
DATABASE_URL=ecto://username:password@localhost/sec_client_prod
SECRET_KEY_BASE=your_very_long_secret_key_base_here
PHX_HOST=yourdomain.com
MIX_ENV=prod
RELEASE_NAME=sec_client_rel
RUN_MIGRATIONS=true
```

## Local Development Deployment

For local testing, use the improved `deploy.sh` script:

```bash
# Make script executable
chmod +x deploy.sh

# Run deployment
./deploy.sh
```

The script will:
1. Prompt for port number (default: 8500)
2. Build the Docker image
3. Stop any existing container
4. Start new container with proper configuration
5. Show deployment status and logs

### Local Environment Setup

Create a `.env` file in your project root:

```bash
# .env (for local development)
DATABASE_URL=ecto://postgres:postgres@localhost/sec_client_dev
SECRET_KEY_BASE=your_local_secret_key_base
PHX_HOST=localhost
MIX_ENV=prod
RELEASE_NAME=sec_client_rel
RUN_MIGRATIONS=true
```

## Troubleshooting

### Pipeline Issues

1. **Build Failures**:
   - Check Dockerfile syntax
   - Verify all dependencies are available
   - Check Docker registry permissions

2. **Test Failures**:
   - Ensure PostgreSQL service is properly configured
   - Check database connection settings
   - Verify test database setup

3. **Deployment Failures**:
   - Verify SSH connectivity to target servers
   - Check environment variables are set correctly
   - Ensure Docker is running on target servers
   - Verify registry access from target servers

### Container Issues

1. **Container Won't Start**:
   ```bash
   # Check container logs
   docker logs sec_client_app
   
   # Check container status
   docker ps -a
   ```

2. **Database Connection Issues**:
   - Verify `DATABASE_URL` is correct
   - Ensure database server is accessible
   - Check firewall rules

3. **Port Conflicts**:
   - Use `lsof -i :8500` to check port usage
   - Choose different port or stop conflicting service

### Performance Optimization

1. **Memory Settings**:
   - Adjust Erlang memory flags in Dockerfile if needed
   - Monitor container memory usage

2. **Database Performance**:
   - Ensure database migrations run successfully
   - Monitor database connection pool

## Security Considerations

1. **Environment Variables**:
   - Never commit sensitive data to repository
   - Use GitLab CI/CD variables for secrets
   - Rotate secrets regularly

2. **SSH Keys**:
   - Use dedicated deployment keys
   - Restrict key permissions
   - Regularly rotate keys

3. **Network Security**:
   - Use HTTPS in production
   - Configure proper firewall rules
   - Consider using VPN for server access

## Monitoring and Maintenance

1. **Log Monitoring**:
   ```bash
   # View application logs
   docker logs -f sec_client_app
   
   # View system logs
   journalctl -u docker
   ```

2. **Health Checks**:
   - Monitor application endpoints
   - Set up alerts for service failures
   - Regular backup procedures

3. **Updates**:
   - Regular security updates on servers
   - Keep Docker images updated
   - Monitor for Elixir/Phoenix security advisories

## Support

For issues with deployment:
1. Check this guide first
2. Review GitLab CI/CD logs
3. Check server logs and Docker status
4. Contact the development team with specific error messages
